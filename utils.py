from socket import timeout
# from subprocess import HIGH_PRIORITY_CLASS
import time
import json
import hashlib
import re, re, os
from turtle import update
import requests
from urllib.request import unquote
import logging
from datetime import datetime
from elasticsearch import Elasticsearch
    



def url_join(url: str, query_dict: dict):
    ls = []
    if url and query_dict:
        for k, v in query_dict.items():
            ls.append(k + '=' + str(v))
        query_str = "&".join(ls)
        request_url = url + "?" + query_str
        request_url = request_url.replace('{', '%7B')
        request_url = request_url.replace('}', "%7D")
        return request_url
    else:
        print('input params error')
        return ""
    

def gen_signature(url="/v1/forum/getChildForums", param={"fid": 4, "_t": int(time.time() * 1000)}, appcode="PC42ce3bfa4980a9"):
    params_str = url + json.dumps(param, separators=(',', ':')) + hashlib.md5(appcode.encode('utf-8')).hexdigest()[:16]
    return hashlib.md5(params_str.encode('utf-8')).hexdigest()

def remove_non_chinese_end(s):
    match = re.search(r"[\u4e00-\u9fa5]$", s)
    if match:
        return s
    else:
        while s and not re.match(r'[\u4e00-\u9fa5]$', s[-1]):
            s = s[:-1]
        return s


def join_text(text_list):
    text = ""
    for t in text_list:
        t = t.replace('\n','')
        if not t or t[0] == '[': # 剔除引用的方括号文本
            continue
        text += t
    pattern = re.compile('^\S{1}\s.*')
    if re.match(pattern, text):
        lis_text = list(text)
        lis_text.pop(1)
        text = "".join(lis_text)
    return text


def remove_parentheses(text:str):
    if not text:
        return text
    ret = re.sub("（.*?）", "", text)
    ret = re.sub("（.*?\)", "", ret)
    ret = re.sub("\(.*?）", "", ret)
    ret = re.sub("\[(.*?)\]","", ret)
    ret = re.sub(r"（.*?）", "", ret)
    ##
    ret = re.sub(".*?部长", "", ret)
    ret = re.sub(".*?主任", "", ret)
    ret = re.sub(".*?管理部", "", ret)
    ret = re.sub(".*?行长", "", ret)
    ret = re.sub(".*?审计长", "", ret)
    ret = re.sub(".*?主席：","",ret)
    ret = re.sub(".*?副主席：", "", ret)
    ret = re.sub(" ", "", ret)
    ret = re.sub("。$","", ret)
    return re.sub("\(.*?\)", "", ret)


def get_href(strs, name):
    pattern = r'\shref\S+[\s]?\S+[\s]?'+name.strip()#r'.+?href=\"(.+?)\.*?".*?\>'+ name.strip()+r'<.*a>'
    #r'\shref\S+[\s]?\S+[\s]?'+name.strip()
    res = re.search(pattern, strs)
    # print("get_href----", res)
    if res:
        if " " in res[0][1:]:
            temp_str = res[0].split()
            href = temp_str[0][6:-1]
            href = href[href.find("/item"):]
            return href
        else:
            temp_str = res[0].split('>')
            href = temp_str[0][6:-1]
            href = href[href.find("/item"):]
            return href
    return None


def get_workdir():
    date = time.strftime('%Y%m%d',time.localtime(time.time()))
    workdir = os.path.abspath(os.path.join(os.path.dirname(__file__), f'runtime/{date}'))
    os.system(f'mkdir -p {workdir}')
    return workdir


def get_search_text_from_url(url):
    url = unquote(url)
    text = url[url.find('item/')+len('item/'):]
    pos = text.find('/')
    if pos != -1:
        text = text[:pos]
    return text


def get_balance():
    url = 'https://webapi.jghttp.alicloudecs.com/index/index/get_my_balance?neek=51095&appkey=fc4bfc46b18ce74403573662c625fbd3'
    resp = requests.get(url)
    resp = resp.json()
    # print(resp)
    time.sleep(2)
    return float(resp['data']['balance'])

def get_ips(n, t=3):
    """
    t: ip时效
        3 - 0~3min  0.005￥/ip
        5 - 0~5min  0.01￥/ip
        10 - 0~10min  0.02￥/ip
        15 - 0~15min 0.03￥/ip
    """
    get_ip = "http://api.tianqiip.com/getip?secret=sd5kt44eb54q0cp3&num={num}&type=json&port=1&time={time}&mr=1&sign=902b7418b60dd09b741b0d45475768d5"
    url = get_ip.format(num=n, time=t)
    for _ in range(5):
        try:
            resp = requests.get(url)
            res = json.loads(resp.text)
            assert isinstance(res, dict) and res['code'] == 1000
            ips = [f"{ip['ip']}:{ip['port']}" for ip in res['data']]
            print(f"获取ip:\n{ips}")
            return ips
        except:
            time.sleep(1)
    raise Exception('获取ip失败')

def write_to_txt(es_con, index_name, save_txt_name="all_url_list.txt"):
    from ops.esops import get_date_from_ES
    all_leader_info = get_date_from_ES(escon=es_con, index_name=index_name)
    runtime_dir = get_workdir()
    all_url_list = []
    with open(os.path.join(runtime_dir, save_txt_name), "w", encoding="utf-8") as fp:
        for item in all_leader_info:
            url = item["_source"].get("url")
            if url:
                url = "https://baike.baidu.com/{}".format(url) if not url.startswith("https") else url           
                fp.write(url+"\n")

def write_to_json(es_con, fallen_index_name, save_json_name="fallen_leader_url.json"):
    from ops.esops import get_date_from_ES
    all_leader_info = get_date_from_ES(escon=es_con, index_name=fallen_index_name)
    runtime_dir = get_workdir()
    fallen_list = []

    with open(os.path.join(runtime_dir, save_json_name), "w", encoding="utf-8") as fp:
        for item in all_leader_info:
            _name = item["_source"]["name"]
            _position = item["_source"]["position"]
            _url = item["_source"]["url"]
            _url = re.sub("/?from.*?", "", _url)
            info = {"name": _name, "position": _position, "url":_url}
            if info not in fallen_list:
                fallen_list.append(info)
        json_data = json.dumps(fallen_list, ensure_ascii=False, indent=2)
        fp.write(json_data)

def is_chinese(char): 
    """"判断一个字符是否是汉字"""
    return bool(re.match(r"[\u4e00-\u9fff]", char))

def trim_non_chinese_edges(text):
    """删除字符串开头和结尾的非汉字字符"""
    start=0
    if start<len(text) and not is_chinese(text[start]):
        start+=1
    end=len(text)
    if end>start and not is_chinese(text[end-1]):
        end -= 1
    return text[start:end]

def validate_and_filter_data():
    """根据invalidate_name_list.txt的规则，过滤index中的无效数据"""
    # 这是一个独立的函数，不需要输入，直接进行验证
    logging.basicConfig(filename=r"/home/<USER>/leader-info/UpdateLeaderInfo/runtime/{}/logs/name_validate_res.log".format(datetime.now().strftime("%Y%m%d")), filemode="a+", level=logging.INFO, 
                        format="%(levelname)s - %(message)s",
                        datefmt="%Y-%m-%d %H:%M:%S")
    

    txt_path="/home/<USER>/leader-info/UpdateLeaderInfo/configs/invalidate_name_list.txt"
    with open(txt_path, "r", encoding='utf-8') as fp:
        lines = fp.readlines()
        invalidate_name_list = []
        for line in lines:
            if line:
                split_line = line.split(" ")
                if split_line:
                    invalidate_name_list.append(split_line[0])
    config_file = os.path.join(os.path.abspath(os.path.dirname(__file__)), "configs/es_config.json")
    with open(config_file, "r", encoding='utf-8') as config_fp:
        CONFIG_ES = json.load(config_fp)
        __index_name__ = CONFIG_ES["leader_info_index"]    #'leader-info-final'  # index新表，增加了leader_type字段                           

    es = Elasticsearch(CONFIG_ES['host'], basic_auth=(CONFIG_ES['user'], CONFIG_ES['passwd']), request_timeout=120, max_retries=10000, retry_on_timeout=True)
    query = {"match_all":{}}
    response = es.search(index=CONFIG_ES["leader_info_index"], query=query, size=10000)
    hits = response["hits"]["hits"]
    del_count=0
    update_count = 0
    for hit in hits:
        _id = hit.get("_id")
        name = hit.get("_source").get("cName")
        position = hit.get("_source").get("oPosition")
        trim_name = trim_non_chinese_edges(name)
        if trim_name!=name: #name第一个或者最后一个字符不是汉字，需要update数据
            position = re.sub("(按姓氏笔画为序)", "", position)
            update_body = hit.get("_source")
            update_body["cName"] = trim_name
            update_body["eName"] = trim_name
            update_body["oPosition"] = position
            update_body["update_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            message = r"Update: Index: {}, ID: {}, Msg: {}".format(__index_name__, _id, f"{name}->{trim_name}")
            logging.info(message)
            update_count +=1
            es.update(index=__index_name__, id=_id, body={"doc": update_body})
        else:
            if "（按姓氏笔画为序）" in position:
                trim_position = re.sub("（按姓氏笔画为序）", "", position)
                update_body=hit.get("_source")
                update_body["oPosition"], update_body["update_time"] = trim_position, datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                message = r"Update - Index: {}, ID: {}, Msg: {}".format(__index_name__, _id, f"{position}->{trim_position}")
                logging.info(message)
                update_count+=1
                es.update(index=__index_name__, id=_id, body={"doc":update_body})
            if name in invalidate_name_list:
                update_count+=1     
                message = r"Delete - Index: {}, ID: {}, Msg: {}".format(__index_name__, _id, f"{name} is invalid")
                logging.info(message)
                es.delete(index="leader-info-final", id=_id)
    logging.info("Delete count={}, Update count={}".format(del_count, update_count))

    # print(len(invalidate_name_list))
    return


# validate_and_filter_data()

if __name__ == "__main__":
    # text = "院长：陈希（中央党校（国家行政学院）校长（院长））"
    # res = remove_parentheses(text)
    # print(res)
    # import re

    # text = "张三（国际组织（国家行政学院）校长（院长））"
    # new_text = re.sub(r'（[^（）]*）', '', text)
    # res = remove_parentheses(new_text)
    # print(res)

    # name = new_text.strip()
    # print(name)
    get_ips(n=1)