import os, re, requests, random, json
from venv import logger
import traceback, os, sys
from lxml import etree
import logging

# basedir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
# print(basedir)
# sys.path.append(basedir)
from mytools import my_requests, get_orgs_href, get_leaders_data, get_province_leaders


if __name__=="__main__":
    logging.basicConfig(filename='ce_running.log', level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    urls = [{"url":"http://district.ce.cn/zt/rwk/index.shtml", "flag":"地方省市"},
            {"url":"http://www.ce.cn/ztpd/xwzt/rwk/index.shtml", "flag":"部委机构"}]
    with open("difang_leaders.json", "a", encoding="utf-8") as file:
        for item in urls:
            url, flag = item.get("url"), item.get("flag")
            if flag not in ["地方省市","部委机构"]:
                raise ValueError
            if flag not in "部委机构":
                continue
            res_list = get_orgs_href(url=url, flag=flag)
            for org_info in res_list:
                for org_name, org_url in org_info.items():
                    print("="*60)
                    print(f"{org_name}:{org_url}")
                    logging.info(f"{org_name}:{org_url}")
                    try:
                        page = my_requests(url=org_url)
                        if flag=="部委机构":
                            leaders = get_leaders_data(page, flag=flag)
                            json.dump({"orgnization":org_name, "leaders":leaders}, file, ensure_ascii=False, indent=2)
                        elif flag=="地方省市":
                            leaders = get_province_leaders(province=org_name, page=page)
                            json.dump({"province":org_name, "leaders":leaders}, file, ensure_ascii=False, indent=2)
                        logging.info(f"len(leaders)={len(leaders)}")
                        # logging.info(json.dumps(leaders, ensure_ascii=False))
                    except:
                        logging.error(traceback.format_exc())
                        print(traceback.format_exc())
        


    
    