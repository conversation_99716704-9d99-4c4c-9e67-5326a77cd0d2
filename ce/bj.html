
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312" />

	
<meta name="Keywords" content="地方领导;地方政要;省长;省委书记;市长;市委书记;省委;市委;省份;地方人物库;人物;资料 地方党政领导人物库首页"/>
<meta name="Description" content="中国经济网地方人物库频道包括全国31个省区市及港澳台的人事任免信息，每天播报各地方领导的最新动态。" />
<meta name="filetype" content="0">
<meta name="publishedtype" content="1">
<meta name="pagetype" content="2">
<meta name="catalogs" content="xin_30197">

<title>北京党政领导人物库_中国经济网</title>
<script type="text/javascript">
//手机跳转
var browser = {
    versions: function() {
        var u = navigator.userAgent;
        var app = navigator.appVersion;
        return {
            trident: u.indexOf('Trident') > -1,
            presto: u.indexOf('Presto') > -1,
            pwebkit: u.indexOf('AppleWebKit') > -1,
            gecko: u.indexOf('Gecko') > -1 && u.indexOf('Khtml') == -1,
            mobile: !!u.match(/AppleWebKit.*Mobile.*/) || !!u.match(/AppleWebKit/),
            //是否为移动终端
            ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),
            //ios终端                
            android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1,
            //android终端或者uc浏览器                
            //iPhone: u.indexOf('iPhone') > -1 || u.indexOf('Mac') > -1, //是否为iPhone或者QQHD浏览器                
            iPhone: u.indexOf('iPhone') > -1,
            //是否为iPhone或者QQHD浏览器                
            iPad: u.indexOf('iPad') > -1,
            //是否iPad                
            webApp: u.indexOf('Safari') == -1
            //是否web应该程序，没有头部与底部 

        };

    } ()

}
var argStr = location.search;
if (browser.versions.iPhone || browser.versions.android) {
    //用手机浏览
    if (argStr == "")
    {
        if(location.href.indexOf("index.shtml")>0){
            var ref=location.href.split(".shtml");
            window.open(ref[0]+'_21098.shtml', '_self', '', 'true');
        }else{
            window.open(location.href+'index_21098.shtml', '_self', '', 'true');
        }
    }

}
</script>
</head>

<style type="text/css">

body, div, h1, h2, h3, h4, h5, h6, p, ul, ol, li, form, img, input, dl, dt, dd {margin: 0;padding: 0;}
body {font-size:12px;background:#fff; font:"宋体"; color:#333333;}
input {border:none;}
li {list-style:none;}
img {border:none;}
input{ border:none;}
.clear {clear:both;}
a {color:#333333;text-decoration:none;}
a:hover {color:#333333;text-decoration:underline;}




.tonglan1{ width:1000px; height:52px; margin:0 auto; border-bottom:3px #4972C4 solid;}
.tonglan1 h3{ width:64px; text-align:center;background:#4A73C5; height:22px; line-height:22px; margin:15px auto; color:#fff; font-size:14px; font-weight:normal; float:left;}
.tonglan1 span{ float:right; line-height:52px; display:block; margin-right:20px;color: #FF0000; font-size:14px; font-weight:bold;}
.tonglan1 span a{color: #FF0000;}


.main{ width:1000px; margin:10px auto;}

.left{ width:354px; float:left;}
.left h3{width:300px; height:22px; line-height:22px; background:url(http://district.ce.cn/images/difangrenimg_13_08.jpg) no-repeat;font-size:14px; font-weight:bold; padding-left:25px; margin:0px;}
.left1{ width:334px; height:192px; background:#ECF5FE; border:1px #D9E6EF solid; padding:20px 0px 0px 20px;}
.renwu{ width:145px; text-align:center; font-size:14px; font-weight:bold; float:left; margin-right:20px; display:inline;}
.renwu img{ width:118px; height:148px; margin:0 auto; border:1px #AAC0CB solid; margin-bottom:5px; }


.left2{ margin-top:12px; }
.ren2{ width:334px; background:#ECF5FE; border:1px #D9E6EF solid; padding:10px 10px 0 10px;  line-height:30px; font-size:14px;}


 h4{border:1px #D9E6EF solid; width:350px; height:22px; line-height:22px;padding-left:5px; font-size:14px; font-weight:bolder; background:#c1e3ff url(http://www.ce.cn/images/dfrwejy.jpg) 5px 5px no-repeat; text-indent:15px; margin:0px; }

.right h4{ margin-top:10px; width:617px;}

.right .se { display: inline; float:left;display:block; }



.right{ width:622px; float:right; }

.right ul{ line-height:27px; margin:0px;}
.list{border:1px #DDDDDD solid; padding:20px 20px 40px; font-size:14px;}

ul span{ float:right; *margin-top:-25px;}

.ye{ clear:both; width:500px; text-align:center; font-size:12px; font-weight:bold;}

.right h3{width:300px; height:22px; line-height:22px; background:url(http://district.ce.cn/images/difangrenimg_13_08.jpg) no-repeat;font-size:14px; font-weight:bold; padding-left:25px; margin:10px 0 0 ;}
.list1{border:1px solid #DDDDDD; width:622px; padding-bottom:12px;}



.city {background:#ECF5FE; border: 1px solid #D9E5F1;color: #999999;display: inline;float: left; font-size: 14px;height: 20px;
line-height: 20px; margin:12px 0px 0 9px;text-align: center;width: 50px;}


.bottom{ clear:both;text-align:center; margin-top:30px; line-height:28px;}
</style>

 
<body>



<!--头部-->
<link href="http://district.ce.cn/css/2015districttop.css" rel="stylesheet" type="text/css" charset="gbk">

<!--头部灰色导航条-->
<script type="text/javascript" src="http://www.ce.cn/inc/cealltop.js" charset="gbk"></script> 

<!--头部ad-->
<div class="cealltopad" >
     <!--头部ad-->

     <div class="lead">
      
     <a href="http://district.ce.cn/newarea/sddy/202402/03/t20240203_38892007.shtml" target="_blank"><IMG SRC="http://i.ce.cn/district/zt/rwk/rw/202402/03/W020240203482108610588.jpg" width=735 height="85" vspace=0 border=0 /></a>
 
</div>
	 <div class="riad">
     
     
 <a href="http://district.ce.cn/zt/zggx/" target="_blank"><IMG SRC="http://i.ce.cn/district/zt/rwk/rw/201709/25/W020220613607015603642.jpg" width=253 height="85" vspace=0 border=0 /></a>
  
 </div>
</div>



<!--导航-->

<!--导航-->
<div class="ceallnava">
  <div class="celogoa"><a href="http://www.ce.cn/" target="_blank"><img src="http://www.ce.cn/img4/cealllogo.jpg" border="0" /></a></div>
  <div class="celogob"><a href="http://www.ce.cn/xwzx/" target="_blank"><img src="http://www.ce.cn/xwzx/images/szshlogo.gif" border="0" /></a></div>
  <ul >
       
         <li class="nobor"><a href="http://district.ce.cn/zt/rwk/rw/rspd/" target="_blank" title="人事盘点">人事盘点</a></li>
       
 
       
      <li><a href="http://district.ce.cn/zt/rwk/rw/fanfu/" target="_blank" title="反腐·问责">反腐·问责</a></li>
       
      <li><a href="http://district.ce.cn/zt/rwk/rw/sbj/" target="_blank" title="省级任免">省级任免</a></li>
       
      <li><a href="http://www.ce.cn/ztpd/xwzt/zyzsjgrwk/" target="_blank" title="中直机构人物库">中直机构人物库</a></li>
       
      <li><a href="http://www.ce.cn/ztpd/xwzt/rwk/index.shtml" target="_blank" title="部委人物库">部委人物库</a></li>
       
      <li><a href="http://district.ce.cn/zt/rwk/index.shtml" target="_blank" title="地方党政领导人物库">地方党政领导人物库</a></li>
       
      <li><a href="http://district.ce.cn/newarea/sddy/" target="_blank" title="人事动态">人事动态</a></li>
       
   </ul>
</div>

<!--导航-->



<div class="tonglan1">
	<h3>北京</h3>
	<span><a href="http://district.ce.cn/zt/rwk/201112/09/t20111209_1267894.shtml" target="_blank">[纠错]</a></span>
</div>




<!--main-->
<div class="main">
    <!--left-->
	<div class="left">
    	<div class="left1">
        
        	<div class="renwu"><a href="http://district.ce.cn/newarea/sddy/202211/14/t20221114_38227089.shtml" target="_blank"><img src="http://i.ce.cn/district/zt/rwk/sf/bj/tp/202211/13/W020221113608862201555.png" alt="尹力.png"  width=118 height=148/></a><br/><a href="http://district.ce.cn/newarea/sddy/202211/14/t20221114_38227089.shtml" target="_blank">市委书记 尹力</a></div>
      
        	<div class="renwu"><a href="http://district.ce.cn/newarea/sddy/202301/20/t20230120_38358026.shtml" target="_blank"><img src="http://i.ce.cn/district/zt/rwk/sf/bj/tp/202210/28/W020221028670145418933.jpg" alt="未标题-2.jpg"  width=118 height=148/></a><br/><a href="http://district.ce.cn/newarea/sddy/202301/20/t20230120_38358026.shtml" target="_blank">市长 殷勇</a></div>
      
            
            <div class="clear"></div>
        </div>
        <div class="left2" >
        	<h3><a href="./sw/" target="_blank">市委领导</a></h3>
            <div class="ren2"><div class=TRS_Editor><p>
<table cellspacing="0" cellpadding="0" width="327" border="0">
    <tbody>
        <tr height="19">
            <td height="19" width="18"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><span lang="EN-US"><span lang="EN-US"><span lang="EN-US"><a target="_blank" href="http://district.ce.cn/newarea/sddy/202211/14/t20221114_38227089.shtml"><font style="font-size: 12pt" color="#0000ff"><b style="font-size: 12pt">尹力</b></font></a></span></span></span></td>
            <td width="244"><font style="font-size: 12pt">市委书记</font></td>
        </tr>
        <tr height="19">
            <td height="19"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><span lang="EN-US"><span lang="EN-US"><span lang="EN-US"><a target="_blank" href="http://district.ce.cn/newarea/sddy/202301/20/t20230120_38358026.shtml"><font style="font-size: 12pt" color="#0000ff"><b style="font-size: 12pt">殷勇</b></font></a></span></span></span></td>
            <td width="244"><font style="font-size: 12pt">市委副书记、市长</font></td>
        </tr>
        <tr height="19">
            <td height="19"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><span lang="EN-US"><span lang="EN-US"><span lang="EN-US"><a target="_blank" href="http://district.ce.cn/newarea/sddy/202212/29/t20221229_38315330.shtml"><font style="font-size: 12pt" color="#0000ff"><b style="font-size: 12pt">刘伟</b></font></a></span></span></span></td>
            <td width="244"><font style="font-size: 12pt">市委副书记</font></td>
        </tr>
        <tr height="19">
            <td height="19"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><a target="_blank" href="http://district.ce.cn/newarea/sddy/202311/26/t20231126_38806225.shtml"><font style="font-size: 12pt" color="#0000ff"><b style="font-size: 12pt">游钧</b></font></a></td>
            <td width="244"><font style="font-size: 12pt">市委常委、组织部部长</font></td>
        </tr>
        <tr height="19">
            <td height="19"><b><font style="font-size: 12pt">&nbsp;</font></b></td>
            <td width="65"><a target="_blank" href="http://district.ce.cn/newarea/sddy/202207/01/t20220701_37821904_5.shtml"><font style="font-size: 12pt" color="#0000ff"><b style="font-size: 12pt">陈健</b></font></a></td>
            <td width="244"><font style="font-size: 12pt">市委常委、市纪委书记，市监委主任</font></td>
        </tr>
        <tr height="19">
            <td height="19"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><a target="_blank" href="https://www.beijing.gov.cn/gongkai/sld/swld/swcw/202102/t20210210_2282290.html"><font style="font-size: 12pt" color="#0000ff"><b style="font-size: 12pt">夏林茂</b></font></a></td>
            <td width="244"><font style="font-size: 12pt"><span>市委常委、市政府党组副书记、副市长</span></font></td>
        </tr>
        <tr height="19">
            <td height="19" width="18"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><a target="_blank" href="http://district.ce.cn/newarea/sddy/202207/01/t20220701_37821904_8.shtml"><font style="font-size: 12pt" color="#0000ff"><b style="font-size: 12pt">付文化</b></font></a></td>
            <td width="259"><font style="font-size: 12pt"><span>市委常委，北京卫戍区少将司令员</span></font></td>
        </tr>
        <tr height="19">
            <td height="19" width="18"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><a target="_blank" href="http://district.ce.cn/newarea/sddy/202301/12/t20230112_38341976.shtml"><font style="font-size: 12pt" color="#0000ff"><b style="font-size: 12pt">杨晋柏</b></font></a></td>
            <td width="259"><font style="font-size: 12pt"><span>市委常委<span>、统战部部长</span></span></font></td>
        </tr>
        <tr height="19">
            <td height="19" width="18"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><a target="_blank" href="http://district.ce.cn/newarea/sddy/202207/01/t20220701_37821904_10.shtml"><font style="font-size: 12pt" color="#0000ff"><b style="font-size: 12pt">靳伟</b></font></a></td>
            <td width="259"><font style="font-size: 12pt"><span>市委常委，副市长</span></font></td>
        </tr>
        <tr height="19">
            <td height="19" width="18"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><a target="_blank" href="http://district.ce.cn/newarea/sddy/202207/02/t20220702_37825061.shtml"><font style="font-size: 12pt" color="#0000ff"><b style="font-size: 12pt">孙军民</b></font></a></td>
            <td width="259"><font style="font-size: 12pt"><span>市委常委、政法委书记</span></font></td>
        </tr>
        <tr height="19">
            <td height="19" width="18"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><a target="_blank" href="http://district.ce.cn/newarea/sddy/202303/02/t20230302_38421230.shtml"><font style="font-size: 12pt" color="#0000ff"><b style="font-size: 12pt">赵磊</b></font></a></td>
            <td width="259"><font style="font-size: 12pt"><span>市委常委、秘书长、市直机关工委书记、改革办主任</span></font></td>
        </tr>
        <tr height="19">
            <td height="19" width="18"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><a target="_blank" href="http://district.ce.cn/newarea/sddy/202401/08/t20240108_38857828.shtml"><font style="font-size: 12pt" color="#0000ff"><b style="font-size: 12pt">于英杰</b></font></a></td>
            <td width="259"><font style="font-size: 12pt"><span>市委常委、教育工委书记</span></font></td>
        </tr>
    </tbody>
</table>
</p></div> 
      
             </div>
            
        </div>
        <div class="left2" >
        	<h3><a href="./szf/" target="_blank">市政府领导</a></h3>
            <div class="ren2"><div class=TRS_Editor><p>
<table width="330" bordercolor="#000000" border="0" cellspacing="0" cellpadding="0">
    <tbody>
        <tr height="19">
            <td width="18" height="19"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65" data-spm-max-idx="1" data-auto-spmd-max-idx="0"><a target="_blank" data-spm-anchor-id="zm5092-001.0.0.1" href="http://district.ce.cn/newarea/sddy/202301/20/t20230120_38358026.shtml"><font color="#0000ff" style="font-size: 12pt"><b style="font-size: 12pt">殷勇</b></font></a></td>
            <td width="259"><font style="font-size: 12pt">市委副书记、市长</font></td>
        </tr>
        <tr height="19">
            <td width="18" height="19"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65" data-spm-max-idx="1" data-auto-spmd-max-idx="0"><a target="_blank" data-spm-anchor-id="zm5092-001.0.0.1" href="https://www.beijing.gov.cn/gongkai/sld/swld/swcw/202102/t20210210_2282290.html"><font color="#0000ff" style="font-size: 12pt"><b style="font-size: 12pt">夏林茂</b></font></a></td>
            <td width="259"><font style="font-size: 12pt">市委常委、市政府党组副书记、副市长</font></td>
        </tr>
        <tr height="19">
            <td width="18" height="19"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><a target="_blank" href="http://district.ce.cn/newarea/sddy/202310/12/t20231012_38746722.shtml"><font color="#0000ff" style="font-size: 12pt"><b style="font-size: 12pt">亓延军</b></font></a></td>
            <td width="259"><font style="font-size: 12pt">公安部党委副书记、分管日常工作的副部长(正部长级)，副市长，市公安局局长</font></td>
        </tr>
        <tr height="19">
            <td width="18" height="19"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><span><a target="_blank" href="http://district.ce.cn/newarea/sddy/202011/28/t20201128_36058987.shtml"><font color="#0000ff" style="font-size: 12pt"><b style="font-size: 12pt">靳伟</b></font></a></span></td>
            <td width="259"><font style="font-size: 12pt">副市长</font></td>
        </tr>
        <tr height="19">
            <td width="18" height="19"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><span><a target="_blank" href="http://district.ce.cn/newarea/sddy/202111/26/t20211126_37119133.shtml"><font color="#0000ff" style="font-size: 12pt"><b style="font-size: 12pt">谈绪祥</b></font></a></span></td>
            <td width="259"><font style="font-size: 12pt">副市长</font></td>
        </tr>
        <tr height="19">
            <td width="18" height="19"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><a target="_blank" href="http://district.ce.cn/newarea/sddy/202403/29/t20240329_38952620.shtml"><font color="#0000ff" style="font-size: 12pt"><strong style="font-size: 12pt">马骏</strong></font></a></td>
            <td width="259"><font style="font-size: 12pt">副市长</font></td>
        </tr>
        <tr height="19">
            <td width="18" height="19"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><span><a target="_blank" href="http://district.ce.cn/newarea/sddy/202301/20/t20230120_38358026_8.shtml"><font color="#0000ff" style="font-size: 12pt"><b style="font-size: 12pt">司马红</b></font></a></span></td>
            <td width="259"><font style="font-size: 12pt">副市长</font></td>
        </tr>
        <tr height="19">
            <td width="18" height="19"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><a target="_blank" href="http://district.ce.cn/newarea/sddy/202405/31/t20240531_39022389.shtml"><font color="#0000ff" style="font-size: 12pt"><strong style="font-size: 12pt">穆鹏</strong></font></a></td>
            <td width="259"><font style="font-size: 12pt">副市长</font></td>
        </tr>
        <tr height="19">
            <td width="18" height="19"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><span><a target="_blank" href="https://www.beijing.gov.cn/gongkai/sld/szfld/dzcy/202301/t20230119_2905964.html"><font color="#0000ff" style="font-size: 12pt"><b style="font-size: 12pt">隋振江</b></font></a></span></td>
            <td width="259"><font style="font-size: 12pt">市政府党组成员</font></td>
        </tr>
        <tr height="19">
            <td width="18" height="19"><font style="font-size: 12pt">&nbsp;</font></td>
            <td width="65"><a target="_blank" href="http://district.ce.cn/newarea/sddy/202405/31/t20240531_39022389.shtml"><font color="#0000ff" style="font-size: 12pt"><strong style="font-size: 12pt">曾劲</strong></font></a></td>
            <td width="259"><font style="font-size: 12pt">秘书长</font></td>
        </tr>
    </tbody>
</table>
</p></div> 
      
             </div>
        </div>
        
        
        
        
          
        
         <div class="left2" >
        	 
        	<h4><a href="http://district.ce.cn/zt/rwk/sf/bj/srd/201302/28/t20130228_770903.shtml" target="_blank">市人大常委会领导（点击进入）</a></h4>
         
        </div>
        
        
        
        
          
        
         <div class="left2">
        	 
        	<h4>
			<a href="http://district.ce.cn/zt/rwk/sf/bj/zx/201302/26/t20130226_770905.shtml" target="_blank">市政协领导名单+简历（点击进入）</a>
			</h4>
         
        </div>
        

    
	
	
	
	  

        
         <div class="left2" >
		 
        <h4>
			<a href="http://district.ce.cn/zt/rwk/sf/bj/qx/201206/07/t20120607_1269157.shtml" target="_blank">16个市辖区党政领导（<U>点击进入</U>）</a>
			</h4>
         
        </div>
       
 




    	
    </div>
    <!--left end-->
    
    
    

    
    
    
    <!--right-->
    <div class="right" >
    
    <div class="list">
     
   
     	<ul style="padding-bottom:22px; padding-left:0px;">
       <li>&middot;<a href="../../../../newarea/sddy/202405/31/t20240531_39022381.shtml" target="_blank">北京任免曾劲、穆鹏、李军会、徐会杰、刘圣国、马兰霞职务</a><span>2024/05/31  10:52</span></li>      
        <li>&middot;<a href="../../../../newarea/sddy/202405/31/t20240531_39022389.shtml" target="_blank">曾劲任北京市政府秘书长 穆鹏不再担任</a><span>2024/05/31  10:35</span></li>
         <li>&middot;<a href="../../../../newarea/sddy/202405/09/t20240509_38996602.shtml" target="_blank">北京对袁海鹏、高坚、刘圣国、王翔宇进行任前公示</a><span>2024/05/09  10:26</span></li>
          <li>&middot;<a href="../../../../newarea/sddy/202405/08/t20240508_38994980.shtml" target="_blank">于海波任北京市密云区代区长</a><span>2024/05/08  09:19</span></li>
           <li>&middot;<a href="../../../../newarea/sddy/202405/06/t20240506_38993310.shtml" target="_blank">底志欣任北京市房山区代区长 阳波辞去区长职务</a><span>2024/05/06  23:39</span></li>


        </ul>
        


   
        	
        
        
  
        
   
     	<ul style="padding-bottom:22px; padding-left:0px;">
       <li>&middot;<a href="../../../../newarea/sddy/202404/29/t20240429_38988313.shtml" target="_blank">“80后”阳波任北京房山区委书记 底志欣任区委副书记</a><span>2024/04/29  17:43</span></li>      
        <li>&middot;<a href="../../../../newarea/sddy/202404/29/t20240429_38988327.shtml" target="_blank">原北京铁路局副局长兼北京站党委书记、站长王丽娟接受审查调查</a><span>2024/04/29  17:37</span></li>
         <li>&middot;<a href="http://district.ce.cn/newarea/sddy/202404/28/t20240428_38986210.shtml" target="_blank">北京房山区委书记邹劲松调中央港澳工作办公室、国务院港澳事务办公室任职</a><span>2024/04/28  10:08</span></li>
          <li>&middot;<a href="../../../../newarea/sddy/202404/22/t20240422_38978446.shtml" target="_blank">北京市政府党组成员、副市长高朋接受中央纪委国家监委纪律审查和监察调查</a><span>2024/04/22  09:26</span></li>
           <li>&middot;<a href="../../../../newarea/sddy/202404/19/t20240419_38976481.shtml" target="_blank">北京公示3名干部拟任职务 "80后"阳波拟任区委书记</a><span>2024/04/19  14:36</span></li>


        </ul>
        


   
        	
        
        
  
        
   
     	<ul style="padding-bottom:22px; padding-left:0px;">
       <li>&middot;<a href="../../../../newarea/sddy/202404/19/t20240419_38976130.shtml" target="_blank">马斌任北京市石景山区副区长 申键辞去副区长职务</a><span>2024/04/19  09:24</span></li>      
        <li>&middot;<a href="http://district.ce.cn/newarea/sddy/202404/11/t20240411_38966479.shtml" target="_blank">莫高义任国务院新闻办公室主任 此前担任北京市委常委、宣传部部长</a><span>2024/04/11  13:13</span></li>
         <li>&middot;<a href="../../../../newarea/sddy/202404/01/t20240401_38954629.shtml" target="_blank">北京对马斌、于家明、刘敏华、宫吉成进行任前公示</a><span>2024/04/01  09:17</span></li>
          <li>&middot;<a href="../../../../newarea/sddy/202403/29/t20240329_38952620.shtml" target="_blank">马骏、穆鹏任北京市副市长</a><span>2024/03/29  13:15</span></li>
           <li>&middot;<a href="../../../../newarea/sddy/202403/27/t20240327_38949509.shtml" target="_blank">北京市人大常委会财政经济办公室主任闫立刚接受审查调查</a><span>2024/03/27  09:24</span></li>


        </ul>
        


   
        	
        
        
  
        
   
     	<ul style="padding-bottom:22px; padding-left:0px;">
       <li>&middot;<a href="../../../../newarea/sddy/202403/16/t20240316_38936612.shtml" target="_blank">中关村发展集团股份有限公司原党委书记、董事长赵长山被开除党籍和公职</a><span>2024/03/16  15:28</span></li>      
        <li>&middot;<a href="../../../../newarea/sddy/202403/15/t20240315_38935500.shtml" target="_blank">北京对徐振涛、马光耀、申键、张艳林、宋红伟等10人进行任前公示</a><span>2024/03/15  10:17</span></li>
         <li>&middot;<a href="../../../../newarea/sddy/202403/07/t20240307_38925200.shtml" target="_blank">胡海渊、唐超任北京市海淀区副区长</a><span>2024/03/07  14:46</span></li>
          <li>&middot;<a href="../../../../newarea/sddy/202403/02/t20240302_38918863.shtml" target="_blank">扈强任北京电影学院党委副书记、院长(图|简历)</a><span>2024/03/02  17:48</span></li>
           <li>&middot;<a href="../../../../newarea/sddy/202402/28/t20240228_38915242.shtml" target="_blank">陈龙波任北京市怀柔区副区长</a><span>2024/02/28  17:23</span></li>


        </ul>
        


   
        	
        
        
  
        
   
     	<ul style="padding-bottom:22px; padding-left:0px;">
       <li>&middot;<a href="../../../../newarea/sddy/202402/20/t20240220_38905212.shtml" target="_blank">张晓家任北京市西城区委常委、宣传部部长</a><span>2024/02/20  17:35</span></li>      
        <li>&middot;<a href="../../../../newarea/sddy/202402/19/t20240219_38904093.shtml" target="_blank">北京城市副中心行政办公区工程建设办公室原主任郑志勇被开除党籍</a><span>2024/02/19  16:48</span></li>
         <li>&middot;<a href="../../../../newarea/sddy/202402/08/t20240208_38898362.shtml" target="_blank">齐慧超任北京市海淀区委常委</a><span>2024/02/08  10:07</span></li>
          <li>&middot;<a href="../../../../newarea/sddy/202402/02/t20240202_38890841.shtml" target="_blank">北京公示李卫华、赵云龙、张奇、齐莹拟任职务</a><span>2024/02/02  09:13</span></li>
           <li>&middot;<a href="../../../../newarea/sddy/202401/26/t20240126_38882075.shtml" target="_blank">王建中当选北京市人大常委会秘书长</a><span>2024/01/26  09:30</span></li>


        </ul>
        


   
        	
        
        
  
        
   
     	<ul style="padding-bottom:22px; padding-left:0px;">
       <li>&middot;<a href="../../../../newarea/sddy/202401/15/t20240115_38866540.shtml" target="_blank">刘存志当选北京市朝阳区政协副主席</a><span>2024/01/15  15:23</span></li>      
        <li>&middot;<a href="../../../../newarea/sddy/202401/14/t20240114_38865079.shtml" target="_blank">纪亚辉、李世琪当选北京市丰台区政协副主席</a><span>2024/01/14  09:37</span></li>
         <li>&middot;<a href="../../../../newarea/sddy/202401/14/t20240114_38865071.shtml" target="_blank">连宇、陈燃当选北京市丰台区人大常委会副主任</a><span>2024/01/14  09:23</span></li>
          <li>&middot;<a href="../../../../newarea/sddy/202401/14/t20240114_38865067.shtml" target="_blank">沈立军、马玉兰当选北京市平谷区人大常委会副主任</a><span>2024/01/14  09:18</span></li>
           <li>&middot;<a href="../../../../newarea/sddy/202401/13/t20240113_38864751.shtml" target="_blank">徐航当选为北京市昌平区监察委员会主任</a><span>2024/01/13  15:24</span></li>


        </ul>
        


   
        	
        
        
  
           
     
     
           <div class="ye">
            <SCRIPT LANGUAGE="JavaScript">
























<!--
function createPageHTML(_nPageCount, _nCurrIndex, _sPageName, _sPageExt){
  if(_nPageCount == null || _nPageCount<=1){
		return;
	}

//首页和上一页以及第一页的代码
	document.write("<span style=\"font-size:12px;\">共(<font color=\"#FF0000\">");
	  document.write(_nPageCount);
	  document.write("<\/FONT>)页</span>&nbsp;");
	  //var fyn;
	 var nStep=5
	  var nCurrIndex = _nCurrIndex || 0;
	  	if(nCurrIndex < 0)return;
	
	  if(nCurrIndex > 0)
	  	document.write("<a href=\""+_sPageName+"."+_sPageExt+"\" style=font-size:12px color:#0000FF>首 页</a>&nbsp;");
	  else
	    document.write("<span style=\"font-size:12px;\">首 页</span>&nbsp;&nbsp;");
		
	  if(nCurrIndex > 0){
	      if(nCurrIndex == 1){
		  document.write("<a href=\""+_sPageName+"."+_sPageExt+"\">上一页</a>&nbsp;&nbsp;");
		  }else{document.write("<a href=\""+_sPageName+"_" + (nCurrIndex-1) + "."+_sPageExt+"\">上一页</a>&nbsp;&nbsp;");}
	    }
	  else{
	    document.write("<span style=\"font-size:12px;\">上一页</span>&nbsp;&nbsp;");
	    }
	    /*if(nCurrIndex == 0)
		document.write("1&nbsp;");
	else
		document.write("<a href=\""+_sPageName+"."+_sPageExt+"\">1</a>&nbsp;");*/

//首页和上一页以及第一页的代码




	if(_nPageCount<=4){//_nPageCount小于等于4的情况
	if(nCurrIndex == 0)
		document.write("1&nbsp;");
	else
		document.write("<a href=\""+_sPageName+"."+_sPageExt+"\">1</a>&nbsp;");
	for(var i=1; i<_nPageCount; i++){
			if(nCurrIndex == i)
				document.write((i+1) + "&nbsp;");
			else
				document.write("<a href=\""+_sPageName+"_" + i + "."+_sPageExt+"\">"+(i+1)+"</a>&nbsp;");
		}


	}else{//_nPageCount小于等于4的情况end
	//下面这个IF是判断是小于步长的一半的情况的;
	if(nCurrIndex < Math.ceil(nStep/2)){ 	
	        var xxxx=Math.ceil(nStep/2);
	        //alert("步长一半的数值:  "+xxxx+" nCurrIndex: "+nCurrIndex);
	    	for(i=nCurrIndex;i<nCurrIndex+nStep;i++){
	    		if(i == 0 && nCurrIndex==0){
	    		document.write("<span style=\"color:#FF0000; font-size:12px\">[1]</span>&nbsp;&nbsp;");
	    	  }
			    if(i == 0 && nCurrIndex!=0){
					document.write("<a href=\""+_sPageName+"."+_sPageExt+"\" style=font-size:12px color:#0000FF>[1]</a>&nbsp;&nbsp;");}
		  if(i > 0 && i <= _nPageCount){
			  if(i==nCurrIndex)
	    			document.write("[<span style=\"color:#FF0000; font-size:12px\">"+(i+1)+"</span>]&nbsp;&nbsp;");
			  else
	    			document.write("[<a href=\""+_sPageName+"_" + i + "."+_sPageExt+"\">"+(i+1)+"</a>]&nbsp;&nbsp;");
		  	}
	  			}
	  	var nextOver = _nPageCount - nCurrIndex;	//翻页到极限时候;
       //alert("nCurrIndex: "+nCurrIndex);
	   if(nCurrIndex < (_nPageCount-1))
	   document.write("<a href=\""+_sPageName+"_" + (nCurrIndex+1) + "."+_sPageExt+"\">下一页</a>&nbsp;&nbsp;");	 
	  	else if (nextOver <= nStep){
	    document.write("下一页&nbsp;&nbsp;");}	
      
	  
	     if(nCurrIndex != (_nPageCount-1))
	     document.write("<a href=\""+_sPageName+"_" + (_nPageCount-1) + "."+_sPageExt+"\">末 页</a>&nbsp;&nbsp;");	
	     else
	     document.write("末 页");
	  return false;
	
	    	}
	
	  for(i=Math.ceil(nCurrIndex-nStep/2);i<Math.ceil(nCurrIndex+nStep/2);i++){
	   var ssssss=Math.ceil(nStep/2);
	   //alert("步长一半的数值11:  "+ssssss+" nCurrIndex11: "+nCurrIndex);
		  //if(i > 0 && i <= _nPageCount){
		  if(i > 0 && i <_nPageCount){
			  if(i==nCurrIndex)
	    		document.write("[<span style=\"color:#FF0000; font-size:12px\">"+(i+1)+"</span>]&nbsp;&nbsp;");
			  else
	    		document.write("[<a href=\""+_sPageName+"_" + i + "."+_sPageExt+"\">"+(i+1)+"</a>]&nbsp;&nbsp;");
		  }
		  
	  }	  
	 } 
	 

	 //下一页和末页的设置
	   var nextOver = _nPageCount - nCurrIndex;	//翻页到极限时候;
	   if(nCurrIndex < (_nPageCount-1))
	   document.write("<a href=\""+_sPageName+"_" + (nCurrIndex+1) + "."+_sPageExt+"\">下一页</a>&nbsp;&nbsp;");	 
	  	else if (nextOver <= nStep){
	    document.write("下一页&nbsp;&nbsp;");}	
      
	  
	  if(nCurrIndex != (_nPageCount-1))
	     document.write("<a href=\""+_sPageName+"_" + (_nPageCount-1) + "."+_sPageExt+"\">末 页</a>&nbsp;&nbsp;");	
		else
	    document.write("末 页");
	  //下一页和末页的设置	



}//函数结束符
//WCM置标
createPageHTML(34, 0, "index", "shtml");	
//-->
                </SCRIPT></div>
        </div>
      
     	
      <h4><span class="se">各省区市党政领导人物库</span><div class="clear"></div></h4>
        <div class="list1">

<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/bj/index.shtml" target="_blank"><font color="#0066FF"><b>北京</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/tj/index.shtml" target="_blank"><font color="#0066FF"><b>天津</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/he/index.shtml" target="_blank"><font color="#0066FF"><b>河北</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/sx/index.shtml" target="_blank"><font color="#0066FF"><b>山西</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/nm/index.shtml" target="_blank"><font color="#0066FF"><b>内蒙古</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/ln/index.shtml" target="_blank"><font color="#0066FF"><b>辽宁</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/jl/index.shtml" target="_blank"><font color="#0066FF"><b>吉林</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/hlj/index.shtml" target="_blank"><font color="#0066FF"><b>黑龙江</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/sh/index.shtml" target="_blank"><font color="#0066FF"><b>上海</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/js/index.shtml" target="_blank"><font color="#0066FF"><b>江苏</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/zj/index.shtml" target="_blank"><font color="#0066FF"><b>浙江</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/ah/index.shtml" target="_blank"><font color="#0066FF"><b>安徽</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/fj/index.shtml" target="_blank"><font color="#0066FF"><b>福建</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/jx/index.shtml" target="_blank"><font color="#0066FF"><b>江西</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/sd/index.shtml" target="_blank"><font color="#0066FF"><b>山东</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/ha/index.shtml" target="_blank"><font color="#0066FF"><b>河南</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/hb/index.shtml" target="_blank"><font color="#0066FF"><b>湖北</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/hn/index.shtml" target="_blank"><font color="#0066FF"><b>湖南</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/gd/index.shtml" target="_blank"><font color="#0066FF"><b>广东</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/gx/index.shtml" target="_blank"><font color="#0066FF"><b>广西</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/hi/index.shtml" target="_blank"><font color="#0066FF"><b>海南</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/cq/index.shtml" target="_blank"><font color="#0066FF"><b>重庆</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/sc/index.shtml" target="_blank"><font color="#0066FF"><b>四川</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/gz/index.shtml" target="_blank"><font color="#0066FF"><b>贵州</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/yn/index.shtml" target="_blank"><font color="#0066FF"><b>云南</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/xz/index.shtml" target="_blank"><font color="#0066FF"><b>西藏</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/sn/index.shtml" target="_blank"><font color="#0066FF"><b>陕西</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/gs/index.shtml" target="_blank"><font color="#0066FF"><b>甘肃</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/qh/index.shtml" target="_blank"><font color="#0066FF"><b>青海</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/nx/index.shtml" target="_blank"><font color="#0066FF"><b>宁夏</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/xj/index.shtml" target="_blank"><font color="#0066FF"><b>新疆</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/hk/index.shtml" target="_blank"><font color="#0066FF"><b>香港</b></font></a></div>
      
        	<div class="city"><a href="http://district.ce.cn/zt/rwk/sf/mo/index.shtml" target="_blank"><font color="#0066FF"><b>澳门</b></font></a></div>
      
        	<div class="city">台湾</div>



         
            <div class="clear"></div>
        
        </div>
      
      
    </div>
    <!--right-->
    
    
    
    
    
    <div class="clear"></div>

</div>
<!--main end-->

















<div class="bottom"> <SCRIPT language="javascript" src="http://www.ce.cn/inc1/cebottom.js" charset="gbk">

</SCRIPT></div>
 

<script type="text/javascript">
var _bdhmProtocol = (("https:" == document.location.protocol) ? " https://" : " http://");
document.write(unescape("%3Cscript src='" + _bdhmProtocol + "hm.baidu.com/h.js%3Fd130ad2e0466d0dbb676e389eb463ef5' type='text/javascript'%3E%3C/script%3E"));
</script>

<!--统计-->
<div class="noplay">
<script type="text/javascript">document.write(unescape("%3Cscript src='http://cl3.webterren.com/webdig.js?z=32' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">wd_paramtracker("_wdxid=000000000000000000000000000000000000000000")</script>
</div>


</body>
</html>