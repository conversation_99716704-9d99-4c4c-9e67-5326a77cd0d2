import traceback
from lxml import etree
import requests, re, random, json
from bs4 import BeautifulSoup

user_agent_list = [
        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/22.0.1207.1 Safari/537.1",
        "Mozilla/5.0 (X11; CrOS i686 2268.111.0) AppleWebKit/536.11 (KHTML, like Gecko) Chrome/20.0.1132.57 Safari/536.11",
        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.6 (KHTML, like Gecko) Chrome/20.0.1092.0 Safari/536.6",
        "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.6 (KHTML, like Gecko) Chrome/20.0.1090.0 Safari/536.6",
        "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/19.77.34.5 Safari/537.1",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.9 Safari/536.5",
        "Mozilla/5.0 (Windows NT 6.0) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.36 Safari/536.5",
        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3",
        "Mozilla/5.0 (Windows NT 5.1) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_0) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3",
        "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1062.0 Safari/536.3",
        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1062.0 Safari/536.3",
        "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3",
        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3",
        "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3",
        "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.0 Safari/536.3",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.24 (KHTML, like Gecko) Chrome/19.0.1055.1 Safari/535.24",
        "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/535.24 (KHTML, like Gecko) Chrome/19.0.1055.1 Safari/535.24",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36"
]

# url请求，获取response
def my_requests(url):
    page_source = ""
    headers = {"user-agent": random.choice(user_agent_list)}
    if headers:
        response = requests.request("GET", url=url, headers=headers)
    else:
        response = requests.request("GET", url=url)
    if response:
        response.encoding = response.apparent_encoding
    page_source = response.text
    return page_source

# 获取机构列表，如外交部、国防部，用flag区分是提取部委机构还是地方省市列表
# 获取国务院的机构列表和省市列表
def parse_shouye_index(page_source, flag="部委机构"):
    if flag not in ["部委机构", "地方省市"]:
        return []
    else:
        res_list = []
        root = etree.HTML(page_source)
        deming_list = root.xpath(".//div[@class='main']//div[@class='deming ']/a") if flag in ["部委机构"] else root.xpath(".//div[@class='chengshi']/a")
        print(flag, "len(deming)=", len(deming_list))
        if flag in ["部委机构"]:     
            for deming in deming_list:
                text = "".join(deming.xpath("./text()"))
                href = "".join(deming.xpath("./@href"))
                info = {text : href}
                res_list.append(info)
                # print(f"{text}:{href}")
        else:
            for deming in deming_list:
                text = "".join(deming.xpath(".//text()"))
                href = "".join(deming.xpath("./@href"))
                # print(f"{text}:{href}")
                info = {text: href}
                res_list.append(info)
        return res_list

# 获取机构名称和url
def get_orgs_href(url, flag = "部委机构"):
    org_list = []
    if url:
        page_resource = my_requests(url=url)
        if page_resource:
            org_list = parse_shouye_index(page_source=page_resource, flag=flag)
            return org_list
        else:
            return org_list
    else:
        return org_list

# 获得领导人姓名和职务信息，返回list    
def get_buwei_leaders(page):
    leaders_info = []
    root = etree.HTML(page)
    div_list = root.xpath(".//div[@class='content']//div[@class='title2']")
    for div in div_list:
        positions = "".join(div.xpath("./text()"))
        ul = div.xpath("./following-sibling::*[1]")
        if len(ul)>0 and ul[0].tag=='ul':
            if ul[0].attrib["class"] in ["list1", "list1 ", " list1"]:
                li_lst = ul[0].xpath("./li")
                for li in li_lst:
                    p_txt = "".join(li.xpath("./p/a/text()"))
                    res = re.findall(r'\((.*?)\)', p_txt)
                    if res:
                        if "纪检组组长" in res or "纪检组长" in res or "党组成员" in res or "政治部主任" in res:
                            position = "".join(res)
                        else:
                            if positions in ["其他领导"]:
                                position = "".join(res)
                            else:
                                position = positions
                    else:
                        position = re.sub("纪检组长", "", positions)
                        position = re.sub("^、|、$|", "", position)    
                        position = re.sub("、{1,}", "、", position)
                    p_txt = re.sub("\(.*?\)", "", p_txt)
                    info = {"name":p_txt, "position":position}
                    if info not in leaders_info:
                        leaders_info.append(info)
    return leaders_info

# 获取市级领导人信息
def get_city_leaders(page):
    leaders = []
    root = etree.HTML(page)
    table = root.xpath(".//table[@class='MsoNormalTable']/tbody/tr")
    tr_list = table
    for idx, tr in enumerate(tr_list):
        if idx==0:
            positions = tr.xpath("./td//span/text()")
        else:
            tds = tr.xpath("./td")
            info = {}
            for td_idx, td in enumerate(tds):
                if td_idx==0:
                    city = re.sub("\s", "", "".join(td.xpath(".//text()")))
                    info["city"] = city
                else:
                    td_text = re.sub("\s", "", "".join(td.xpath(".//text()")))
                    td_text = re.sub("\(.*?\)", "", td_text)
                    position = positions[td_idx]
                    info[position] = td_text
            leaders.append(info)
    return leaders


def get_leaders_data(page="", flag="地方省市", province=""):
    leaders_data = []
    if flag not in ["地方省市", "部委机构"]:
        raise ValueError
    if flag in ["地方省市"]:
        if page:
            leaders = get_province_leaders(province=province, page=page)
        else:
            raise ValueError
    else:
        leaders = get_buwei_leaders(page=page)
    
    return leaders

def get_shengwei_shengzhengfu_leaders(tr_list):
    leaders = []
    for tr in tr_list:
        td_list = tr.xpath("./td")
        if td_list:
            pos = "".join(td_list[-1].xpath(".//text()")).strip()
            name = "".join(td_list[-2].xpath(".//text()")).strip()
            info = {"name": name, "position": pos}
            leaders.append(info)
    return leaders

# 通过解析得到人大、政协和地市领导
def get_renda_zhengxie_dishi_leaders(url="", flag="renda"):
    if flag not in ["renda", "zhengxie", "dishi"]:
        raise ValueError
    if not url:
        return []
    leaders = []

    page = my_requests(url=url)
    if not page:
        return []
    root = etree.HTML(page)
    pos_pattern = re.compile("主任|副主任|秘书长|主席|副主席")
    # 获取人大和政协领导人
    if flag in ["renda", "zhengxie"]:
        p_list = root.xpath(".//div[@class='content']//div/p")
        print(f"flag={flag}, p_list={len(p_list)}")
        stop = False
        for p in p_list:
            if not stop:
                p_text = "".join(p.xpath(".//text()")).strip()
                p_text = re.sub("简历","", p_text).strip()
                if "任免动态" in p_text:
                    stop=True
                    continue
                pos = "".join(pos_pattern.findall(p_text)).strip()
                name = re.sub(pos_pattern, "", p_text).strip()
                if name and pos:
                    info = {"name": name, "position":pos}
                    if info not in leaders:
                        leaders.append(info)
    else:
        # 获取地市领导人
        tr_list = root.xpath(".//div[@class='content']//table[@class='MsoNormalTable']/tbody/tr")
        if not tr_list:
            tr_list = root.xpath(".//div[@class='content']//table/tbody/tr")
        if tr_list:
            pos_list = []
            pos_text = tr_list[0].xpath("./td//text()")
            for item in pos_text:
                if re.compile("书记|主任|市长|主席|人大|政协|区长|州|洲|市|区|县").findall(item) and not re.compile("各市|各区|各地|各州|各洲|各盟").findall(item):
                    pos_list.append(item.strip())
        for tr in tr_list[1:]:
            td_list = tr.xpath("./td")
            city_leaders={}
            city = "".join(td_list[0].xpath(".//text()")).strip()
            city_leaders["city"]=city
            city_leaders["leaders"]=[]
            for td_idx, td in enumerate(td_list[1:]):
                name = "".join(td.xpath(".//text()")).strip()
                name = re.sub("\（.*?\）|\(.*？\)|\(.*?\)", "", name)
                pos = pos_list[td_idx]
                city_leaders["leaders"].append({"name":name, "position":pos})
            if city_leaders not in leaders:
                leaders.append(city_leaders)
    return leaders


# 获得province领导，包括省委、省政府领导
def get_province_leaders(url="", province="山西", page=""):
    leaders = []
    root = etree.HTML(page)
    lefts = root.xpath(".//div[@class='main']/div[@class='left']/div[@class='left2']")
    hrefs = []
    for idx, left in enumerate(lefts):
        find_h_tag = left.xpath("./h3|./h4")
        if find_h_tag:
            find_h_tag = find_h_tag[0]
            h_tag = find_h_tag.tag
            if h_tag in ["h3"]:
                org_name = "".join(left.xpath("./h3/a/text()")).strip()
                renwu_tr = left.xpath("./div[@class='ren2']//table/tbody/tr")
                shengwei_leaders = get_shengwei_shengzhengfu_leaders(tr_list=renwu_tr)
                leaders.append({"org":org_name, "leaders":shengwei_leaders})
            elif h_tag in ["h4"]:
                org_name = "".join(left.xpath("./h4/a//text()")).strip()
                org_name = re.sub("\+|\(.*?\)|\（.*?\）", "", org_name)
                href = "".join(left.xpath("./h4/a/@href")).strip()
                if href:
                    if idx==2:
                        flag="renda"
                    elif idx==3:
                        flag="zhengxie"
                    elif idx==len(lefts)-1:
                        flag = "dishi"
                    rendan_zhengxie_leaders = get_renda_zhengxie_dishi_leaders(url=href, flag=flag)
                    leaders.append({"org":org_name, "leaders":rendan_zhengxie_leaders})
            else:
                raise ValueError
    return leaders


if __name__=="__main__":
    with open(file="jiangxi.html", mode="r", encoding="utf-8") as file:
        page = file.read()
        leaders = get_province_leaders(province="江西", page=page)
        with open("jiangxi.json", "w", encoding="utf-8") as json_file:
            json.dump(leaders, json_file, ensure_ascii=False, indent=2)

        
