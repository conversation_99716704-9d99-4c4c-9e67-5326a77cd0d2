import os
os.environ['OPENBLAS_NUM_THREADS']='1'
import schedule, random, os, re
import time
from datetime import datetime, timedelta
from ops.fill_information import FillLeaderInformation
from ops.esops import get_date_from_ES
from utils import write_to_txt, write_to_json
from main_update_leaderinfo import weekly_task, generate_random_time


def schedule_jobs():
    hour = random.randint(9,11)
    minute = random.randint(0,59)
    second = random.randint(0,59)
    print("{:02d}:{:02d}:{:02d}".format(hour, minute, second))
    # schedule.every().day.at("{:02d}:{:02d}:{:02d}".format(hour, minute, second)).do(daily_task)
    schedule.every().wednesday.at("17:00").do(weekly_task)
    print("fill_information任务计划已设置")
if __name__=="__main__":
    schedule_jobs()
    while True:
        schedule.run_pending()
        time.sleep(60)

    

