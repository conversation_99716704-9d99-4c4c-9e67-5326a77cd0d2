import os
import time
import logging


def get_mylogger(name='', filename=None, log_dir=None, level='debug'):
    if name == '':
        name = 'logger'
        
    levels = {
        'info': logging.INFO,
        'debug': logging.DEBUG,
        'warning': logging.WARNING,
        'error': logging.ERROR,
    }
    level = levels[level]

    if log_dir is None:
        log_dir = os.path.join(os.path.dirname(__file__), 'logs')
    os.system(f'mkdir -p {log_dir}')
    # print(log_dir)

    if filename is None:
        t = time.strftime('%Y%m%d',time.localtime(time.time()))
        filename = os.path.join(log_dir, f'{t}.log')

    logger = logging.getLogger(name)

    if not logger.hasHandlers():
        logger.setLevel(level)
        formatter = logging.Formatter('%(asctime)s|%(levelname)s|%(lineno)d】%(message)s')
        fh = logging.FileHandler(os.path.join(log_dir, filename))
        fh.setLevel(level)
        fh.setFormatter(formatter)
        logger.addHandler(fh)

        ch = logging.StreamHandler()
        ch.setLevel(logging.DEBUG)
        ch.setFormatter(formatter)
        logger.addHandler(ch)  
    return logger
