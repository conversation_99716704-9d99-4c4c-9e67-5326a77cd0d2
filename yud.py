# -*- uft-8 -*-

import html
from pdb import run
import os, re, sys, json
from urllib.parse import unquote, quote
sys.path.append("/home/<USER>/leader-info/UpdateLeaderInfo/")
from ops import org_list

def list_files_in_path(directory="/home/<USER>/leader-info/UpdateLeaderInfo/runtime/20240806/snapshot/baike.baidu.com/search/word/"):
    allfiles = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            dir = os.path.join(root, file)
            if not dir.endswith(".json"):
                continue
            with open(dir, "r", encoding="utf-8") as fp:
                ddd = json.load(fp).get("query")
                match = re.search(r'word=(.*)', ddd)
                match_str = unquote(match.group(1)) if match else None
                info = {"name": match_str, "dir": dir.split("_")[0]}
                allfiles.append(info)
    return allfiles


from parsers.parser_html import *  
def _get_leaders(org_name="", org_type="高校", html_content=""):
    leaders = []
    if org_type not in ["地方"]:
        if org_type=="央企": # 央企
            leaders = parse_yq(org=org_name, page_content=html_content)
        elif org_type=="国家级协会": #国家级协会
            leaders = parse_xh(org=org_name, page_content=html_content)
        elif org_type=="国务院机构": #国务院机构
            leaders = parse_gov(org=org_name, page_content=html_content)
        elif org_type=="军事机构":# 军事机构
            leaders = parse_pla(org=org_name, page_content=html_content)
        elif org_type=="中直机构":# 中直机构
            leaders = parse_zz(org=org_name, page_content=html_content)
        elif org_type=="领导机构":# 领导机构
            leaders = parse_cpc(org=org_name, page_content=html_content)
        elif org_type=="中管高校":# 高校
            leaders = parse_school( org=org_name, page_content=html_content)
    else:
        df_org_categories  = ["政府", "中共", "人大", "政协"]
        if org_name.find("人民政府")!=-1:
            province_patt = re.compile(r"(.*?)人民政府")
            province = "".join(re.findall(province_patt, org_name))
            org_type = "政府"
        elif org_name.find("人民代表大会常务委员会")!=-1:
            province_patt = re.compile(r"(.*?)人民代表大会常务委员会")
            province = "".join(re.findall(province_patt, org_name))
            org_type = "人大"
        elif org_name.find("中国共产党")!=-1:
            province_patt = re.compile(r"中国共产党(.*?)委员会")
            province = "".join(re.findall(province_patt, org_name))
            org_type = "中共"
        elif org_name.find("中国人民政治协商会议")!=-1:
            province_patt = re.compile(r"中国人民政治协商会议(.*?)委员会")
            province = "".join(re.findall(province_patt, org_name))
            org_type = "政协"
        leaders = parse_cppcc(org_info={"province":province, "org": org_type, "org_name":org_name}, page_content=html_content)
    return leaders
    

if __name__=="__main__":
    with open("/home/<USER>/leader-info/UpdateLeaderInfo/runtime/20240806/org_list.json", "r", encoding="utf-8") as fp:
        my_org_list = json.load(fp)
    
    search_dir = "/home/<USER>/leader-info/UpdateLeaderInfo/runtime/20240806/snapshot/baike.baidu.com/search/word/"
    allfiles = list_files_in_path(directory=search_dir)

    for item in my_org_list:
        print("---------------------------------------------------------")
        org_type = item.get("parent")
        name = item.get("name")
        files = [element.get("dir") for element in allfiles if name in element.get("name")]
        html_page = ""
        for file_str in files:
            html_path = file_str
            with open(file_str, "r", encoding="utf-8") as html_fp:
                page_source = html_fp.read()
                print(name)
                leaders = _get_leaders(org_name=name, org_type=org_type, html_content=page_source)
           
        
        


    
