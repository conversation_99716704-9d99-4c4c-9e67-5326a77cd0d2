# -*- coding:utf-8 -*-
"""
这是一个用h800上的server服务，用fallen-leader-clean中的数据对leader-info-final中的数据进行过滤。
结果保存在check_result文件夹下对应日期的txt中

该文件包含以下功能：
1. 一个类Check_by_Sever，
2. 使用方法 erver = Check_by_Sever()    server.running()

"""
from cgitb import text
import traceback
from networkx import PowerIterationFailedConvergence
import requests, json, time, re, os
from elasticsearch import Elasticsearch
from tqdm import tqdm
from fuzzywuzzy import fuzz, process
from datetime import datetime


import sys

if "/home/<USER>/leader-info/UpdateLeaderInfo/ops" not in sys.path:
    sys.path.append("/home/<USER>/leader-info/UpdateLeaderInfo/ops")
from esops import get_date_from_ES

def get_glm_ouput(text1,text2):
    url = "http://180.149.156.8:9219/api/checkperson"
    glm_res = requests.get(url=url, json={"text1": text1, "text2": text2}, timeout=200)
    res_text = json.dumps(glm_res.json(), ensure_ascii=False, indent=2)
    res_text = res_text.replace("\\n", "")
    return res_text

# 计算字符串的相似度，Jaccard相似度衡量两个集合的相似程度，可以直接用来衡量两个字符串的重叠度。
def jaccard_similarity(str1, str2):
    """计算字符串的相似度，Jaccard相似度衡量两个集合的相似程度，可以直接用来衡量两个字符串的重叠度。"""
    set1 = set(str1)
    set2 = set(str2)
    intersection = len(set1 & set2)
    union = min(len(set1),len(set2))#len(set1 | set2) 这里修改了，按照短字符串计算
    similarity = intersection / union
    return similarity


class Check_by_Sever():
    """判断当前es中存储的领导人中是否有落马官员，对当前时政领导人进行过滤"""
    def __init__(self):
        configfile = os.path.join(os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__),"..")), "configs"), "es_config.json")
                # self.nerservice = NERService()
        with open(configfile, "r", encoding="utf-8") as fp:
            CONFIG_ES = fp.read()
            CONFIG_ES = json.loads(CONFIG_ES)
            self.escon = Elasticsearch(CONFIG_ES['host'], basic_auth=(CONFIG_ES['user'], CONFIG_ES['passwd']), request_timeout=120, max_retries=10, retry_on_timeout=True)
            self.fallen_leader_index = CONFIG_ES["fallen_leader_index"]
            self.leader_info_index = CONFIG_ES["leader_info_index"]

        self.fallen_leader = get_date_from_ES(self.escon, self.fallen_leader_index, query="")
        self.leader = get_date_from_ES(self.escon, self.leader_info_index, query="")

    def running(self):
        current_day = datetime.today().strftime("%Y%m%d")
        pattern = re.compile("判别结果：否|判别结果：是")
        save_dir = '/home/<USER>/leader-info/UpdateLeaderInfo/server/check_result/'#os.path.join(oa.path.abspath(os.path.dirname(__file__)),"check_result")
        with open(os.path.join(save_dir,"check_res_{}.txt".format(current_day)), "w", encoding="utf-8") as fp:
            for item in tqdm(self.leader):
                province=item["_source"]["province"]
                city = item["_source"]["city"]
                _id = item["_id"]
                name = item["_source"]["cName"]
                org = item["_source"]["organization"]
                oPosition = item["_source"]["oPosition"]
                CPL_info = "{}-{}{}{}{}".format(name,province,city,org,oPosition)
                CPL_info = re.sub("（.*?）|\(.*?\)", "", CPL_info)
                
                samename_fallen = [fallen for fallen in self.fallen_leader if fallen["_source"]["name"]==name]
                if samename_fallen:
                    fp.write("======================================================================\nCurrent Political Leader Info = {}\n".format(CPL_info))
                    fp.write("_id = {} in leader-info-new\n".format(_id))
                    for fallenitem in samename_fallen:
                        fallen_info = "{}-{}".format(fallenitem["_source"].get("name"),fallenitem["_source"].get("position"))
                        fallen_info = re.sub("（.*?）|\(.*?\)", "", fallen_info)
                        fp.write("\t---------\n")
                        fp.write("\tfallen info = {}\n".format(fallen_info))
                        glm_input_text = "{}和{}".format(CPL_info, fallen_info)
                        try:
                            glm_res = get_glm_ouput(text1=CPL_info, text2=fallen_info)
                            result = "".join(pattern.findall("{}".format(glm_res)))
                            fp.write("\tGLM Res = {}\n".format(glm_res))
                            if "判别结果：是" in result:
                                self.escon.delete(index=self.leader_info_index, id=_id)
                                break   
                            else:
                                match_radio = jaccard_similarity(CPL_info, fallen_info)
                                fp.write("\tmatch_radio = {}\n".format(match_radio))
                                if match_radio==1.0:
                                    self.escon.delete(index=self.leader_info_index, id=_id)  
                                    break            
                        except:
                            fp.write("Error = {}\n".format(traceback.format_exc()))
                else:
                    continue
                    
if __name__=="__main__":
    server = Check_by_Sever()
    server.running()