import requests
from flask import <PERSON>lask, request
import traceback
import hashlib
from pipeline import <PERSON><PERSON><PERSON><PERSON>


timeout = 60
checker = PersonChecker('config.yaml')
app = Flask(__name__)

@app.route('/leader_check_v3', methods=['POST', 'GET'])
def leader_check_v3():
    json_data = request.get_json()
    doc = json_data['sentence']
    secret_id = json_data.get('secretId', '')
    business_id = json_data.get('businessId', '')
    tasks = [
        'userdef', 
        'fallen', 
        'sensitive', 
        'relative', 
        'leader'
    ]
    try:
        res = checker.pipeline(doc, tasks, secret_id, business_id)
        return res
    except:
        print(traceback.format_exc())
        return traceback.format_exc()


if __name__=='__main__':
    app.run(host='0.0.0.0', port=9219, debug=False, threaded=False)
