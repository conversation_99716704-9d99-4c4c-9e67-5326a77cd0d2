#!/usr/bin/python3
#coding:utf8
import os, sys
import time
from os import walk
import json
import argparse
import csv
from datetime import datetime
from tqdm import tqdm
from elasticsearch import Elasticsearch
from elasticsearch import helpers
from elasticsearch.helpers import bulk, scan
import json

def read_file(filename):
    file = open(filename, 'r')  # 创建的这个文件，也是一个可迭代对象
    try:
        text = file.read()  # 结果为str类型
    finally:
        file.close()
    return text

class ManES:
    @classmethod
    def __init__(self, index_name, index_type):
        '''

        :param index_name: 索引名称
        :param index_type: 索引类型
        '''
        self.index_name =index_name
        self.index_type = index_type
        # 无用户名密码状态
        #self.es = Elasticsearch([ip])
        #用户名密码状态
        config_path = os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")),"configs/configall.json")
        with open(config_path, "r", encoding="utf-8") as config_file:
            config_data = json.load(fp=config_file)
            es_config = config_data.get("es", {})
            if es_config:
                self.es = Elasticsearch(es_config["host"], http_auth=(es_config["user"], es_config["passwd"]))
            else:
                raise ValueError("ES config is Empty!")


    def delete_index(self,index_name):
        self.es.indices.delete(index=index_name, ignore=404)

    def create_index_json(self, index_name, mapping_file):
        _index_mappings = eval(read_file(mapping_file))
        print(_index_mappings)
        if self.es.indices.exists(index_name) is not True:
            res = self.es.indices.create(index=index_name, body=_index_mappings)
            print(res)
    
    def add_case(self, case_data):
        # index_name: 数据库名称
        # case_data: list of json
        bulks = []
        _type = '_doc'
        if isinstance(case_data, list):
            for case in case_data:
                bulks.append({
                    "_index": self.index_name,
                    "_type": _type,
                    "_source": case
                })
        elif isinstance(case_data, dict):
            bulks.append({
            "_index": self.index_name,
            "_type": _type,
            "_source": case_data
            })
        else:
            print('新增数据格式错误！')
            return None
        helpers.bulk(self.es, bulks)
        return None

    def has_index(self, index_name):
        # 检查链接的es中是否存在index_name
        if self.es.indices.exists(index=index_name):
            return True
        return False

    def clear(self):
        # 清楚当前index下的所有数据
        query = {
        "query":{
            "match_all": {}
            },
        }
        scan_res = helpers.scan(client=self.es, query=query, scroll= "10m", index=self.index_name, timeout="10m")
        for res in tqdm(scan_res):
            wid = res["_id"]
            # self.es.delete(index=self.index_name, doc_type='_doc', id=wid)
            self.es.delete(index=self.index_name, id=wid)
        print('All Data Delete')



    def IndexData(self):
        es = Elasticsearch()
        csvdir = 'D:/work/ElasticSearch/exportExcels'
        filenamelist = []
        for (dirpath, dirnames, filenames) in walk(csvdir):
            filenamelist.extend(filenames)
            break
        total = 0
        for file in filenamelist:
            csvfile = csvdir + '/' + file
            self.Index_Data_FromCSV(csvfile,es)
            total += 1
            print(total)
            time.sleep(10)

    def Index_Data_FromCSV(self,csvfile):
        '''
        从CSV文件中读取数据，并存储到es中
        :param csvfile: csv文件，包括完整路径
        :return:
        '''
        '''
        list = csv.CSVOP.ReadCSV(csvfile)
        index = 0
        doc = {}
        for item in list:
            if index > 1:#第一行是标题
                doc['title'] = item[0]
                doc['link'] = item[1]
                doc['date'] = item[2]
                doc['source'] = item[3]
                doc['keyword'] = item[4]
                res = self.es.index(index=self.index_name, doc_type=self.index_type, body=doc)
                print(res['created'])
            index += 1
            print(index)
        '''

    def Index_Data(self):
        '''
        数据存储到es
        :return:
        '''
        list = [
            {   "index": 1,
                "时间": "1919-05",
                "地点": "北京",
                "事件": "五四运动",
                "简介": "1919年5月4日，北京的爱国学生开展了集会、游行、罢课等活动，反对北洋政府准备在损害中国主权的《凡尔赛和约》上签字，得到社会各界广泛支持，最终形成了全国规模的爱国运动，并取得了胜利。五四运动促进了马列主义在中国的传播，在思想上、干部上为中国共产党的建立作了准备"
             },
            {   "index": 2,
                "时间": "1919-02",
                "地点": "北京1",
                "事件": "五四运动1",
                "简介": "1919年5月4日，北京的爱国学生开展了集会、游行、罢课等活动，反对北洋政府准备在损害中国主权的《凡尔赛和约》上签字，得到社会各界广泛支持，最终形成了全国规模的爱国运动，并取得了胜利。五四运动促进了马列主义在中国的传播，在思想上、干部上为中国共产党的建立作了准备"
             }
              ]
        for item in list:
            res = self.es.index(index=self.index_name, doc_type=self.index_type, body=item)
            print(res)#['created'])

    def Bulk_CSV_Data(self, index_name, csv_name):
        with open(csv_name, encoding='utf-8') as f:
            reader = csv.reader(f)
            header = next(reader)
            for row in reader:
                doc = {}
                for idx in range(len(header)):
                    doc[header[idx]] = row[idx]
                    #print(header[idx], row[idx])
                res = self.es.index(index_name, body=doc)
                #print(doc)

    def Bulk_Data(self, bulk_datas):
        success, _ = bulk(self.es, bulk_datas, raise_on_error=True,request_timeout=6000)


    def Bulk_Json_Data(self, index_name, json_name):
        bulk_datas= []
        for line in open(json_name,'r'):
            data = json.loads(line)
            bulk_data = {
                "_index": index_name,
                "_source": data
            }
            bulk_datas.append(bulk_data)

        success, _ = bulk(self.es, bulk_datas, raise_on_error=True,request_timeout=6000)
        #print('Performed %d actions' % success)

    def bulk_Index_Data(self):
        '''
        用bulk将批量数据存储到es
        :return:
        '''
        list_data = [
            {"date": "2017-09-13",
             "source": "慧聪网",
             "link": "http://info.broadcast.hc360.com/2017/09/130859749974.shtml",
             "keyword": "电视",
             "title": "付费 电视 行业面临的转型和挑战"
             },
            {"date": "2017-09-13",
             "source": "中国文明网",
             "link": "http://www.wenming.cn/xj_pd/yw/201709/t20170913_4421323.shtml",
             "keyword": "电视",
             "title": "电视 专题片《巡视利剑》广获好评：铁腕反腐凝聚党心民心"
             },
            {"date": "2017-09-13",
             "source": "人民电视",
             "link": "http://tv.people.com.cn/BIG5/n1/2017/0913/c67816-29533981.html",
             "keyword": "电视",
             "title": "中国第21批赴刚果（金）维和部隊启程--人民 电视 --人民网"
             },
            {"date": "2017-09-13",
             "source": "站长之家",
             "link": "http://www.chinaz.com/news/2017/0913/804263.shtml",
             "keyword": "电视",
             "title": "电视 盒子 哪个牌子好？ 吐血奉献三大选购秘笈"
             }
        ]
        ACTIONS = []
        i = 1
        for line in list_data:
            action = {
                "_index": "tst_ott", #self.index_name,
                #"_type": self.index_type,
                "_id": i, #_id 也可以默认生成，不赋值
                "_source": {
                    "date": line['date'],
                    "source": line['source'], #.decode('utf8'),
                    "link": line['link'],
                    "keyword": line['keyword'], #.decode('utf8'),
                    "title": line['title']} #.decode('utf8')}
            }
            i += 1
            ACTIONS.append(action)
            # 批量处理
        success, _ = bulk(self.es, ACTIONS, raise_on_error=True)
        #success, _ = bulk(self.es, ACTIONS, index="test_ott", raise_on_error=True)
        #print('Performed %d actions' % success)

    def Delete_Index_Data(self,id):
        '''
        删除索引中的一条
        :param id:
        :return:
        '''
        res = self.es.delete(index=self.index_name, doc_type=self.index_type, id=id)
        print(res)

    def Get_Data_Id(self,index_name, id):

        res = self.es.get(index_name, id=id)
        print(res)
        print(res['_source'])

        print('------------------------------------------------------------------')
        #
        # # 输出查询到的结果
        #for hit in res['hits']['hits']:
        #    # print hit['_source']
        #    print(hit['_source']['date'],hit['_source']['source'],hit['_source']['link'],hit['_source']['keyword'],hit['_source']['title'])

    def Get_Count_By_Body(self, index_name, doc):

        _searched = self.es.count(index=index_name, body=doc)

        return _searched['count']


    def __legacy_Get_Data_By_Body(self, index_name, doc):

        _searched = self.es.search(index=index_name, body=doc)

        return _searched['hits']['hits']

        print(_searched)

        for hit in _searched['hits']['hits']:
            # print hit['_source']
            print(hit['_source']['date'], hit['_source']['source'], hit['_source']['link'], hit['_source']['keyword'], hit['_source']['title'])

    def Get_Data_By_Body(self, index_name, *args, **kwargs):
        doc = {'query': {'match_all': {}}}
        res = scan(client=self.es, query=doc, scroll='10m', index=index_name, timeout='10m') 
        return res


'''


if __name__ == "__main__":

    parser = argparse.ArgumentParser("es-tool parameters")
    parser.add_argument("cmd", type=str, nargs='?', help="[index/import/delete] operation.")
    #parser.add_argument("delete", type=str, nargs='?', help="Delete index and data operation.")
    #parser.add_argument("import", type=str, nargs='?', help="Import data from json file using bulk.")
    parser.add_argument("-name", type=str, required=True, help="(string) Name of the index containing the returned document.")
    parser.add_argument("-mapping", type=str, required="index" in sys.argv, help="(string) Name of the mapping file.")
    parser.add_argument("-data", type=str, required="import" in sys.argv, help="(string) Name of the data file.")
    parser.add_argument("-type", type=str, required="data" in sys.argv, help="(string) Name of the file type(json/csv).")
    args = parser.parse_args()

    obj =Elastic(args.name,"_doc",ip ="127.0.0.1")

    if args.cmd=='index':
        #obj.delete_index(args.name)
        obj.create_index_json(args.name, args.mapping)
    elif args.cmd=='delete':
        obj.delete_index(args.name)
    elif args.cmd=='import':
        if args.type=='json':
            obj.Bulk_Json_Data(args.name, args.data)
        elif args.type=='csv':
            obj.Bulk_CSV_Data(args.name, args.data)
'''
