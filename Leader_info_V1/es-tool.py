#coding:utf8
import os, sys
import time
from os import walk
import json
import argparse
import csv
from dbops import ManES
from datetime import datetime
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk

def read_file(filename):
    file = open(filename, 'r')  # 创建的这个文件，也是一个可迭代对象
    try:
        text = file.read()  # 结果为str类型
    finally:
        file.close()
    return text


if __name__ == "__main__":

    parser = argparse.ArgumentParser("es-tool parameters")
    parser.add_argument("cmd", type=str, nargs='?', help="[index/import/delete] operation.")
    #parser.add_argument("delete", type=str, nargs='?', help="Delete index and data operation.")
    #parser.add_argument("import", type=str, nargs='?', help="Import data from json file using bulk.")
    parser.add_argument("-name", type=str, required=True, help="(string) Name of the index containing the returned document.")
    parser.add_argument("-mapping", type=str, required="index" in sys.argv, help="(string) Name of the mapping file.")
    parser.add_argument("-data", type=str, required="import" in sys.argv, help="(string) Name of the data file.")
    parser.add_argument("-type", type=str, required="data" in sys.argv, help="(string) Name of the file type(json/csv).")
    args = parser.parse_args()

    obj =ManES(args.name,"_doc",ip ="127.0.0.1")

    if args.cmd=='index':
        #obj.delete_index(args.name)
        obj.create_index_json(args.name, args.mapping)
    elif args.cmd=='delete':
        obj.delete_index(args.name)
    elif args.cmd=='import':
        if args.type=='json':
            obj.Bulk_Json_Data(args.name, args.data)
        elif args.type=='csv':
            obj.Bulk_CSV_Data(args.name, args.data)



'''
obj.delete_index('ott')
obj.delete_index('tst-ott')
obj.create_index_json('leadertalkdata-article', 'data/leadertalkdata-people-mapping.json')
obj.Bulk_Json_Data("leadertalkdata-article", 'data/leadertalkdata-people.json')
#obj.create_index()
#obj.Index_Data()
#obj.bulk_Index_Data()
'''

# obj.IndexData()
# obj.Delete_Index_Data(1)
# csvfile = 'D:/work/ElasticSearch/exportExcels/2017-08-31_info.csv'
# obj.Index_Data_FromCSV(csvfile)
# obj.GetData(es)
