from operator import index
import os, json
import time
from collections import Counter
from dbops import ManES
from utils import ROOT, name_filter, remove_parentheses, add_call_position, expand_other_titles, generate_all_titles, get_all_titles
from logger import logger
import datetime

os.chdir(os.path.dirname(__file__))



def load_es_data(obj: ManES):
    # 从es中加载采
    items = []
    doc = {"_source":[], "size":50000}
    # all_res = obj.Get_Data_By_Body('leader-info-new', doc)
    all_res = obj.Get_Data_By_Body('leader-info-final', doc)
    for item in all_res:
        items.append(item['_source'])  
    all_names = [item['eName'] for item in items]
    c = Counter(all_names) 
    temp_dict = dict(c)
    repeat_names = [n for n in temp_dict if temp_dict[n]>1]
    return items, repeat_names


def load_es_orgs(obj: ManES):
    #加载机构信息
    orgs = {}
    doc = {"_source":[], "size":1000}
    all_res = obj.Get_Data_By_Body('organizations', doc)
    for res in all_res:
        item = res['_source']
        if item:
            orgs[item["name"]] = item["short_name"]
            total_list = item["short_name"] +[item["name"]]
            for org in item["short_name"]:
                if not org:
                    continue
                temp_list = total_list.copy()
                temp_list.remove(org)
                orgs[org] = temp_list
    # import os, json
    # with open(os.path.join(os.path.dirname(__file__), "organizations.json"), "w", encoding='utf-8') as fp:
    #     json.dump(orgs, fp, ensure_ascii=False, indent=4)
    return orgs 


def confirm_person(items, repeat_names): 
#将姓名相同的人进行合并
    name_dict= {} # 存储兼职和重名人物的{name1:[{'province':p1,'url':url1,...},{'province':p2,'url':url2,...}……]}
    for item in items:
        if item['eName'] in repeat_names and item['url'] and item['ePosition']: #存在重名或兼任, 编辑职务不为空
            temp_infos = name_dict.get(item['eName'],[])
            if  temp_infos:
                #temp_titles = [t['ePosition'] for t in temp_infos] #存储编辑职务
                temp_titles = [t['url'] for t in temp_infos] #存url
                #if item['ePosition'] not in temp_titles:
                if item['url'] not in temp_titles:
                    name_dict[item['eName']] = name_dict.get(item['eName'],[]) + [item]
            else:    
                name_dict[item['eName']] = name_dict.get(item['eName'],[]) + [item]
    # 先把有url的信息添加到name_dict中，再处理无url信息的                
    for item in items:                
        if item['eName'] in repeat_names and (not item['url']) and item['province'] and item['oPosition']: #url为空，且为地方领导，province相同，进行合并
            temp_infos = name_dict.get(item['eName'],[])
            if temp_infos:
                temp_province = [t['province'] for t in temp_infos if t['province']]
                if item['province'] not in temp_province:
                    name_dict[item['eName']] = name_dict.get(item['eName'],[]) + [item]
                # else: #记录中有相同省份，进行合并, 利用机构职务补全ePosition
                #     for t in name_dict[item['eName']]:
                #         if t['province']==item['province']:
                #             if len(t['oPosition'])>1 and  t['oPosition'][-1] in "；。，" and item['oPosition'] not in t['ePosition']:
                #                 t['ePosition'] = t['ePosition'][:-1]+'，' + item['oPosition']
                #             else:
                #                 t['ePosition'] += '，'+item['oPosition']                         
            else:    
                name_dict[item['eName']] = name_dict.get(item['eName'],[]) + [item]        
    return name_dict


def make_dataset(items, name_dict, org_dict, repeat_names, esobj, index_name='leader-info-v1'):
	# 对抓取的数据进行后处理，整理为入ES库格式(json)
    new_lines = []    
    process_names = [] #记录已处理过的姓名
    url_num = 0
    valid_num = 0
    for subitem in items:
        if not subitem['eName']:
            # print(subitem)
            continue
        name = name_filter(subitem['eName']) # 姓名信息 
        use_url = 0 #记录是否使用了URL
        if name not in repeat_names: #该姓名没有重名或兼职
            subitems = [subitem]
        elif name in name_dict and name not in process_names:#该姓名存在重名或兼职
            subitems = name_dict.get(name)  
            process_names.append(name)
        else:
            continue   

        for item in subitems:
            if item['ePosition'] and item['url']: # 存在编辑职务
                title = item['ePosition']
                title = remove_parentheses(title) # 去除职务中的括号
                use_url = 1
                url_num += 1
            else: # 不存在url, 将title设置为 机构+机构职务

                continue
            if item.get('leader_type', 'no') != 'no' and len(title)>4 and int(item['leader_type']) == 0:
                cp = add_call_position(title,name)
                cp = [name+c for c in cp] 
                match_text = title + name 
                other_titles =  expand_other_titles(title, item['province'], item['city'], org_dict)  
                other_titles_v1 = generate_all_titles(title, org_dict,name)

                jsonline = {}
                jsonline['name'] = name
                jsonline['url'] = item['url']
                jsonline['title'] = title
                if other_titles:
                    mix_titles = other_titles_v1.copy()
                    for t in other_titles:
                        if t not in other_titles_v1:
                            mix_titles.append(t)

                    jsonline['all_title'] = mix_titles 

                else:
                    jsonline['all_title'] = other_titles_v1
                jsonline['match_text'] = match_text
                jsonline['position'] = cp  
                jsonline['use_url'] = use_url
                jsonline['province'] = item['province']
                jsonline['city'] = item['city']
                jsonline["matchtext_all"]=[]
                for n in get_all_titles(title, org_dict): #增加扩展字段
                    jsonline["matchtext_all"].append(n+name)                 
                if len(name)>1:
                    new_lines.append(jsonline) 
                    valid_num += 1
    print(valid_num, url_num)
    return new_lines
    # 不json保存文件，直接上传es
    #update_es(index_name, new_lines, esobj)
    #return valid_num, url_num


def update_es(index_name, lines, esobj):
    if esobj.has_index(index_name):
        esobj.index_name = index_name
    else:
        cmd_index_str = f'/home/<USER>/miniconda3/envs/leader_check/bin/python {ROOT}/es-tool.py index -name leader-info-v1 -mapping {ROOT}/data/leader-info-v1-mapping.json'
        os.system(cmd_index_str)
    if esobj.index_name == index_name:
        esobj.clear() # 清空原表数据
        esobj.add_case(lines) # 添加新数据
        timestr = str(time.localtime(time.time()))
        with open(f'{ROOT}/es_build.record', 'w', encoding='utf-8') as f:
            f.write(timestr)

def backup_historical_data(obj:ManES, index_name, historical_path = "history"):
    '''根据index备份历史数据到文件夹history下'''
    data = obj.Get_Data_By_Body(index_name=index_name)
    history_data = []
    for item in data:
        history_data.append(item)
    today_str = datetime.datetime.today().strftime("%Y%m%d")
    history_file = os.path.join(historical_path, "{}_{}.json".format(index_name,today_str))
    with open(history_file, "w", encoding="utf-8") as fp:
        json.dump(obj=history_data, fp=fp, ensure_ascii=False, indent=4)
    return history_data


logger.info('开始更新leader-info-v1')
obj = ManES('leader-info-v1', '_doc')
items, repeat_names = load_es_data(obj)   # 这里的items是lead-info-final的数据
'''备份历史数据'''
leader_info_final = backup_historical_data(obj=obj, index_name="leader-info-final", historical_path="history")
leader_info_v1 = backup_historical_data(obj=obj, index_name="leader-info-v1", historical_path="history")
logger.info(f"备份leader-info-final数据：{len(leader_info_final)}")
logger.info(f"备份leader-info-v1数据：{len(leader_info_v1)}")

logger.info(f'读取领导人: {len(items)}')
logger.info(f'重复人名: {len(repeat_names)}')
org_dict = load_es_orgs(obj)
logger.info(f'读取机构: {len(org_dict)}')
name_dict = confirm_person(items, repeat_names)
logger.info(f'合并重复人名: {len(name_dict)}')
names = name_dict.keys()
# print(names)
# print(len(names))
new_lines = make_dataset(items, name_dict, org_dict, repeat_names, obj)
logger.info(f'生成数据集: {len(new_lines)}')
# update_es('leader-info-v1', new_lines, obj)
# logger.info('leader-info-v1更新完成')

