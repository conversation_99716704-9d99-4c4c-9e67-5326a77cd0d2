#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的职务名称解析模块

该模块将复杂的中文职务名称字符串解析为结构化的数据格式，
输出包含省份、城市、机构和职位信息的字典列表。

Author: 优化版本
Date: 2025-08-22
"""

import re
import json
import os
try:
    from typing import List, Dict, Optional, Tuple
except ImportError:
    # Python 2.7 兼容性
    List = list
    Dict = dict
    Optional = object
    Tuple = tuple


class TitleAnalyzer:
    """职务名称解析器"""
    
    # 配置常量
    ORG_TRIGGERS = {
        'party': ["委", "党委"],
        'party_sub': ["组织部", '宣传部', "统战部", "纪委", "政法委"],
        'government': ["政府", "省长", "市长", "区长"],
        'government_sub': ["办公厅", "机关"],
        'congress': ["人大常委会", "人民代表大会"],
        'cppcc': ["政协", "政治协商会议"],
        'university': ["大学", "学院", "委员会", "人大", "人民代表大会"],
        'enterprise': ["公司", "集团", "银行"],
        'military': ["兵团", "军区", "警备区"],
        'city': ["市", "区", "地区", "自治州"],
        'democratic_parties': ['民革', '民盟', '民建', '民进', '农工党', '致公党', '九三学社', '台盟', '主委']
    }
    
    # 省份信息
    PROVINCES = [
        '河北省', '山西省', '辽宁省', '吉林省', '黑龙江省', '江苏省', '浙江省', '安徽省', 
        '福建省', '江西省', '山东省', '河南省', '湖北省', '湖南省', '广东省', '海南省', 
        '四川省', '贵州省', '云南省', '陕西省', '甘肃省', '青海省', '内蒙古自治区', 
        '广西壮族自治区', '西藏自治区', '宁夏回族自治区', '新疆维吾尔自治区',
        '北京市', '天津市', '上海市', '重庆市'
    ]
    
    PROVINCE_SHORT_NAMES = [p[:2] for p in PROVINCES]
    
    # 职位关键词
    KEY_POSITIONS = [
        "主席", "省长", "书记", "主任", "委员", "会长", "党组书记", "党组成员", 
        "党委书记", "行政长官", '院长', '校长', "局长", "部长", "市长", "总理", 
        "常委", "总编辑", "秘书长", "组长", "总书记", "牵头人", "外长", "成员", 
        "领袖", "总经理", "委员长", '检察长', '董事长',"台长"
    ]
    
    def __init__(self, data_dir=None):
        """
        初始化解析器
        
        Args:
            data_dir: 数据目录路径，包含城市名称等配置文件
        """
        self.data_dir = data_dir or os.path.dirname(__file__)
        self.all_city = []
        self.city2province = {}
        self._load_city_data()
        
        # 构建触发器映射
        self.trigger_map = {
            1: self.ORG_TRIGGERS['party'],
            2: self.ORG_TRIGGERS['government'],
            3: self.ORG_TRIGGERS['congress'],
            4: self.ORG_TRIGGERS['cppcc']
        }
    
    def _load_city_data(self):
        """加载城市数据"""
        try:
            city_file = os.path.join(self.data_dir, 'data', 'city_names.txt')
            if os.path.exists(city_file):
                with open(city_file, 'r', encoding='utf-8') as f:
                    city_dict = json.loads(f.read())
                
                for province in city_dict:
                    if province[-1] in "省市":
                        pro_name = province[:-1]
                    elif province[-3:] == "自治区":
                        pro_name = province[:-3]
                    else:
                        pro_name = ''
                    
                    if pro_name:
                        for city_name in city_dict[province]:
                            self.city2province[city_name[:2]] = pro_name
                
                for city_list in city_dict.values():
                    self.all_city.extend(city_list)
        except (FileNotFoundError, json.JSONDecodeError):
            # 如果文件不存在或格式错误，使用默认配置
            pass
    
    def trigger(self, text, trigger_list):
        """
        触发函数，返回命中的关键词
        
        Args:
            text: 待检查的文本
            trigger_list: 触发词列表
            
        Returns:
            命中的关键词，如果没有命中返回空字符串
        """
        for keyword in trigger_list:
            if keyword in text:
                return keyword
        return ""
    
    def normalize_separators(self, text):
        """
        标准化分隔符，将顿号替换为逗号
        
        Args:
            text: 原始文本
            
        Returns:
            标准化后的文本
        """
        if not text:
            return text
            
        u_trigger = self.ORG_TRIGGERS['university']
        c_trigger = self.ORG_TRIGGERS['enterprise']
        p_trigger = self.ORG_TRIGGERS['democratic_parties']
        
        org_all = ["省委", "市委", "区委", "州委", "盟委", "政府"] + \
                  self.ORG_TRIGGERS['congress'] + self.ORG_TRIGGERS['cppcc'] + ['主委']
        
        # 如果不是高校或企业，进行分隔符标准化
        if not self.trigger(text, u_trigger) and not self.trigger(text, c_trigger):
            end_idx = len(text)
            for i in range(len(text) - 1, -1, -1):
                if i >= len(text):
                    continue
                    
                if (text[i] == '、' and 
                    (self.trigger(text[i+1:end_idx], org_all) or 
                     self.trigger(text[i+1:end_idx], p_trigger) or 
                     len(text[i+1:end_idx]) > 6)):
                    text = text[:i] + '，' + text[i+1:]
                    end_idx = i
                    
                if text[i] in '，、':
                    end_idx = i
                    
                if (i > 0 and i < len(text) - 1 and
                    text[i] == '兼' and 
                    text[i-1] not in '，、' and 
                    text[i+1] != '职'):
                    text = text[:i] + '，' + text[i+1:]
                    end_idx = i
        
        return text
    
    def create_position_dict(self):
        """创建职位字典"""
        return {"province": "", "city": "", "org": "", "position": []}

    def match_position(self, index, text):
        """
        判断文本第index个字向前，是否为职务名
        
        Args:
            index: 字符位置
            text: 文本内容
            
        Returns:
            职务名的长度
        """
        max_length = 0
        positions = ['副' + k for k in self.KEY_POSITIONS] + self.KEY_POSITIONS
        positions = list(set(positions))
        
        for length in range(2, 6):
            if index >= length and text[index-length:index] in positions:
                if length > max_length:
                    max_length = length
        
        return max_length

    def parse_local_government(self, sub_str):
        """
        解析地方四套班子职务

        Args:
            sub_str: 职务子字符串

        Returns:
            解析结果字典或None
        """
        if len(sub_str) < 2 or sub_str[:2] not in self.PROVINCE_SHORT_NAMES:
            return None

        # 排除高校、民主党派、军区的干扰
        if (self.trigger(sub_str, self.ORG_TRIGGERS['university']) or
            self.trigger(sub_str, self.ORG_TRIGGERS['democratic_parties']) or
            self.trigger(sub_str, self.ORG_TRIGGERS['military'])):
            return None

        result = self.create_position_dict()

        # 解析省份信息
        province_idx = self.PROVINCE_SHORT_NAMES.index(sub_str[:2])
        province_name = self.PROVINCES[province_idx]

        if province_name[-1] in "省市":
            result["province"] = province_name[:-1]
            province_len = 1
        elif province_name[-3:] == '自治区':
            result["province"] = province_name[:-3]
            province_len = 3
        else:
            return None

        local_len = len(result["province"]) + province_len
        org_start = len(result["province"])

        # 解析城市信息
        city_char = self.trigger(sub_str[local_len:], self.ORG_TRIGGERS['city'])
        if city_char and city_char != province_name[-1]:
            end_idx = sub_str.index(city_char)
            result["city"] = sub_str[local_len:end_idx]
            local_len += len(result["city"]) + len(city_char)
            org_start = local_len - len(city_char)
            province_len = len(city_char)

        # 匹配四套班子
        for i in range(1, 5):
            trigger_list = self.trigger_map[i]
            trigger_word = self.trigger(sub_str[local_len:], trigger_list)

            if trigger_word and trigger_word not in self.ORG_TRIGGERS['government']:
                # 非政府机构
                end_idx = sub_str.index(trigger_word) + len(trigger_word)
                result["org"] = sub_str[org_start:end_idx]

                if "、" in sub_str[end_idx+1:]:
                    result["position"] = sub_str[end_idx:].split("、")
                else:
                    result["position"] = [sub_str[end_idx:]]

                return result

            elif trigger_word in self.ORG_TRIGGERS['government']:
                # 政府机构
                if trigger_word == "政府":
                    org_end_idx = sub_str.index(trigger_word) + len(trigger_word)
                    result["org"] = sub_str[org_start:org_end_idx]
                    end_idx = org_end_idx
                else:
                    result["org"] = sub_str[org_start:org_start+province_len] + '政府'
                    end_idx = org_start + province_len

                if "、" in sub_str[end_idx+1:]:
                    result["position"] = sub_str[end_idx:].split("、")
                else:
                    result["position"] = [sub_str[end_idx:]]

                return result

        return None

    def parse_university_enterprise(self, sub_str):
        """
        解析高校和央企职务

        Args:
            sub_str: 职务子字符串

        Returns:
            解析结果字典或None
        """
        combined_triggers = self.ORG_TRIGGERS['university'] + self.ORG_TRIGGERS['enterprise']
        hit_keyword = self.trigger(sub_str, combined_triggers)

        if hit_keyword:
            result = self.create_position_dict()
            end_idx = sub_str.index(hit_keyword) + len(hit_keyword)
            result["org"] = sub_str[:end_idx]

            positions = sub_str[end_idx:].split('、') if sub_str[end_idx:] else []
            result["position"] = [pos for pos in positions if pos.strip()]

            return result

        return None

    def parse_democratic_party(self, sub_str, prev_sub_str):
        """
        解析民主党派职务

        Args:
            sub_str: 当前职务子字符串
            prev_sub_str: 前一个职务子字符串

        Returns:
            解析结果字典或None
        """
        if not self.trigger(prev_sub_str, self.ORG_TRIGGERS['democratic_parties']):
            return None

        result = self.create_position_dict()

        if len(sub_str) >= 2 and sub_str[:2] in self.PROVINCE_SHORT_NAMES:
            party_name = self.trigger(prev_sub_str, self.ORG_TRIGGERS['democratic_parties'])
        else:
            party_name = ''

        position_length = self.match_position(len(sub_str), sub_str)
        end_idx = len(sub_str) - position_length

        result["org"] = party_name + sub_str[:end_idx]
        if position_length > 0:
            result["position"].append(sub_str[end_idx:])

        return result

    def parse_central_position(self, sub_str, title_results):
        """
        解析中央或其他非地方职务

        Args:
            sub_str: 职务子字符串
            title_results: 已解析的结果列表

        Returns:
            解析结果字典或None
        """
        result = self.create_position_dict()

        # 从右向左匹配职位
        idx = len(sub_str)
        end_idx = idx

        while idx >= 0:
            position_length = self.match_position(idx, sub_str)
            if position_length:
                result["position"].append(sub_str[idx-position_length:idx])
                end_idx = idx - position_length
                idx = idx - position_length
            else:
                idx = idx - 1

            # 处理"兼"字连接的职务
            if "兼" in sub_str and re.findall(r"兼(?!职)", sub_str):
                continue
            else:
                break

        result["org"] = sub_str[:end_idx]
        result["org"] = re.sub("兼任", "", result["org"])
        result["position"] = result["position"][::-1]  # 反转顺序

        # 处理省市简称补全
        if (result["org"] and title_results and title_results[-1]["province"] and
            (result["org"][0] in ["省", "市"] or result["org"][:3] == '自治区')):
            result["province"] = title_results[-1]["province"]
            result["city"] = title_results[-1]["city"]

            # 处理政府职务
            trigger_word = self.trigger(sub_str, self.ORG_TRIGGERS['government'])
            if trigger_word in self.ORG_TRIGGERS['government']:
                if trigger_word == "政府":
                    org_end_idx = sub_str.index(trigger_word) + len(trigger_word)
                    result["org"] = sub_str[:org_end_idx]
                    end_idx = org_end_idx
                else:
                    # 这里需要province_len，但在当前上下文中不可用，使用默认值
                    result["org"] = result["org"] + '政府'
                    end_idx = len(result["org"])

                if "、" in sub_str[end_idx+1:]:
                    result["position"] = sub_str[end_idx:].split("、")
                else:
                    result["position"] = [sub_str[end_idx:]]

        # 处理城市职务简称
        elif (len(result["org"]) > 2 and title_results and
              result["org"][:2] in self.city2province and
              title_results[-1]["province"] == self.city2province.get(result["org"][:2])):
            city_names = [c for c in self.all_city if c[:2] == result["org"][:2]]
            if city_names:
                result["province"] = title_results[-1]["province"]
                result["city"] = city_names[0][:-1]
                result["org"] = result["org"][len(city_names[0][:-1]):]

        return result if result["org"] and result["position"] else None

    def analysis_title_str(self, title_str, name=""):
        # region 将字符串格式的职务名称解析为结构化数据格式
        """
        Args:
            title_str: 职务名称字符串
            name: 人员姓名（可选，用于调试）

        Returns:
            包含{"province", "city", "org", "position"}字典的列表
        """
        # endregion
        if not title_str or not title_str.strip():
            return []

        title_results = []

        # 预处理：标准化分隔符
        title_str = self.normalize_separators(title_str)

        # 分割职务
        sub_list = re.split("[，、]", title_str)

        for k, sub_str in enumerate(sub_list):
            if not sub_str.strip():
                continue

            result = None

            # 1. 尝试解析地方四套班子职务
            result = self.parse_local_government(sub_str)

            # 2. 尝试解析高校和央企职务
            if not result:
                result = self.parse_university_enterprise(sub_str)

            # 3. 尝试解析民主党派职务
            if not result and k > 0:
                result = self.parse_democratic_party(sub_str, sub_list[k-1])

            # 4. 解析中央或其他职务
            if not result:
                result = self.parse_central_position(sub_str, title_results)

            # 添加有效结果
            if result and result not in title_results:
                title_results.append(result)

        return title_results


# 便捷函数
def analysis_title_str(title_str, name=""):
    """
    便捷函数：解析职务名称字符串

    Args:
        title_str: 职务名称字符串
        name: 人员姓名（可选）

    Returns:
        解析结果列表
    """
    analyzer = TitleAnalyzer()
    return analyzer.analysis_title_str(title_str, name)


# 示例用法
if __name__ == "__main__":
    # 测试用例
    test_cases = [
        "河北省委书记",
        "国务院总理",
        "北京大学校长",
        "中国工商银行董事长",
        "湖北省委常委，武汉市委书记",
        "民盟中央主席", 
        "中央广播电视总台副台长、党组成员",
        "中国文联副主席、中国音协主席、香港中文大学音乐学院院长",
        "中国文联党组成员，中国美术家协会副主席，浙江省美术家协会主席，，中国文联第十一届书记处书记"
    ]

    analyzer = TitleAnalyzer()

    for case in test_cases:
        print(f"\n输入: {case}")
        result = analyzer.analysis_title_str(case)
        for i, res in enumerate(result):
            print(f"结果{i+1}: {res}")
