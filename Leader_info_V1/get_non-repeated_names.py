from operator import index
import os, json
import time
from collections import Counter
from dbops import ManES
from utils import ROOT, name_filter, remove_parentheses, add_call_position, expand_other_titles, generate_all_titles, get_all_titles
from logger import logger
import datetime
from collections import defaultdict
import re

#os.chdir(os.path.dirname(__file__))

def load_es_data(obj: ManES):
    # 从es中加载采
    items = []
    doc = {"_source":[], "size":50000}
    # all_res = obj.Get_Data_By_Body('leader-info-new', doc)
    all_res = obj.Get_Data_By_Body('leader-info-final', doc)
    for item in all_res:
        items.append(item['_source'])  
    all_names = [item['eName'] for item in items]
    c = Counter(all_names) 
    temp_dict = dict(c)
    repeat_names = [n for n in temp_dict if temp_dict[n]>1]
    non_repeat_names = [n for n in temp_dict if temp_dict[n]==1]
    print("all_names count=", len(all_names))
    print("repeat_names=", len(repeat_names))
    print("non-repeat_names=", len(non_repeat_names))
    return items, repeat_names, non_repeat_names


def load_es_orgs(obj: ManES):
    #加载机构信息
    orgs = {}
    doc = {"_source":[], "size":1000}
    all_res = obj.Get_Data_By_Body('organizations', doc)
    for res in all_res:
        item = res['_source']
        if item:
            orgs[item["name"]] = item["short_name"]
            total_list = item["short_name"] +[item["name"]]
            for org in item["short_name"]:
                if not org:
                    continue
                temp_list = total_list.copy()
                temp_list.remove(org)
                orgs[org] = temp_list
    # import os, json
    # with open(os.path.join(os.path.dirname(__file__), "organizations.json"), "w", encoding='utf-8') as fp:
    #     json.dump(orgs, fp, ensure_ascii=False, indent=4)
    return orgs 
def confirm_person(items, repeat_names): 
#将姓名相同的人进行合并
    name_dict= {} # 存储兼职和重名人物的{name1:[{'province':p1,'url':url1,...},{'province':p2,'url':url2,...}……]}
    for item in items:
        if item['eName'] in repeat_names and item['url'] and item['ePosition']: #存在重名或兼任, 编辑职务不为空
            temp_infos = name_dict.get(item['eName'],[])
            if  temp_infos:
                #temp_titles = [t['ePosition'] for t in temp_infos] #存储编辑职务
                temp_titles = [t['url'] for t in temp_infos] #存url
                #if item['ePosition'] not in temp_titles:
                if item['url'] not in temp_titles:
                    name_dict[item['eName']] = name_dict.get(item['eName'],[]) + [item]
            else:    
                name_dict[item['eName']] = name_dict.get(item['eName'],[]) + [item]
    # 先把有url的信息添加到name_dict中，再处理无url信息的                
    for item in items:                
        if item['eName'] in repeat_names and (not item['url']) and item['province'] and item['oPosition']: #url为空，且为地方领导，province相同，进行合并
            temp_infos = name_dict.get(item['eName'],[])
            if temp_infos:
                temp_province = [t['province'] for t in temp_infos if t['province']]
                if item['province'] not in temp_province:
                    name_dict[item['eName']] = name_dict.get(item['eName'],[]) + [item]
                # else: #记录中有相同省份，进行合并, 利用机构职务补全ePosition
                #     for t in name_dict[item['eName']]:
                #         if t['province']==item['province']:
                #             if len(t['oPosition'])>1 and  t['oPosition'][-1] in "；。，" and item['oPosition'] not in t['ePosition']:
                #                 t['ePosition'] = t['ePosition'][:-1]+'，' + item['oPosition']
                #             else:
                #                 t['ePosition'] += '，'+item['oPosition']                         
            else:    
                name_dict[item['eName']] = name_dict.get(item['eName'],[]) + [item]        
    return name_dict

def match_pos(text):
    a = "董事长|主席|省长|市长|区长|书记|主任|调研员|委员|会长|社长|行长|首席|监事|党组|长官|局长|部长|司长|司令|政委|参谋长|专员|市长|市长|区长|州长|盟长|县长|干部|巡视|督学|总理|常委|编辑|编委|秘书|会计|组长|牵头人|外长|成员|领导|领袖|经理|董事|总监|校长|副校|院长|检察长|经济师|工程师|畜牧师|农艺师|兽医师|署长|副署长|总设计师|总会计师|总审计师|上将|教育长|总审计师|总飞行师|理事|常委"

    #def get_position_pattern():
    pos = "处长|局长|行长|社长|编辑|部长|总裁|司长|省长|常委|专员|董事|院长|校长|专务|干部|委员|教授|所长|主委|台长|关长|经理|会长|总监|区长|县长|政委|书记|总书记|监事|主席|州长|市长|校长|庭长|成员|组长|参谋|参事|主任|厅长|军长|师长|旅长|团长|营长|理事长|总编辑|总队长|总经理|支队长|检查员|司令员|巡视员|调研员|检察长|监狱长|督导员|总院长|盟长|侦察员|镇长|审判员|督察长|秘书长|侦查员|经济师|首席业务创新官|专职纪检监察员|首席发展顾问|警务专员|专职评审委员|信访督查专员|业务总监|特级信贷经理|首席业务经理|专职外部董事|纪检监察员|总经济师|中央委员|法人代表|第一政委|大队长|风险总监|咨询委员|评审委员|副部队长|区委常委|总工程师|总会计师|社务委员|资深经理|监察专员|资深专家|资深专员|第一书记|运行总监|国务委员|执行董事|行务委员|巡视专员|督察员|高级警长|警长|审查员|CEO|CTO|CIO|CFO|工作人员|督办|顾问|专家|高级专家|总审计师|审计师|首席信息官|首席专家|专家|馆长|首席审计官|总地质师|地质师|高级法官|法官|总工|顾问|法定代表人|副职|正职|理事|理事长|审计长|大使|全权大使|特命全权大使|总设计师|设计师|署长|首席兽医师|首席兽医官|首席风险官|总飞行师|飞行师|编委|校（院）长|编导|总理|领导人|记者|审核|审核员|审校|主编"
    #return p

    more_pos = [x for x in a.split('|') if x not in pos]
    pos += "|" + "|".join(more_pos)
    p = f"(专职|常务|一级|二级|三级|四级|助理|党组|候补)?副?({pos})"
    match = re.search(p, text)
    #print(text, match)
    if match:
        if match.span()[1] == len(text):
            return True
    return False
    #if re.fullmatch(p, text):
    #    return True
    #return False




def confirm_non_repeat_names(items, non_repeat_names, org_dict):
    """
    职务拼接规则:
1、parent！=‘地方’，检查position里是不是有机构全称和机构简称，没有直接拼接，有的话，present post=position
2、parent=='地方’，province不为空，city不为空，没有url的话，用现有的present post
3、parent=='地方', province不为空，city为空，省委四套班子，中共=中国共产党{}委员会，政协=中国人民政治协商会议{}委员会，政府={}人民政府，人大={}人民代表大会常务委员会，这种拼接比较麻烦
    """

    province_4class = {
            "中共": "中国共产党{}委员会",
            "政协": "中国人民政治协商会议{}委员会",
            "政府":"{}人民政府",
            "人大":"{}人民代表大会常务委员会"
    }

    situa_count = defaultdict(int)
    for item in items:
        if item["eName"] in non_repeat_names:
            present_pos = ""
            org = item["organization"]
            prov = item["province"]
            city = item["city"]
            
            if item["type"] not in ["地方"]:
                org_names = org_dict.get(org, []) + [org]
                for name in org_names:
                    if name in item["oPosition"]:
                        present_pos = item["ePosition"]
                        situa_count["not_difang_org_in_position"] += 1
                        #print(item, present_pos)
                        break
                else:
                    situa_count["not_difang_org_not_in_position"] += 1
                    present_pos = org + item["oPosition"]
            elif item["type"] in ["地方"]:
                if prov and city:
                    if not item["url"]:
                        situa_count["difang_no_url"] += 1
                        present_pos = item["ePosition"]
                    else:
                        situa_count["difang_has_url"] += 1
                        pass
                if prov and not city:
                    if item["organization"] in province_4class:
                        situa_count["difang_in_province_4class"] += 1
                        present_pos = province_4class[item["organization"]].format(prov) + item["oPosition"]
                    else:
                        situa_count["difang_not_in_province_4class"] += 1
                        #print(item, present_pos)
                        pass
            if match_pos(present_pos):
                situa_count["its_pos"] += 1
            else:
                situa_count["not_pos"] += 1
                print(present_pos)
            #print(present_pos)
    print(situa_count)
                

print(match_pos(text="海淀三中专职副校长"))

#os._exit(1)

obj = ManES('leader-info-v1', '_doc')
items, repeat_names, non_repeat_names = load_es_data(obj)   # 这里的items是lead-info-final的数据
org_dict = load_es_orgs(obj)
#print(org_dict)
name_dict = confirm_person(items, repeat_names)
confirm_non_repeat_names(items, non_repeat_names, org_dict)
names = name_dict.keys()
#print(names)
print(len(names))
for k,v in name_dict.items():
    print(k, v)
    break
