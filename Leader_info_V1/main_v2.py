from operator import index, le
import os
import json
import re
import time
from collections import Counter
from dbops import ManES
from utils import ROOT, name_filter, remove_parentheses, add_call_position, expand_other_titles, generate_all_titles, get_all_titles
from LeaderInfo_V1_logger import logger
import datetime
import logging

os.chdir(os.path.dirname(__file__))


concatenation_logger = logging.basicConfig(filename="/home/<USER>/leader-info/UpdateLeaderInfo/Leader_info_V1/logs/concatenation_{}.log".format(datetime.datetime.today().strftime("%Y%m%d")), filemode="w", level=logging.INFO, format="%(lineno)d - %(levelname)s - %(message)s")

concatenation_count=0

def replace(match): #保证优先选择匹配最长的pattern
    return ""

def load_es_data(obj: ManES, index_name:str='leader-info-final'):
    ''''从es leader-info-final中加载数据，items是index数据，repeat_name是重复的姓名'''
    items = []
    doc = {"_source":[], "size":50000}
    # all_res = obj.Get_Data_By_Body('leader-info-new', doc)
    all_res = obj.Get_Data_By_Body(index_name, doc)
    for item in all_res:
        items.append(item['_source'])  
    all_names = [item['eName'] for item in items]
    c = Counter(all_names) 
    temp_dict = dict(c)
    repeat_names = [n for n in temp_dict if temp_dict[n]>1]
    print("es数据条目{}，重名条目{}".format(len(items), len(repeat_names)))
    return items, repeat_names


def load_es_orgs(obj: ManES, index_name:str='organizations'):
    '''从es的organization中加载机构信息'''
    orgs = {}
    doc = {"_source":[], "size":1000}
    all_res = obj.Get_Data_By_Body(index_name, doc)
    for res in all_res:
        item = res['_source']
        if item:
            orgs[item["name"]] = item["short_name"]
            total_list = item["short_name"] +[item["name"]]
            for org in item["short_name"]:
                if not org:
                    continue
                temp_list = total_list.copy()
                temp_list.remove(org)
                orgs[org] = temp_list
    return orgs 


def confirm_person(items, repeat_names): 
#将姓名相同的人进行合并
    name_dict= {} # 存储兼职和重名人物的{name1:[{'province':p1,'url':url1,...},{'province':p2,'url':url2,...}……]}
    for item in items:
        if item['eName'] in repeat_names and item['url'] and item['ePosition']: #存在重名或兼任, 编辑职务不为空
            temp_infos = name_dict.get(item['eName'],[])
            if  temp_infos:
                #temp_titles = [t['ePosition'] for t in temp_infos] #存储编辑职务
                temp_titles = [t['url'] for t in temp_infos] #存url
                #if item['ePosition'] not in temp_titles:
                if item['url'] not in temp_titles:
                    name_dict[item['eName']] = name_dict.get(item['eName'],[]) + [item]
            else:    
                name_dict[item['eName']] = name_dict.get(item['eName'],[]) + [item]
    # 先把有url的信息添加到name_dict中，再处理无url信息的                
    for item in items:                
        if item['eName'] in repeat_names and (not item['url']) and item['province'] and item['oPosition']: #url为空，且为地方领导，province相同，进行合并
            temp_infos = name_dict.get(item['eName'],[])
            if temp_infos:
                temp_province = [t['province'] for t in temp_infos if t['province']]
                if item['province'] not in temp_province:
                    name_dict[item['eName']] = name_dict.get(item['eName'],[]) + [item]

                '''             
                # else: #记录中有相同省份，进行合并, 利用机构职务补全ePosition
                #     for t in name_dict[item['eName']]:
                #         if t['province']==item['province']:
                #             if len(t['oPosition'])>1 and  t['oPosition'][-1] in "；。，" and item['oPosition'] not in t['ePosition']:
                #                 t['ePosition'] = t['ePosition'][:-1]+'，' + item['oPosition']
                #             else:
                #                 t['ePosition'] += '，'+item['oPosition']    
                '''                   
            else:    
                name_dict[item['eName']] = name_dict.get(item['eName'],[]) + [item]        
    return name_dict


def generate_title_by_concatenation(input: dict={}, org_dict: dict={}):
    '''通过拼接的手段获取title'''
    global concatenation_count
    if not input: #input为空
        return input
    logging.info("=="*40)
    logging.info("input={}".format(input))
    output = {}
    output = input
    org_type = input.get("type", "")
    province = input.get('province', "")
    city = input.get("city", "")
    organization = input.get("organization", "")
    oposition = re.sub('^集团公司|^集团有限公司|^公司|^集团', "", input.get("oPosition", ""))
    cposition = input.get("cPosition", "")
    eposition = input.get("ePosition", "")
    if eposition:
        return output
    elif cposition:
        output["ePosition"] = cposition
    else:
        position_list = ['董事长','主席','省长','市长','区长','书记','主任','调研员','委员','会长','社长','行长','首席','监事','党组','长官','局长','部长','司长','司令','政委','专员','市长','区长','州长','盟长','县长','干部','巡视','督学','总理','常委','编辑','编委','秘书','会计','组长','牵头人','外长','成员','领导','领袖','经理','董事','总监','校长','副校长','院长','检察长','经济师,''工程师','畜牧师,''农艺师','兽医师','署长','副署长','设计师','会计师','审计师','上将','教育长','审计师','飞行师','理事','常委','书记','总书记','总编辑','工程师', '处长','局长','行长','社长','总裁','省长','常委','专员','董事','院长','校长','专务','干部','委员','教授','所长','主委','台长','关长','经理','区长','县长','政委','书记','总书记','主席','州长','市长','校长','庭长','成员','组长','参谋','参事','主任','厅长','军长','师长','旅长','团长','营长','理事长','总编辑','总队长','总经理','支队长','检查员','司令员','巡视员','调研员','检察长','监狱长','督导员','总院长','盟长','侦察员','镇长','审判员','督察长','秘书长','侦查员','经济师','首席业务创新官','专职纪检监察员','首席发展顾问','警务专员','专职评审委员','信访督查专员','特级信贷经理','首席业务经理','专职外部董事','纪检监察员','总经济师','中央委员','法人代表','第一政委','大队长','咨询委员','评审委员','副部队长','区委常委','总会计师','社务委员','资深经理','监察专员','资深专家','资深专员','第一书记','国务委员','执行董事','行务委员','巡视专员','督察员','高级警长','警长','审查员','CEO','CTO','CIO','CFO','工作人员','督办','顾问','专家','高级专家','总审计师','审计师','首席信息官','首席专家','专家','馆长','首席审计官','总地质师','地质师','高级法官','法官','总工','法定代表人','副职','正职','理事','理事长','审计长','大使','全权大使','特命全权大使','总设计师','设计师','署长','首席兽医师','首席兽医官','首席风险官','总飞行师','飞行师','编委','校（院）长','编导','总理','领导人','记者','审核','审核员','审校','主编','党组成员', "纪检监察组组长", "党组成员","直属工会主席","总工程师","党委常委",'党委书记','职工董事',"董事长", '独立董事', '执行董事','非执行董事', '总飞行师', "政治委员", "副部级专职委员",'检察官',"建筑师","销售经理","独立非执行董事",'工程师', "秘书长","保护官",'首席合规官']
        position_list = list(set(position_list))
        position_str = "|" + "|".join(position_list)
        position_str = f"(专职|常务|一级|二级|三级|四级|助理|党组|候补|党委|工会|纪委|主席团|纪检|常务委员会|财务|名誉|中将|陆军|空军|海军|总法律|政治工作部|院务|监察组|党群工作部|直属工会|审计部|人力资源部|法务|安全|风险|运行|业务|副部级专职|大|数据|外部|职工|执行|独立|市委|监事会|统战部|市委|董事会|中将|空军)?(副|总)?({position_str})(师|长|裁|员)?"
        position_pattern = re.compile(position_str)

        filered_oposition = position_pattern.sub(replace, oposition)#re.sub(position_pattern, "", oposition)
        cleaned_text = re.sub(r"[^\u4e00-\u9fa5]+", "", filered_oposition)
        logging.info("    cleaned_text={}".format(cleaned_text))
        if cleaned_text and cleaned_text!="兼" and cleaned_text!="和":
            # cleaned_text认为去掉职务和标点符号后，还有中文字符，认为oposition中含有机构信息
            output["cPosition"] = oposition
            output['ePosition'] = oposition
        else:
            if org_type not in ['地方']:
                title = "{}{}".format(organization, oposition)
            else:
                if city: #市级领导人
                    # 默认已经有cposition信息
                    if not cposition or not eposition:
                        logging.warning('市级领导人eposition为空:{}'.format(input))
                    pass
                else:
                    if organization=="中共":
                        title = "中国共产党{}委员会{}".format(province, oposition)   
                    elif organization=="政府":
                        title = "{}人民政府{}".format(province, oposition)
                    elif organization=="政协":
                        title = "中国人民政治协商会议{}委员会{}".format(province, oposition)
                    elif organization=="人大":
                        title = "{}人民代表大会常务委员会{}".format(province, oposition)
                    else:
                        # logger.warning('省委领导人机构不在四套班子里：{}'.format(input))
                        logging.warning('省委领导人机构不在四套班子里：{}'.format(input))
                        title = ""
            output["cPosition"], output["ePosition"]=title, title 
    if output["ePosition"]:
        concatenation_count+=1
    logging.info("output={}".format(output))
    logging.info("")
    return output


def make_dataset(items, name_dict, org_dict, repeat_names, esobj, index_name='leader-info-v1', use_concate_title=True):
    # items输入的是从es表中读取的数据
	# 对抓取的数据进行后处理，整理为入ES库格式(json), use_concate_title用来控制是否使用职务拼接的方法，只有True和False
    new_lines = []    
    process_names = [] #记录已处理过的姓名
    generate_data=[]
    url_num = 0
    valid_num = 0
    for subitem in items:
        if not subitem['eName']:
            continue
        name = name_filter(subitem['eName']) # 姓名信息 
        if name in ["肖从真"]:
            continue
        # if name not in ['刘中民']:
        #     continue
        if name in ["刘中民"] and subitem['url'] in [r"/item/%E5%88%98%E4%B8%AD%E6%B0%91/13768568"]:
            subitem["ePosition"] = '中国工程院院士，第十四届全国政协常委，中科院大连化物所所长，民盟辽宁省委员会主任委员'
  

        use_url = 0 #记录是否使用了URL
        if name not in repeat_names: #该姓名没有重名或兼职
            subitems = [subitem]   # subitems存储的都是没有重复的item信息
        elif name in name_dict and name not in process_names:#该姓名存在重名或兼职
            subitems = name_dict.get(name)  
            process_names.append(name)
        else:
            continue   
        

        for item in subitems:
            if item['ePosition'] and item['url']: # 存在编辑职务
                title = item['ePosition']
                title = remove_parentheses(title) # 去除职务中的括号
                use_url = 1
                url_num += 1
            else: # 不存在url, 将title设置为 机构+机构职务
                if use_concate_title==True:
                    generate_item = generate_title_by_concatenation(input=item, org_dict=org_dict)
                    generate_data.append(generate_item)
                    title = generate_item["ePosition"]
                else:
                    continue
            if item.get('leader_type', 'no') != 'no' and len(title)>4 and int(item['leader_type']) == 0:
                cp = add_call_position(title,name)
                cp = [name+c for c in cp] 
                match_text = title + name 
                # print("title:{}".format(title))
                other_titles =  expand_other_titles(title, item['province'], item['city'], org_dict)  
                # print("other_titles:{}".format(other_titles))
                
                other_titles_v1 = generate_all_titles(title, org_dict, name)
                # print("other_titles_v1:{}".format(other_titles_v1))

                jsonline = {}
                jsonline['name'] = name
                jsonline['url'] = item['url']
                jsonline['title'] = title
                if other_titles:
                    mix_titles = other_titles_v1.copy()
                    for t in other_titles:
                        if t not in other_titles_v1:
                            mix_titles.append(t)

                    jsonline['all_title'] = mix_titles 

                else:
                    jsonline['all_title'] = other_titles_v1
                
                new_all_title = []
                for iitem in jsonline["all_title"]:
                    iitem = re.sub(r'(公司)(公司)', r'\1', iitem)  # 替换 "公司公司" 为 "公司"
                    iitem = re.sub(r'(有限公司)(有限公司)', r'\1', iitem)  # 替换 "有限公司有限公司" 为 "有限公司"
                    iitem = re.sub(r'(中国)(中国)', r'\1', iitem)
                    new_all_title.append(iitem)
                jsonline["all_title"] = new_all_title
                
                jsonline['match_text'] = match_text
                jsonline['position'] = cp  
                jsonline['use_url'] = use_url
                jsonline['province'] = item['province']
                jsonline['city'] = item['city']
                jsonline["matchtext_all"]=[]
                for n in get_all_titles(title, org_dict): #增加扩展字段
                    item = n+name
                    item = re.sub(r'(公司)(公司)', r'\1', item)  # 替换 "公司公司" 为 "公司"
                    item = re.sub(r'(有限公司)(有限公司)', r'\1', item)  # 替换 "有限公司有限公司" 为 "有限公司"
                    item = re.sub(r'(中国)(中国)', r'\1', item)
                    jsonline["matchtext_all"].append(item)  
                if len(name)>1:
                    new_lines.append(jsonline) 
                    valid_num += 1
    with open("/home/<USER>/leader-info/UpdateLeaderInfo/Leader_info_V1/data/generante{}.json".format(datetime.datetime.today().strftime("%Y%m%d")), "w", encoding="utf-8") as file:
        json.dump(generate_data, ensure_ascii=False, fp=file, indent=2)
    print("生成的v1数据={}\t 含有url的条数={}".format(valid_num, url_num))
    # print("new_lines:{}".format(new_lines))
    return new_lines



def update_es(index_name, lines, esobj):
    if esobj.has_index(index_name):
        esobj.index_name = index_name
    else:
        cmd_index_str = f'/home/<USER>/miniconda3/envs/leader_check/bin/python {ROOT}/es-tool.py index -name leader-info-v1 -mapping {ROOT}/data/leader-info-v1-mapping.json'
        os.system(cmd_index_str)
    if esobj.index_name == index_name:
        esobj.clear() # 清空原表数据
        esobj.add_case(lines) # 添加新数据
        timestr = str(time.localtime(time.time()))
        with open(f'{ROOT}/es_build.record', 'w', encoding='utf-8') as f:
            f.write(timestr)

def backup_historical_data(obj:ManES, index_name, historical_path = "history"):
    '''根据index备份历史数据到文件夹history下'''
    data = obj.Get_Data_By_Body(index_name=index_name)
    history_data = []
    for item in data:
        history_data.append(item)
    today_str = datetime.datetime.today().strftime("%Y%m%d")
    history_file = os.path.join(historical_path, "{}_{}.json".format(index_name,today_str))
    with open(history_file, "w", encoding="utf-8") as fp:
        json.dump(obj=history_data, fp=fp, ensure_ascii=False, indent=4)
    return history_data

def compare(file1, file2):
    with open(file1, "r", encoding="utf-8") as fp1:
        file1_data = json.load(fp1)
    with open(file2, "r", encoding="utf-8") as fp2:
        file2_data = json.load(fp2)
    different_data = []
    for item in file1_data:
        name = item["_source"]["name"]
        _source1 = item["_source"]
        tongmings_in_f2 = [new_item["_source"] for new_item in file2_data if new_item["_source"]["name"]==name and new_item["_source"]["title"]==_source1["title"]]
        for tongming in tongmings_in_f2:
            if sorted(tongming["all_title"])==sorted(_source1["all_title"]):
                continue
            else:
                info = {"s1": _source1, "s2":tongming}
                different_data.append(info)

    with open("/home/<USER>/leader-info/UpdateLeaderInfo/Leader_info_V1/data/differnent.json", "w", encoding="utf-8") as fp:
        json.dump(different_data, ensure_ascii=False, indent=2, fp=fp)

# compare(file1="/home/<USER>/leader-info/UpdateLeaderInfo/Leader_info_V1/history/leader-info-v1_20241121.json", file2="/home/<USER>/leader-info/UpdateLeaderInfo/Leader_info_V1/history/leader-info-v1_20241120.json")
            

if __name__=="__main__":
    
    logger.info('开始更新leader-info-v1')
    obj = ManES('leader-info-v1', '_doc')
    items, repeat_names = load_es_data(obj, index_name='leader-info-final')

    org_dict = load_es_orgs(obj)
    logger.info(f'读取机构: {len(org_dict)}')

    '''备份历史数据，为了防止数据突然变化，便于用历史数据回退'''
    leader_info_final = backup_historical_data(obj=obj, index_name="leader-info-final", historical_path="history")
    leader_info_v1 = backup_historical_data(obj=obj, index_name="leader-info-v1", historical_path="history")
    logger.info(f"备份leader-info-final数据：{len(leader_info_final)}")
    logger.info(f"备份leader-info-v1数据：{len(leader_info_v1)}")

    logger.info(f'读取领导人: {len(items)}')
    logger.info(f'重复人名: {len(repeat_names)}')

    name_dict = confirm_person(items, repeat_names)
    logger.info(f'合并重复人名: {len(name_dict)}')
    names = name_dict.keys()
    # print(names)
    print("len(names)=", len(names))
    new_lines = make_dataset(items, name_dict, org_dict, repeat_names, obj, use_concate_title=True)
    logger.info(f'生成数据集: {len(new_lines)}')
    # print(f'生成数据集: {len(new_lines)}')
    logging.info("concatenation count={}".format(concatenation_count))
    # print("concatenation=",concatenation_count)
    with open("data/{}.json".format(datetime.datetime.today().strftime("%Y%m%d")), "w", encoding="utf-8") as fp:
        json.dump(new_lines, fp=fp, ensure_ascii=False, indent=2)

    update_es('leader-info-v1', new_lines, obj)
    logger.info('leader-info-v1更新完成')

