import os
import sys
import functools
import datetime
import time
import logging
from logging.handlers import WatchedFileHandler
from logging import FileHandler, Filter

# import formatter


class TimedRotatingFileHandler(WatchedFileHandler):
    def __init__(self, log_dir='./logs', mode="a", encoding=None, delay=False, errors=None):
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        self.log_dir = log_dir
        self.file_name = "{}.log".format(datetime.datetime.now().strftime("%Y-%m-%d"))

        file_name = os.path.join(log_dir, self.file_name)
        super().__init__(file_name, mode=mode, encoding=encoding, delay=delay)

    def emit(self, record):
        current_file_name = "{}.log".format(
            datetime.datetime.now().strftime("%Y-%m-%d")
        )
        if current_file_name != self.file_name:
            self.file_name = current_file_name
            self.baseFilename = os.path.abspath(
                os.path.join(self.log_dir, current_file_name)
            )
            if self.stream:
                self.stream.flush()
                self.stream.close()
            self.stream = self._open()
            self._statstream()

        self.reopenIfNeeded()
        super().emit(record=record)


class ContextFilter(Filter):
    """
    session 级别的上下文追踪， 因为 logging 是单例模式， 所以 在__init__.py取出指定的logger
    实例， 注册一次就可以了， 然后再session 开始的时候， 更新一下 sessionId 就可以了
    """
    sessionId = "session_unknown"

    def init(self, session_id=""):
        self.sessionId = session_id if session_id else str(int(time.time()*1e6))

    def filter(self, record):
        record.sessionId = self.sessionId
        return True
    
class Logger2(logging.Logger):
    def __init__(self, name='', log_dir=None, level='info'):
        if name == '':
            name = 'mylogger'
            
        levels = {
            'info': logging.INFO,
            'debug': logging.DEBUG,
            'warning': logging.WARNING,
            'error': logging.ERROR,
        }
        level = levels[level]
        super().__init__(name, level)

        # 创建log文件保存路径
        if log_dir is None:
            log_dir = os.path.join(os.path.dirname(__file__), 'logs')
        os.system(f'mkdir -p {log_dir}')
        # 创建log文件，并规定写入格式
        log_file = os.path.join(log_dir, "leaderinfo-V1.log")
        print('###logfile=', log_file)
        file_handler = logging.FileHandler(filename=log_file, mode='a',encoding='utf-8')
        file_handler.setLevel(level=level)

        logformatter = logging.Formatter('%(name)s| %(levelname)s|%(asctime)s|%(filename)s:%(lineno)d】%(message)s', datefmt="%H:%M:%S")
        file_handler.setFormatter(logformatter)

        self.addHandler(file_handler)


    
class Logger(logging.Logger):
    def __init__(self, name='', log_dir=None, level='info'):
        if name == '':
            name = 'logger'
            
        levels = {
            'info': logging.INFO,
            'debug': logging.DEBUG,
            'warning': logging.WARNING,
            'error': logging.ERROR,
        }
        level = levels[level]
        super().__init__(name, level)

        if log_dir is None:
            log_dir = os.path.join(os.path.dirname(__file__), 'logs')
        os.system(f'mkdir -p {log_dir}')
        
        
        self.context_filter = ContextFilter()
        self.addFilter(self.context_filter)

        if not self.handlers:
            formatter = logging.Formatter('%(name)s| %(levelname)s|%(asctime)s|%(filename)s:%(lineno)d】%(message)s', "%H:%M:%S")
            fh = TimedRotatingFileHandler(log_dir)
            # fh = logging.FileHandler(filename)
            fh.setLevel(level)
            fh.setFormatter(formatter)
            self.addHandler(fh)
            # logger.info(f"log file: {filename}")

            # ch = logging.StreamHandler()
            # ch.setLevel(logging.WARNING)
            # ch.setFormatter(formatter)
            # logger.addHandler(ch)

    def new_session(self, session_id=''):
        self.context_filter.init(session_id)


def get_func_chain():
    frame_stack = []
    current_frame = sys._getframe()
    while current_frame:
        name = current_frame.f_code.co_name
        if name not in ["<module>", "wrapper", "get_func_chain"]:
            frame_stack.append(name)
        current_frame = current_frame.f_back
    return frame_stack[::-1]

def get_call_filename():
    name = sys._getframe().f_back.f_back.f_code.co_filename
    return name.split('/')[-1]


def log_timer(func):
    @functools.wraps(func)
    def wrapper(*args, **kargs):
        s = time.time()
        #ss = int(s*1e7)
        #app_logger.info("Entering {}() uniqid:{} chain:{}".format(func.__name__, ss, get_func_chain()+"->"+func.__name__))
        f = func(*args,**kargs)
        e = time.time()
        chain = get_func_chain()
        chain.append(func.__name__)
        chain = ".".join(chain)
        name = get_call_filename()
        logger.info("{}:{} cost: {:.3f}s".format(name, chain, (e-s)))
        return f
    return wrapper

log_dir = os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')), r"runtime/{}/logs".format(datetime.datetime.today().strftime("%Y%m%d")) )#"/home/<USER>/leader-info/UpdateLeaderInfo/runtime"
logger = Logger2('LeaderInfo_V1_runner', level='info', log_dir=log_dir)
