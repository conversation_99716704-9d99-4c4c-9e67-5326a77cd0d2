from operator import itemgetter
import os
import json
import itertools
import time
import re
from dbops import ManES
from analysis_title_str import TitleAnalyzer

ROOT = os.path.abspath(os.path.dirname(__file__))

call_positions2 = ['专员', '总理','主任', '主席', '书记', '代表' ,'会长', '区长','县长', '司长', '委员', 
                  '局长' ,'州长', '市长', '政委', '校长', '法官', '理事', '盟长', '省长', '督察', '社长',
                   '行长', '部长', '院士', '院长', '将军', '台长']
call_positions3 = ['司令员', '审计长', '总书记','总指挥', '总督察', '总经理', '总编辑', '总警监','理事长', 
 '督察长', '研究员', '秘书长', '董事长','委员长','检察长']

special_titles =  ['党组书记','党组成员','党组副书记','党委副书记','党委委员','党委书记','机关党组书记']

key_pos = ["主席","省长","书记","主任","委员", "会长","党组书记","党组成员", "党委书记", "行政长官", '院长', '校长',
"局长","部长","市长","总理","常委","总编辑","秘书长","组长","总书记","牵头人","外长","成员","领袖","总经理","委员长",'检察长','董事长']
#对于特殊人物设置 默认的称谓
key_pos_v1 = ["主席","书记","主任","委员", "会长","党组书记","党组副书记","党组成员", "党委书记", "党委副书记", "行政长官", '院长', '校长', '主委',
"局长","部长","总理","常委","总编辑","秘书长","组长","总书记","外长","组长","领袖","总经理","委员长",'检察长','董事长'] # 非地方的职务名称

sp_name_dict = {'李强': '总理', '赵乐际': '委员长', '王沪宁':'主席',
'韩正': '副主席',
}

provinces = ['河北省', '山西省', '辽宁省', '吉林省', '黑龙江省', '江苏省', '浙江省', '安徽省', '福建省', '江西省', '山东省', '河南省', '湖北省', '湖南省', '广东省', '海南省', '四川省', '贵州省', '云南省', '陕西省', '甘肃省', '青海省',
'内蒙古自治区', '广西壮族自治区', '西藏自治区', '宁夏回族自治区', '新疆维吾尔自治区','北京市', '天津市', '上海市', '重庆市']

province_short_names = [p[:2] for p in provinces]

def load_city_names():
    with open(f'{ROOT}/data/city_names.txt', 'r') as f:
    # with open('/home/<USER>/work/LeaderDataUpdate/data/city_names.txt', 'r') as f:
        content = f.read()
    city_dict = json.loads(content)    
    city2province = {}
    for p in city_dict:
        if p[-1] in "省市":
            pro_name = p[:-1]
        elif p[-3:] == "自治区":
            pro_name = p[:-3]
        else:
            pro_name = ''
        if pro_name:
            for cname in city_dict[p]:
                city2province[cname[:2]] = pro_name
    all_city = []
    for clist in city_dict.values():
        all_city += clist
    return all_city, city2province

all_city, city2province = load_city_names()

def name_filter(name):
    '''去掉name开始部分含有部长、主席、主任等职务信息，结束部分有( , \u3000 。等特殊字符的'''
    start_stops = ['_','部长',"主席","主任"]
    end_stops = ['（',' ','\u3000','。']
    for s in start_stops:
        if name and s in name:
            new_start = name.index(s)
            name = name[new_start+len(s):]
    for s in end_stops:
        if name and s in name:
            end = name.index(s)
            name = name[:end]      
    if len(name)>4 and "·" not in name:
        name = ''          
    return name

def remove_parentheses_legacy(text):
    #去括号
    remove_pair = ['()', '（）']
    res = text
    if not text:
        return text
    for r in remove_pair:
        start = text.find(r[0])
        if start != -1:
            en = text.rfind(r[1])
            
            if en != -1:
                
                res = text[0:start] + text[en+1:]
            else:
                res = text[0:start]
            return res
        
    return text

def remove_parentheses(text:str):
    if not text:
        return text
    ret = re.sub("[（\()].*?[）\)]", "", text)
    ret = re.sub("\[(.*?)\]","", ret)
    return ret

def add_call_position(title,name):
# 增加称谓信息，如李克强总理
    cp = []
    title = remove_parentheses(title) #去括号
    title = title.replace('、','，')
    title = title.replace('兼','，')
    title_list = []
    subtitles = title.split('，')
    for t in subtitles:
        title_list.append(t)
        if "、" not in t:
            if t[-2:] in call_positions2 and t[-3:] not in call_positions3:
                if len(t)>2 and t[-3] =="副":
                    cp.append(t[-3:])
                else:
                    cp.append(t[-2:])
            elif  t[-3:] in call_positions3:
                if len(t)>3 and t[-4] =="副":
                    cp.append(t[-4:])
                else:
                    cp.append(t[-3:])
        else:
            subt = t.split("、")
            for st in subt:
                if st[-2:] in call_positions2 and st[-3:] not in call_positions3:
                    if len(st)>2 and st[-3] =="副":
                        cp.append(st[-3:])
                    else:
                        cp.append(st[-2:])
                elif  st[-3:] in call_positions3:
                    if len(st)>3 and st[-4] =="副":
                        cp.append(st[-4:])
                    else:
                        cp.append(st[-3:])     
        # 特殊规则，外交部长简称外长
        if t == '外交部部长' :    
            cp.append('外长')
    cp = list(set(cp)) # 去重复

    if name in sp_name_dict and sp_name_dict[name]!=cp[0]: # 将关键人物的称谓列表重新排序
        if sp_name_dict[name]in cp: # 重新排序
            temp_cp = cp.copy()
            temp_cp.remove(sp_name_dict[name])
            cp = [sp_name_dict[name]] + temp_cp
        # else:
        #     print(name, title)    
    return cp

def match_pos(i, sentence):
    #判断文本sentence第i个字向前，是否为职务名(在key_pos中)
    # 返回职务名的长度 “部长”：2， “总编辑”：3
    maxl = 0
    pos = ['副'+k for k in key_pos]
    pos += special_titles
    pos += key_pos
    pos = list(set(pos))
    for l in range(2,6):
        if sentence[i-l: i] in pos:
            if l > maxl:
                maxl = l           
    return maxl 

def expand_special_titles_v1(correct_titles):
    #特殊处理党组书记、党委成员等名称
    new_correct_titles= []
    insert_time =0  # 列表插入次数
    i = 0 
    ct = '、'.join(correct_titles)
    has_sepcial = [s in ct for s in special_titles]
    #for i, ct in enumerate(correct_titles):

    if sum(has_sepcial): #包含特殊职务
        if "兼" in ct and "兼"!=ct[0] and '兼任' not in ct: #单独的‘兼’替换为顿号
            ct0 = ct.replace('兼','、')
        else:
            ct0 = ct    
        sub_titles = ct0.split('、')
        # 寻找词根
        l = match_pos(len(sub_titles[0]), sub_titles[0])
        if  l:
            root = sub_titles[0][:-l]#词根  
            if len(root)>2: #防止出现国务委员、国务院党组成员类型错误         
                add_titles = [sub_titles[0][-l:]] + sub_titles[1:]
                for t in add_titles:
                    if t not in new_correct_titles:
                        new_correct_titles.insert(i+insert_time, t)
                        insert_time += 1
                    if root+t not in new_correct_titles:    
                        new_correct_titles.insert(i+insert_time, root+t)
                        insert_time += 1  
                if ct in new_correct_titles:
                    new_correct_titles.remove(ct)  
                    insert_time -= 1   
            else:
                new_correct_titles += sub_titles  
    return new_correct_titles

def process_title_num(t):
    # 处理职务中的第XXX届
    titles = []
    if "届" in t:#e.g.第十九届中央政治局委员
        cut_end = t.index("届")
        idx = cut_end - 1
        while(t[idx].isnumeric() or t[idx]=="第"):
            idx -= 1   
        if idx > 0:    
            titles.append(t[:idx+1] + t[cut_end+1:])
        else:
            titles.append(t[cut_end+1:])
        if t[idx+1]=="第":
            if t[idx+2:] not in titles:
                titles.append(t[idx+2:])
            if t[:idx+1] + t[idx+2:] not in titles:
                titles.append(t[:idx+1] + t[idx+2:])
        if t[idx+1].isnumeric():
            if '第'+t[idx+1:] not in titles:
                titles.append('第'+t[idx+1:])  
    elif "任" in t: #第XXX任行政长官
        cut_end = t.index("任")
        if cut_end > 1 and t[cut_end - 1].isnumeric():
            idx = cut_end - 1
            while(t[idx].isnumeric() or t[idx]=="第"):
                idx -= 1
            if idx > 0:    
                titles.append(t[:idx+1] + t[cut_end+1:])
            else:
                titles.append(t[cut_end+1:])
 
    return titles 

def expand_title_str(t0, is_local = False):
    correct_titles_temp = t0.split('，') # 按照逗号切分职务
    correct_titles = []
    for subt in correct_titles_temp:
        has_special = [s in subt for s in special_titles]
        if sum(has_special) and not is_local: # 包含党组、党委等特殊职务     
            sublist = subt.split('、')
            expans = expand_special_titles_v1(sublist)
            for t in expans:
                if t not in correct_titles:
                    correct_titles.append(t)
        else: #无需特殊扩展
            # 对于部分职务信息使用顿号切割
            if "、" in subt:
                correct_titles += subt.split('、')
            # if  "、秘书长" in c:
            #     correct_titles += c.split('、')
            elif "兼" in subt and "兼"!=subt[0]: #现任中含有“兼”
                correct_titles += subt.split('兼')    
            else:
                correct_titles.append(subt)
    temp_titles = correct_titles.copy()
    insert_time = 0
    for i, t in enumerate(temp_titles):
        if process_title_num(t):
            for pt in process_title_num(t):
                if pt not in correct_titles:
                    correct_titles.insert(i+insert_time, pt)
                    insert_time += 1
    return correct_titles

def expand_province_titles(correct_titles, province, city):  
    '''扩展省长/市长别称, 扩展职务名称，特别是与省长、市长以及其他相关职务的称呼相关。根据输入的省份和城市信息，它会根据一些规则将职务名称扩展为不同的变体。'''
    orgs = ["省政府省长","省人民政府省长","省长"] # 省长别称list
    org_dict = {o: [os for os in orgs if os!=o] for o in orgs}
    c_orgs = [o.replace('省','市') for o in orgs] # 市长别称list
    f_orgs = [o[:-2]+'副'+ o[-2:] for o in orgs] # 副省长
    fc_orgs = [o[:-2]+'副'+ o[-2:] for o in c_orgs] # 副市长
    for co in c_orgs:
        org_dict[co] = [os for os in c_orgs if os!=co]
    for co in f_orgs:
        org_dict[co] = [os for os in f_orgs if os!=co]
    for co in fc_orgs:
        org_dict[co] = [os for os in fc_orgs if os!=co]    

    titles = correct_titles.copy()  
    insert_time = 0      
    # 扩展省长名称
    for i, item in enumerate(correct_titles):   
        match_str = item[2:] if item[:2]==province[:2] else item
        if match_str in org_dict:
            insert_items = org_dict.get(match_str)
            
            for it in insert_items:
                if it not in titles:
                    titles.insert(insert_time + i, it)
                    insert_time += 1
    final_titles = titles.copy() # 增加省份名称
    #final_titles = correct_titles.copy()  
    insert_time = 0
    for i, t in enumerate(titles):
        if t[:len(province)]==province:
            if city=='': #扩展省委、省人大、省政协的省份名称, ‘河北省人大委员会主任’缩写‘省人大委员会主任’，‘省政协主席’加入列表
                if t[len(province)-1:] not in final_titles:
                    short = 1 if province[-1] in ['省','市'] else 3 # 缩写省市、自治区
                    # if t[-2:] in ['省长', '市长']:
                    #     short = 0 
                    final_titles.insert(i+insert_time, t[len(province)-short:])
                    insert_time += 1
            elif city and t[:len(province+city)]==province+city:# ‘河北省邯郸市人大委员会主任’缩写‘市人大委员会主任’‘邯郸市人大委员会主任’
                if t[len(province+city)-1:] not in final_titles:
                    final_titles.insert(i+insert_time,  t[len(province+city)-1:])
                    insert_time += 1
                if t[len(province):] not in final_titles:
                    final_titles.insert(i+insert_time,  t[len(province):])
                    insert_time += 1
                
        if len(t)>3 and (t[1:3] in ['人大','政协'] or t[1] in '纪委监'): #扩展省委、省人大、省政协、省纪委，省监委的省份名称,没有省份，将省份加上
            if city =='' and province[-1] == t[0]:#省直
                if province[:-1]+t not in final_titles:
                    final_titles.insert(i+insert_time, province[:-1]+t)
                    insert_time += 1
            elif city and city[-1] == t[0]:
                if city[:-1]+t not in final_titles:
                    final_titles.insert(i+insert_time, city[:-1]+t)
                    insert_time += 1
                if province+city[:-1]+t not in final_titles:
                    final_titles.insert(i+insert_time, province+city[:-1]+t)
                    insert_time += 1
        if t[-2:] in ['省长', '市长'] and len(t) <4: #最后两字为‘省长’，即‘省长’或者‘副省长’单独出现，扩展为‘XX省省长’
            add_strs = [province + city, city ] if city else [province] #区分省领导和地级市领导
            for add_str in add_strs:
                if add_str+t not in final_titles:
                    final_titles.insert(i+insert_time, add_str+t)
                    insert_time += 1
        if t[-2:] in ['省长', '市长'] and len(t) >=4 and t[0] in ['省','市']:  #最后两字为‘省长’，即‘省政府省长’, 扩展为‘XX省政府省长’
            add_strs = [province + city, city ] if city else [province] #区分省领导和地级市领导
            for add_str in add_strs:
                if add_str[:-1]+t not in final_titles:
                    final_titles.insert(i+insert_time, add_str[:-1]+t)
                    insert_time += 1
        if  t[:3]=='自治区' and city == '': # 职务为“自治区人大常委会主任”，将“XXX自治区人大常委会主任”加入
            if province[:-3]+t not in final_titles:
                final_titles.insert(i+insert_time, province[:-3]+t)
                insert_time += 1
    
    for i, t in enumerate(final_titles):
        if '人民政府' in t: # 将人民政府 替换为 政府 和缺省
                other_name = t.replace('人民政府','政府')
                if other_name not in final_titles:
                    final_titles.insert(i, other_name)
                    insert_time += 1

    # insert_time = 0 
    for s in special_titles[:3]: #适用于党组成员和党组书记的扩展, 党组书记 单独出现
        add_strs = [province + city, city ] if city else [province] #区分省领导和地级市领导
        s1 = add_strs[0][-1] + '政府' + s 
        if s in titles:
            _idx = final_titles.index(s)       
            for add_str in add_strs:
                final_titles.insert(_idx, add_str + '人民政府'+ s) #XXX省人民政府党组书记（成员）
                final_titles.insert(_idx, add_str + '政府'+ s) #XXX省政府党组书记（成员）
                final_titles.insert(_idx, add_str[-1] + '政府'+ s) #简写‘市政府党组书记’
                final_titles.insert(_idx, add_str + s)
        elif s1 in titles: #适用于 省政府党组书记
            _idx = final_titles.index(s1)
            for add_str in add_strs:
                final_titles.insert(_idx, add_str + '人民政府'+ s1[3:]) #XXX省人民政府党组书记（成员）
                final_titles.insert(_idx, add_str[:-1] + s1) #XXX省政府党组书记（成员）
                final_titles.insert(_idx, add_str + s1[3:]) #XXX省政府党组书记（成员）
                final_titles.insert(_idx, s1[3:]) # 简写 党组书记



    return final_titles

def get_all_titles(title, org_dict):
    '''通过扩展机构名，获得简称title的扩展名'''
    '''通过查找每个机构简称在 org_dict 中的扩展名称，并根据一定的规则（如简称长度、包含 "、" 或 "兼" 等）生成所有可能的扩展名称组合。最终返回一个包含所有扩展名称的列表。'''
    sub_title = title.split("，")
    sim_title = []
    for i, t in enumerate(sub_title):                 
        sim_title.append([t])        
        for org in org_dict:
            if org not in t:
                continue
            else:
                if len(t)-len(org) <= 4:
                    other_name = [t.replace(org, s) for s in org_dict[org] ]
                    sim_title[i] += other_name
                elif "、" in t:
                    other_name = [t.replace(org, s) for s in org_dict[org] ]
                    sim_title[i] += other_name
                elif "兼" in t[len(org)+3:len(org)+5]:
                    other_name = [t.replace(org, s) for s in org_dict[org] ]
                    sim_title[i] += other_name    
    strs = ""
    if len(sim_title)>1:
        for item in sim_title:
            strs += str(item) + "," 
        all_titles = []
        for s in itertools.product(*eval(strs[:-1])):
            all_titles.append("，".join(list(s)))
    else:
        all_titles = sim_title[0]
    return all_titles    

def expand_other_titles(t0, province, city, org_dict):
     # 扩展后的正确职务列表
     # 将库中正确职务的字符串扩展为title list,同时特殊处理‘党组书记、部长’兼职类型的职务
    #如为地方领导，特殊处理省长、市长等 
    other_titles_temp = [] # 扩展后的正确职务列表
    if province and province[-1] in '省市区': #省政府、直辖市特殊处理,
        correct_titles = expand_title_str(t0, is_local=True)
        correct_titles = expand_province_titles(correct_titles, province, city)
    else:
        correct_titles = expand_title_str(t0)

    for i, ct in enumerate(correct_titles):
        other_titles_temp.append(ct)
        other_names = get_all_titles(ct, org_dict)
        for o in other_names:
            if o not in other_titles_temp:
                other_titles_temp.append(o)
    other_titles = other_titles_temp.copy()
    for i, ct in enumerate(other_titles_temp):  
        if ct=='外交部部长':
            other_titles.insert(i, '外长')
        if ct=='中央政治局常委':
            other_titles.insert(i, '中央政治局常务委员会委员')
            other_titles.insert(i, '中共中央政治局常务委员会委员')
        if ct in ['国家主席', '国家副主席']:
            other_titles.insert(i, '中国'+ct)
    ## 刘中民，中国科学院大连化学物理研究所所长        
    if "中科院大连化物所所长" in other_titles and "中国科学院大连化学物理所所长" not in other_titles:
        other_titles.append('中国科学院大连化学物理研究所所长') 
    return other_titles

def trigger(strs, tlist):
    #触发函数, 返回命中词
    for t in tlist:
        if t in strs:
            return t 
    return ""

# 配置常量 - 建议提取到配置文件或类常量
ORG_TRIGGERS = {
    'party': ["委", "党委"],
    'party_sub': ["组织部", '宣传部', "统战部", "纪委", "政法委"],
    'government': ["政府", "省长", "市长", "区长"],
    'government_sub': ["办公厅", "机关"],
    'congress': ["人大常委会", "人民代表大会"],
    'cppcc': ["政协", "政治协商会议"],
    'university': ["大学", "学院", "委员会", "人大", "人民代表大会"],
    'enterprise': ["公司", "集团", "银行"],
    'military': ["兵团", "军区", "警备区"],
    'city': ["市", "区", "地区", "自治州"],
    'democratic_parties': ['民革', '民盟', '民建', '民进', '农工党', '致公党', '九三学社', '台盟', '主委']
}

def analysis_title_str(title_str, name=""):
    # 将复杂的中文职务名称字符串解析为结构化的数据格式，输出包含省份、城市、机构和职位信息的字典列表。
    """
    将字符串格式的职务名称解析为结构化数据格式

    Args:
        title_str (str): 职务名称字符串
        name (str): 人员姓名（可选）

    Returns:
        list: 包含{"province", "city", "org", "position"}字典的列表
    """
    if not title_str or not title_str.strip():
        return []

    title_res = []

    # 使用配置常量
    org1_trigger = ORG_TRIGGERS['party']
    org1_sub = ORG_TRIGGERS['party_sub']
    org2_trigger = ORG_TRIGGERS['government']
    org2_sub = ORG_TRIGGERS['government_sub']
    org3_trigger = ORG_TRIGGERS['congress']
    org4_trigger = ORG_TRIGGERS['cppcc']
    u_trigger = ORG_TRIGGERS['university']
    c_trigger = ORG_TRIGGERS['enterprise']
    j_trigger = ORG_TRIGGERS['military']
    city_trigger = ORG_TRIGGERS['city']
    p_trigger = ORG_TRIGGERS['democratic_parties']

    org_all = ["省委", "市委", "区委","州委", "盟委"] + [org2_trigger[0]] + org3_trigger + org4_trigger + ['主委']

    def remove_d(strs):
        # 去除字符串中使用不当的顿号，统一替换为逗号
        # 从右往左遍历
        if not trigger(strs, u_trigger) and not trigger(strs, c_trigger):
            end_idx = len(strs)
            for i in range(len(strs)-1,-1, -1):
                if strs[i] =='、' and (trigger(strs[i+1:end_idx],org_all) or trigger(strs[i+1:end_idx],p_trigger) or len(strs[i+1:end_idx])>6) :
                    strs = strs[:i] + '，' + strs[i+1:]
                    end_idx = i
                if strs[i] in '，、':
                    end_idx = i  
                if strs[i]=='兼' and strs[i-1] not in '，、' and strs[i+1]!='职': # 单独处理“兼”
                    strs = strs[:i] + '，' + strs[i+1:]
                    end_idx = i
        return strs        

    title_str = remove_d(title_str)
    # sub_list = title_str.split('，')
    sub_list = re.split("[，、]", title_str)
    # print(title_str, sub_list)
    for k, sub_str in enumerate(sub_list):
         #每个子职务生成一个dict
        res = {"province":"", "city":"", "org":"", "position":[]}
        if sub_str[:2] in province_short_names and not trigger(sub_str, u_trigger)  and not trigger(sub_str, p_trigger) and not trigger(sub_str, j_trigger): 
            # 包含省份名称，进入地方四套班子职务解析， 排除高校、民主党派、军区的干扰
            pidx = province_short_names.index(sub_str[:2])
            province_name = provinces[pidx]
            province_len = 0 
            if province_name[-1] in "省市": # 普通省份
                res["province"] = province_name[:-1]
                province_len = 1  # 省名称后的字符长度，1或3
            elif province_name[-3:] == '自治区':
                res["province"] = province_name[:-3]
                province_len = 3
            # 地区名称的字符串长度
            local_len = len(res["province"])+province_len
            org_start = len(res["province"])
            c_char = trigger(sub_str[local_len:], city_trigger)
            if c_char and c_char != province_name[-1]: # 包含地级市
                hit_char = trigger(sub_str[local_len:], city_trigger)
                end_idx = sub_str.index(hit_char)
                res["city"] = sub_str[local_len:end_idx]
                local_len += len(res["city"]) + len(hit_char)
                org_start = local_len - len(hit_char)
                province_len = len(hit_char)
            for i in range(4): # 四套班子匹配
                trigger_name = 'org' + str(i+1) +'_trigger'
                trigger_name= eval(trigger_name)
                trigger_word = trigger(sub_str[local_len:], trigger_name)

                if trigger_word and trigger_word not in org2_trigger: # trigger 命中机构名， 非政府
                    end_idx = sub_str.index(trigger_word) + len(trigger_word)
                    res["org"] = sub_str[org_start:end_idx]
                    if "、" in sub_str[end_idx+1:]:
                        res["position"] = sub_str[end_idx:].split("、")
                    else:
                        res["position"] = [sub_str[end_idx:]]
                    # 针对不同的机构进行后处理
                    if i==0 and trigger(sub_str, org1_sub): # 地方党委, 出现分支机构，分支机构单列 e.g.
                        new_res = res.copy()
                        sub_org = trigger(sub_str, org1_sub)
                        end_idx = sub_str.index(sub_org ) + len(sub_org)
                        new_res['org'] = res['org'] + sub_org
                        new_res["position"] = [sub_str[end_idx:]]
                        title_res.append(new_res)
                        # 删除原结果中的职务
                        for p in res["position"]:
                            if sub_org in p:
                                res["position"].remove(p)

                elif trigger_word in org2_trigger: # 地方政府的缩写， trigger 命中职务名
                    if trigger_word in ["政府"]:
                        org_end_idx = sub_str.index(trigger_word) + len(trigger_word)
                        res["org"] = sub_str[org_start:org_end_idx]
                        end_idx = org_end_idx
                    else: # 命中词为省长
                        res["org"] = sub_str[org_start:org_start+province_len] + '政府'
                        end_idx = org_start+province_len
                    if "、" in sub_str[end_idx+1:]:
                        res["position"] = sub_str[end_idx:].split("、")
                    else:
                        res["position"] = [sub_str[end_idx:]]
        
        elif trigger(sub_str, u_trigger+c_trigger) : # 中管高校, 央企， 党委不再单列
            hit = trigger(sub_str, u_trigger+c_trigger) # 大学 或 公司
            end_idx =  sub_str.index(hit) + len(hit)
            res["org"] = sub_str[:end_idx]
            res["position"] = sub_str[end_idx:].split('、')

        elif k>0 and trigger(sub_list[k-1], p_trigger): # 前一个职务为民主党派职务， 顿号缩写
            if sub_str[:2] in province_short_names:
                party_name = trigger(sub_list[k-1], p_trigger)# 获取民主党派的简称
            else:
                party_name = ''
            l = match_pos(len(sub_str), sub_str)
            end_idx = len(sub_str)-l
            res["org"] =  party_name+sub_str[:end_idx]
            res["position"].append(sub_str[end_idx:])

        else: # 中央或其他非地方职务
            idx = len(sub_str)
            end_idx = idx
            while(idx>=0):
                l = match_pos(idx, sub_str)
                if l:
                    res["position"].append(sub_str[idx-l:idx])
                    end_idx = idx-l
                    idx = idx - l 
                else:
                    idx = idx -1 
                if "兼" in sub_str and re.findall(r"兼(?!职)", sub_str):
                    continue
                else:
                    break
            res["org"] = sub_str[:end_idx]
            res["org"] = re.sub("兼任", "", res["org"])
            res["position"] = res["position"][::-1]
            # 当机构名以省、市开头时，补全省的名称 e.g "湖北省委常委，省政府省长"
            if res["org"] and title_res and title_res[-1]["province"] and (res["org"][0] in ["省", "市"] or res["org"][:3]=='自治区'):
                res["province"] = title_res[-1]["province"]
                res["city"] = title_res[-1]["city"]
                trigger_word = trigger(sub_str, org2_trigger)
                if trigger_word in org2_trigger: # 地方政府的缩写， trigger 命中职务名
                    if trigger_word == "政府":
                        org_end_idx = sub_str.index(trigger_word) + len(trigger_word)
                        res["org"] = sub_str[:org_end_idx]
                        end_idx = org_end_idx
                    else:
                        res["org"] = sub_str[org_start:org_start+province_len] + '政府'
                        end_idx = org_start+province_len
                    if "、" in sub_str[end_idx+1:]:
                        res["position"] = sub_str[end_idx:].split("、")
                    else:
                        res["position"] = [sub_str[end_idx:]]
            # 前面为省直职务，后面为地市职务，可能会省略省份名称 如 湖北省委常委，武汉市委书记
            elif len(res["org"])>2 and title_res and res["org"][:2] in city2province and title_res[-1]["province"] == city2province.get(res["org"][:2]):
                city_name = [c for c in all_city if c[:2] == res["org"][:2]]
                if city_name:
                    res["province"] = title_res[-1]["province"]
                    res["city"] = city_name[0][:-1]
                    res["org"] = res["org"][len(city_name[0][:-1]):]
            
        if res["org"] and res["position"] and (res not in title_res):
            title_res.append(res)
            # if len(res["position"])>1:
            #     print("\n", name.strip())
            #     print(title_str)
            #     print(r"{} : {}".format(sub_str, res))
            # if name in ["康旭平"]:
            #     print("-----",name)
            #     print(r"{} : {}".format(sub_str, res))
    return title_res

def normalize_org(res_list):
    # 标准化输出，别名 便于统计结果
    # 统一政府名称
    # 统一position排序方法
    # position 有多个，进行拆解
    norm_list = []
    norm_dict = {"省政府": "省人民政府",  "市政府": "市人民政府", "中央委员会": "中央",
                 "中共中央": '中央'}
    for res in res_list:
        norm_res = res.copy()
        if norm_res.get('org'):
            #norm_res['org'] = filt_title_num(res['org'])
            if norm_res['org'] in norm_dict:
                norm_res['org'] = norm_dict[norm_res['org']]
        if norm_res.get('position') and len(res['position'])>1: # 进行拆解
            for pos in res['position']:
                norm_list.append({"province":res["province"].strip(), "city":res["city"].strip(), "org":norm_res['org'].strip(), "position":[pos.strip()]})
        else:
            norm_list.append(norm_res)
    return norm_list

def expand_local_title(res):
    # 结合省市名称扩展四套班子的职务名称
    # 中国共产党河北省委员会， 中共河北省委，河北省委
    org1_trigger = ['省委', '市委', '自治区党委']
    org2_trigger = ['政府']
    org3_trigger = ['人大', '人民代表大会']
    org4_trigger = ['政协', '政治协商会议']
    all_res = []
    if res['province'] and trigger(res['org'], org1_trigger): #地方党委, res['org'] 为“省委”、“市委”
        plist = [p for p in provinces if res['province'] in p]
        org_name, org_name1 = '', ''
        if plist:
            province_full_name = plist[0]
            start_idx =  res['org'].index('委') +1
            if not res['city']: # 省级
                org_name = "中共"+ res['province'] + res['org']
                org_name1 = "中国共产党"+ province_full_name + '委员会'
            else: # 地市级
                cname_list = [c for c in all_city if res['city'] in c]
                if cname_list:
                    city_full_name = cname_list [0]
                    if cname_list:
                        org_name = "中共"+ res['city'] + res['org']
                        org_name1 = "中国共产党"+ city_full_name + '委员会' + res['org'][start_idx:]
        if org_name1 and org_name:
            orglist = [org_name, org_name1, res['org']]
            all_res.append({"province":"", "city":"", 'org':orglist, "position":res['position']})
            all_res.append(res)
    
    if res['province'] and trigger(res['org'], org2_trigger): #地方政府
        plist = [p for p in provinces if res['province'] in p]
        org_name, org_name1 = '', ''
        if plist:
            province_full_name = plist[0]
            if not res['city']: # 省级
                org_name =  province_full_name[len(res['province']):] # 省、市、自治区
                org_name1 = province_full_name[len(res['province']):] + '政府'
            else: # 地市级
                cname_list = [c for c in all_city if res['city'] in c]
                if cname_list:
                    city_full_name = cname_list[0]
                    org_name = city_full_name[len(res['city']):]
                    org_name1 =city_full_name[len(res['city']):] + '政府'
        if org_name1 and org_name:
            orglist = [org_name, org_name1, res['org']] #省 省政府 省人民政府
            #orglist = list(set(orglist))     
            all_res.append({"province":res['province'], "city":res['city'], 'org':orglist, "position":res['position']})
            all_res.append({"province":"", "city":"", 'org':orglist[1:] + [""], "position":res['position']})
    
    if res['province'] and trigger(res['org'], org3_trigger): #地方人大， 市级人大一般不含省名称，e.g.邯郸市人大常委会主任  
        org_name, other_name = '', ''
        if res['province'] and not res['city']: # 省级人大
            org_name =  res['province'] + res['org'] # 省、市、自治区
            hit_word = trigger(res['org'], org3_trigger)
            other_name = org_name.replace('人大','人民代表大会') if len(hit_word)==2 else org_name.replace('人民代表大会', '人大')  
            org_name_short = res['org'].replace('人大','人民代表大会') if len(hit_word)==2 else res['org'].replace('人民代表大会', '人大')        
        elif res['city']: # 地市级
            org_name =  res['city'] + res['org'] # 含省份的全称
            hit_word = trigger(res['org'], org3_trigger)
            org_name_short = res['org'].replace('人大','人民代表大会') if len(hit_word)==2 else res['org'].replace('人民代表大会', '人大')
            other_name = org_name.replace('人大','人民代表大会') if len(hit_word)==2 else org_name.replace('人民代表大会', '人大')
        if other_name and org_name:
            orglist = [org_name, other_name,res['org'], org_name_short]
            all_res.append({"province":'', "city":'', 'org':orglist, "position":res['position']})

    if res['province'] and trigger(res['org'], org4_trigger): #地方政协， 市级政协一般不含省名称，e.g.邯郸市政协主席 
        org_name, other_name = '', ''
        if res['province'] and not res['city']: # 省级政协
            org_name =  res['province'] + res['org'] # XXX省政协
        elif res['city']: # 地市级
            org_name =  res['city'] + res['org'] # 含省份的全称
        end_idx = org_name.index('政协')
        province_name = org_name[:end_idx] #XX省
        other_name = '中国人民政治协商会议' +  province_name + '委员会' +  org_name[end_idx+2:] #  中国人民政治协商会议XX省委员会
        if other_name and org_name:
            orglist = [org_name, other_name,res['org']]
            all_res.append({"province":'', "city":'', 'org':orglist, "position":res['position']})
    return all_res

def filt_title_num(t):
    # 处理职务中的第XXX届
    num_info = ''
    short = t
    if "届" in t:#e.g.第十九届中央政治局委员
        cut_end = t.index("届")
        idx = cut_end - 1
        while(t[idx].isnumeric() or t[idx]=="第"):
            idx -= 1   
        if idx > 0:    
            short = t[:idx+1] + t[cut_end+1:]
            num_info = t[idx+1:cut_end+1]
        else:
            short = t[cut_end+1:]
            num_info = t[:cut_end+1]
    return short.strip(), num_info

def expand_org(org_name, org_dict):
    #扩展机构名
    other_names = get_all_titles(org_name, org_dict)
    short_name, num_info = filt_title_num(org_name)
    if org_name!=short_name: # 机构中包含第XX界
        other_names.append(short_name)
    #特殊扩展
    if '中央' in other_names:
        other_names.append('中央委员会')
        other_names.append('中国共产党中央委员会')
        other_names.append('中共中央委员会')
        if num_info:# 包含第XX界
            other_names.append(num_info + '中央委员会')
            other_names.append('中国共产党' + num_info + '中央委员会')

    return other_names

def generate_all_titles(title_str, org_dict, name):
    all_title = []
    analyzer = TitleAnalyzer()
    title_res = analyzer.analysis_title_str(title_str, name)
    # title_res= analysis_title_str(title_str, name)
    # print('ans:', title_res)
    # if name in ["康旭平"]:
    #     print(name)
    #     print(title_res)
    title_res= normalize_org(title_res)#标准化
    # print('norm:', title_res)
    for res in title_res:
        temp_res = []
        local_res = expand_local_title(res)
        # print(f"local_res: {local_res}")
        if local_res: #地方领导扩展
            temp_res = local_res
        else:
            temp_res = [res]    
        for r in temp_res:
            if isinstance(r['org'], str): #org 统一扩展为list
                # print(res)
                res['org'] = expand_org(res['org'], org_dict)
                # print(res)
            for c in r['org']:
                for t in r['position']:
                    all_title.append(r['province']+r['city'] +c+t)
    all_title_fix = all_title.copy()
    for i, ct in enumerate(all_title_fix):  
        if ct=='外交部部长':
            all_title.insert(i, '外长')
        if ct=='中央政治局常委':
            all_title.insert(i, '中央政治局常务委员会委员')
            all_title.insert(i, '中共中央政治局常务委员会委员')
        if ct in ['国家主席', '国家副主席']:
            all_title.insert(i, '中国'+ct)
    return all_title

def make_dataset(items, name_dict, org_dict, repeat_names, esobj, index_name='leader-info-v1'):
	# 对抓取的数据进行后处理，整理为入ES库格式(json)
    new_lines = []    
    process_names = [] #记录已处理过的姓名
    url_num = 0
    valid_num = 0
    for subitem in items:
        if not subitem['eName']:
            # print(subitem)
            continue
        name = name_filter(subitem['eName']) # 姓名信息 
        use_url = 0 #记录是否使用了URL
        if name not in repeat_names: #该姓名没有重名或兼职
            subitems = [subitem]
        elif name in name_dict and name not in process_names:#该姓名存在重名或兼职
            subitems = name_dict.get(name)  
            process_names.append(name)
        else:
            continue   
        for item in subitems:
            if item['ePosition'] and item['url']: # 存在编辑职务
                title = item['ePosition']
                title = remove_parentheses(title) # 去除职务中的括号
                use_url = 1
                url_num += 1
            else: # 不存在url, 将title设置为 机构+机构职务
                continue
            if item.get('leader_type', 'no') != 'no' and len(title)>4 and int(item['leader_type']) == 0:
                cp = add_call_position(title,name)
                cp = [name+c for c in cp] 
                match_text = title + name 
                other_titles =  expand_other_titles(title, item['province'], item['city'], org_dict)  
                other_titles_v1 = generate_all_titles(title, org_dict)
                jsonline = {}
                jsonline['name'] = name
                jsonline['url'] = item['url']
                jsonline['title'] = title
                if other_titles:
                    mix_titles = other_titles_v1.copy()
                    for t in other_titles:
                        if t not in other_titles_v1:
                            mix_titles.append(t)

                    jsonline['all_title'] = mix_titles 

                else:
                    jsonline['all_title'] = other_titles_v1
                jsonline['match_text'] = match_text
                jsonline['position'] = cp  
                jsonline['use_url'] = use_url
                jsonline['province'] = item['province']
                jsonline['city'] = item['city']
                jsonline["matchtext_all"]=[]
                for n in get_all_titles(title, org_dict): #增加扩展字段
                    jsonline["matchtext_all"].append(n+name)                 
                if len(name)>1:
                    new_lines.append(jsonline) 
                    valid_num += 1
    print(valid_num, url_num)
    return new_lines
    # 不json保存文件，直接上传es
    # update_es(index_name, new_lines, esobj)
    # return valid_num, url_num

def load_es_orgs(obj: ManES):
    #加载机构信息
    orgs = {}
    doc = {"_source":[], "size":1000}
    all_res = obj.Get_Data_By_Body('organizations', doc)
    for res in all_res:
        item = res['_source']
        if item:
            orgs[item["name"]] = item["short_name"]
            total_list = item["short_name"] +[item["name"]]
            for org in item["short_name"]:
                if not org:
                    print(total_list)
                    continue
                temp_list = total_list.copy()
                temp_list.remove(org)
                orgs[org] = temp_list
    return orgs 

if __name__ == '__main__':
    title = "第十四届全国人大代表、外事委员会委员"
    obj = ManES('leader-info-v1', '_doc')
    org_dict = load_es_orgs(obj)
    titles = generate_all_titles(title, org_dict)
    print(titles)

