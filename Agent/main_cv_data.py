# -*- coding: utf-8 -*-
import json
import os
import traceback
from xml.etree.ElementPath import prepare_predicate
from elasticsearch import Elasticsearch
import sys
sys.path.append("/home/<USER>/leader-info/UpdateLeaderInfo/ops")
from xiugai_es_data import get_date_from_ES
import logging
import requests
from datetime import datetime, timedelta

def setup_logger():
    # 创建一个日志记录器
    logger = logging.getLogger('my_logger')
    logger.setLevel(logging.INFO)
    # 创建一个文件处理器，将日志记录到文件中
    file_handler = logging.FileHandler('/home/<USER>/leader-info/UpdateLeaderInfo/Agent/log/log_file_{}.log'.format(datetime.today().strftime("%Y%d%m")), mode="a")
    file_handler.setLevel(logging.INFO)
    # 创建一个日志格式器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    # 将处理器添加到日志记录器中
    logger.addHandler(file_handler)

    # 创建stream_handler
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setLevel(logging.INFO)
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)
    return logger

class CV_PROCESSE:
    def __init__(self) -> None:
            
        with open('/home/<USER>/leader-info/UpdateLeaderInfo/configs/es_config.json', "r", encoding="utf-8") as fp:
            esconfig = json.load(fp=fp)
        es = Elasticsearch(esconfig["host"], basic_auth=(esconfig["user"], esconfig["passwd"]), request_timeout=120, max_retries=10, retry_on_timeout=True)
        cv_data = get_date_from_ES(escon=es, index_name="leader-cv-new")
        self.cv_data = []
        for item in cv_data:
            _source = item.get("_source", None)
            if _source not in self.cv_data:
                self.cv_data.append(_source)
        self.logger = setup_logger()
        self.logger.info("共有cv数据：{}".format(len(self.cv_data)))

    def leader_check(self, sentence):
        url = "http://*************:7013/api/t1003/leadercheck"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
        }
        data = {
            "sentence": sentence,
            "secretId": "test",
            "businessId": "0",
            "dataId": "dataid",
        }
        r = requests.post(url, headers=headers, data=json.dumps(data))
        return r.json()
    
    # 获取paper_name的报纸新闻
    def get_paperdata(self, paper_data_path="/home/<USER>/bazhuayu_data/paperdata_Spider/runtime/json", start_date=datetime(2024, 8, 1), end_date= datetime(2024,10, 20), paper_name="参考消息"):
        paperdata = []
        dates = []
        current_date = start_date
        while current_date<=end_date:
            if os.path.exists(os.path.join(paper_data_path, current_date.strftime("%Y%m%d"))):
                dates.append(current_date.strftime('%Y%m%d'))
            else:
                pass
            current_date += timedelta(days=1)

        for dt_item in dates:
            jsonfile = os.path.join(paper_data_path, "{}/{}.json".format(dt_item,paper_name))
            if os.path.exists(jsonfile):
                with open(jsonfile, "r", encoding="utf-8") as fp:
                    paperdata.extend(json.load(fp=fp))
        return paperdata

    def process(self):
        # step1: 获取新闻内容
        paper_data = self.get_paperdata(paper_name="参考消息")

        self.chat_completion_url = "http://g12.guodata.com:5602/v1/chat/completions"
        headers={"Content-Type":"application/json"}
        for news in paper_data:  
            _date = news.get("date", None)
            _content = news.get("content", None)
            self.logger.info("==="*30)
            self.logger.info("新闻报道: {}".format(_content))
            api_results = self.leader_check(_content)
            for rst in api_results.get("hitinfos"):
                _source = rst.get("source", None)
                _reference=rst.get("reference", None)
                if _source and _reference and _source.find("习近平")==-1:
                    name_pos = _source.split("-")
                    _name = name_pos[0]
                    es_search_results = [item.get("cv") for item in self.cv_data if item["name"]==_name]
                    if es_search_results:
                        self.logger.info("【{}】简历信息".format(_name))
                        if len(es_search_results)==1:
                            message = "【已知：{}】。{}新闻报道内容：{}。判断新闻报道里的{}是不是已知信息的里提到的{}，用'是'或'否'回答。".format(es_search_results[0], _date, _content,_name, _name)
                            input_message = {"message": message}
                            check_result = requests.post(self.chat_completion_url, headers=headers, data=json.dumps(input_message))
                            self.logger.info("【1】: {}".format(es_search_results[0]))
                            self.logger.info("判断新闻报道里的{}是不是已知信息的里提到的 【{}】，用'是'或'否'回答。".format(_name, _name))
                            self.logger.info(check_result.json())
                        elif not es_search_results:
                            pass
                        else:
                            message = "已知{}个{}简历信息如下:".format(len(es_search_results), _name)
                            for idx, es_rst in enumerate(es_search_results):
                                message+="{}:{}。\n".format(idx+1, es_rst)
                                self.logger.info("【{}】:{}".format(idx+1, es_rst))
                            input_message = {"message": "【{}】。\n{}新闻报道如下：{} 基于已知的人物信息，判断新闻报道中的{}是已知的哪个人？".format(message, _date, _content, _name)}
                            check_result = requests.post(self.chat_completion_url, headers=headers, data=json.dumps(input_message))
                            self.logger.info("基于已知的人物信息，判断新闻报道中的 【{}】 是已知的哪个人？".format(_name))
                            self.logger.info(check_result.json())

def main():
    cv_prcesse = CV_PROCESSE()
    cv_prcesse.process()

if __name__=="__main__":
    main()