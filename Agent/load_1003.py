# -*- coding: utf-8 -*-
import os, json, re
import pandas

current_dir = os.path.dirname(__file__)
# try:
#     with open(os.path.join(current_dir, "data/1003.txt"), mode="r", encoding="utf-8") as fp:
#         text = fp.read()
#         text = "[" + text + "]"
#         text = re.sub("}\n{", "},{", text)
#         json_data = json.loads(text)
#         data = []
#         for item in json_data:
#             hitinfos = item.get("hitinfos", "")           
#             content = item.get("content", "")
#             content = re.sub("\n{1,}", "\n", content)
#             item["content"] = content
#             if item not in data:
#                 data.append(item)
#         with open("ouput_1003.json", "w", encoding="utf-8") as fp:
#             json.dump(data, fp, indent=2, ensure_ascii=False)
#         print("长文本已截断并写入Excel文件！")    
# except Exception as errmsg:
#     print("errmsg:", errmsg)
try:
    with open(os.path.join(current_dir, "log/online_result_20240601-20250120.txt"), mode="r", encoding="utf-8") as fp:
        lines = fp.readlines()
        for line in lines[:10]:
            json_data = json.loads(line)
            content = json_data.get("content", None)
            print(content)
        print("长文本已截断并写入Excel文件！")    
except Exception as errmsg:
    print("errmsg:", errmsg)
