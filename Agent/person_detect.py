# -*- coding: utf-8 -*-
import json
import os
import re
import traceback
from xml.etree.ElementPath import prepare_predicate
from elasticsearch import Elasticsearch
import sys
sys.path.append("/home/<USER>/leader-info/UpdateLeaderInfo/ops")
from xiugai_es_data import get_date_from_ES
import logging
import requests
import random
from datetime import datetime, timedelta
from collections import Counter

def get_txt_date(filename="/home/<USER>/leader-info/UpdateLeaderInfo/Agent/log/online_result_20240601-20250120.txt"):
    # 这个txt中有大量重复的数据，需要过滤一下
    txt_data = []
    contents = []
    with open(file=filename, mode="r", encoding="utf-8") as readfile:
        lines = readfile.readlines()
        for line in lines[:300]:
            jsondata = json.loads(line)
            content = jsondata.get("content", None)
            if content not in contents and content:
                content = re.sub("\n{1,}", "\n", content)
                contents.append(content)
            else:
                continue
              
            detect_result = jsondata.get("data", None)
            if not detect_result:
                continue
            for item in detect_result:
                _type = item.get("type", None)
                hitinfos = item.get("hitinfos", None)
                if str(_type).find("1003")!=-1:  
                    txt_data.append({"content": content, "hitinfos": hitinfos})
    with open("/home/<USER>/leader-info/UpdateLeaderInfo/Agent/log/bufen_onlinedata.json", mode="w", encoding="utf-8") as fp:
        json.dump(txt_data, fp, ensure_ascii=False, indent=4)
    return txt_data

class DatasetClassifier:
    def __init__(self, input_data=[]):
        self.current_path = os.path.dirname(__file__)
        self.globalleaders = {"习近平":"中国共产党中央委员会总书记，中共中央军事委员会主席，中华人民共和国主席，中华人民共和国中央军事委员会主席",
                              "李强":"中共二十届中央政治局常委，国务院总理、党组书记，中央金融委员会主任",
                              "赵乐际":"中共二十届中央政治局常委，十四届全国人大常委会委员长、党组书记",
                              "王沪宁": "中央政治局常委，第十四届全国政协主席、党组书记，中央全面深化改革委员会办公室主任，中国和平统一促进会会长",
                              "蔡奇": "中央政治局常委、中央书记处书记、中央办公厅主任、中央和国家机关工委书记",
                              "丁薛祥": "中共二十届中央政治局常委，国务院副总理、党组副书记，中央科技委员会主任",
                              "李希": "中央政治局常委，中央纪律检查委员会书记"}
        self.input_data = input_data
        self.name_position_dict = self._get_name_position_dict()
    
    def _get_name_position_dict(self):
        name_position_dict =[]
        for data in self.input_data:
            hitinfos = data.get("hitinfos", [])
            content = data.get("content", None)
            name_appearance_position = []  # 存储当前hitinfos中的name、position、和appearance_count
            for info in hitinfos:
                # 循环获取hitinfos，用“-”分割，第一部分是name，第二部分是position
                source = info.get("source", "")
                split_source = source.split("-")
                if source:
                    # 获取source中的姓名和职务，name and position
                    if len(split_source)>1:
                        name = split_source[0]
                        position = split_source[1]
                    else:
                        name = source
                        position = ""
                    if name_appearance_position:
                        name_in_list = False
                        for idx, item in enumerate(name_appearance_position):
                            if not name_in_list:
                                if item["name"] and item["name"]==name:
                                    name_in_list = True
                                    name_appearance_position[idx]["appearance_count"]+=1
                                    if len(name_appearance_position[idx]["position"])<len(position):
                                        name_appearance_position[idx]["position"] = position
                            else:
                                pass
                        if not name_in_list:
                            name_appearance_position.append({"name":name, "appearance_count":1, "position":position})
                    else:
                        name_appearance_position.append({"name":name, "appearance_count":1, "position":position})
                else:
                    continue
            if name_appearance_position:
                name_position_dict.append({"content":content, "name_appearance_position": name_appearance_position})
        with open("/home/<USER>/leader-info/UpdateLeaderInfo/Agent/log/bufen_onlinedata2.json", mode="w", encoding="utf-8") as savefp:
            json.dump(name_position_dict, fp=savefp, ensure_ascii=False, indent=2)
        return name_position_dict
    
    def classify(self, data):
        '''根据文本数据将其分类'''
        classified_data = {"type1":[], "type2":[], "type3":[], "type_4":[]}
        for sentence in data:
            if self.is_type_1(sentence) and len(classified_data["type1"])<200:
                classified_data["type1"].append(sentence)
            elif self.is_type_2(sentence) and len(classified_data["type2"])<200:
                classified_data["type2"].append(sentence)
            elif self.is_type_3(sentence) and len(classified_data["type3"])<200:
                classified_data["type3"].append(sentence)
            elif self.is_type_4(sentence) and len(classified_data["type4"])<200:
                classified_data["type4"].append(sentence)

        return classified_data
    
    def is_type_1(self, sentence):
        """判断是否是类型1：同名人名多次出现，但职务+人名的组合只出现一次"""
        ''' 1、相同人名：字符或拼音完全相同
            2、相似人名：利用拼音或者字符的编辑距离计算，构建名字混淆集
            3、名字多字少字，利用数据库构造数据
        '''
        type_1 = False
        
        return type_1
    
    def is_type_2(self, sentence):
        """判断是否是类型2：职务错误，混淆同名人物的职务"""
        type_2 = False
        return type_2
    
    def is_type_3(self, sentence):
        """判断是否是类型3：多个人名并列出现，且人名前有职务信息"""
        # 判断是否有多个名字并列出现，并且人名前有职务信息
        type_3 = False

        return type_3

    def is_type_4(self, sentence):
        """境内外重要领导人"""
        type_4 = False

        return type_4



txt_data = get_txt_date()
# print(txt_data[1])
datasetclassifier = DatasetClassifier(input_data=txt_data)

                