

import traceback
from unittest import result
import requests
import json
import os
import traceback
from datetime import datetime, timedelta


url = "http://192.168.0.224:7013/api/t1003/leadercheck"

headers = {
    "Content-Type": "application/json;charset=UTF-8",
}

def get_leader_result(sentence):
    data = {
        "sentence": sentence,
        "secretId": "test",
        "businessId": "0",
        "dataId": "dataid",
    }
    r = requests.post(url, headers=headers, data=json.dumps(data))
    return r.json()

text = '蔡奇委员'
api_result = get_leader_result(text)
api_result2 = []
for item in api_result["hitinfos"]:
    item.pop("flag", None)
    item.pop("length", None)
    item.pop("model", None)
    item.pop("offset", None)
    source = item.get("source", None)
info = {"api_result":api_result, "content": text}
print(info)
'''
result_data = []
paper_data_path = "/home/<USER>/bazhuayu_data/paperdata_Spider/runtime/json"
start_date = datetime(2024, 9, 1)
end_date = datetime(2025, 1, 15)
wrong_count = 0
dates = []
current_date = start_date
while current_date <= end_date:
    if os.path.exists(os.path.join(paper_data_path, current_date.strftime("%Y%m%d"))):
        dates.append(current_date.strftime('%Y%m%d'))
    else:
        pass
    current_date += timedelta(days=1)

for date in dates:
    print("=="*30)
    print(date)
    try:        
        # 从报纸
        with open(os.path.join(os.path.dirname(__file__), "paperdata/参考消息.json"), "w", encoding="utf-8") as savefp:
            savefp.write("[\n")
            jsonfile = os.path.join(paper_data_path, "{}/参考消息.json".format(date))  #/home/<USER>/bazhuayu_data/paperdata_Spider/runtime/json/20240402/北京日报.json
            #/home/<USER>/bazhuayu_data/paperdata_Spider/runtime/json/20241209/光明日报.json
            if os.path.exists(jsonfile):
                with open(jsonfile, "r", encoding="utf-8") as fp:
                    paper_data = json.load(fp=fp)
                    for item in paper_data:
                        content = item.get("content")
                        api_result = get_leader_result(content)
                        api_result2 = []
                        for item in api_result["hitinfos"]:
                            item.pop("flag", None)
                            item.pop("length", None)
                            item.pop("model", None)
                            item.pop("offset", None)
                            source = item.get("source", None)
                            if source and "习近平" not in source:
                                api_result2.append(item)
                            else:
                                continue
                        if len(api_result2)==0:
                            continue
                        info = {"api_result":api_result2, "content": content}
                        result_data.append(info)

                        json.dump(info, fp=savefp, ensure_ascii=False, indent=2)
                        savefp.write(",\n")
            savefp.write("]")
            
    except Exception as errmsg:
        wrong_count += 1
        print(traceback.format_exc())
        print("{}:{}".format(date, errmsg))
# with open("paperdata/北京日报.json", "w", encoding="utf-8") as savefp:
#     json.dump(result_data, fp=savefp, ensure_ascii=False, indent=2)

print("wrong_count=", wrong_count)'''

# r = get_leader_result("昨天下午，中共北京市委副书记、市长殷勇与参加市政协十四届三次会议的港澳委员、港澳台侨和对外友好工作顾问座谈。市政协主席魏小东主持。\n座谈会气氛热烈，10位港澳委员、港澳台侨和对外友好工作顾问踊跃发言，纷纷为北京日新月异的变化点赞，并结合实际，聚焦北京市中心工作和“十五五”规划编制等建真言谋良策出实招。\n殷勇认真倾听、详细记录，针对大家关心的历史文化遗产宣传、科创人才集聚、智能网联技术发展、生物科技合作、优化旅游体验、工业AI、国际消费中心城市建设、金融资本市场运作、促进新质生产力与高精尖产业发展、深化京台交流等问题，作出回应，称赞大家提出的高质量建议，对改进提升政府工作具有重要意义。\n殷勇介绍北京市工作情况，就委员和顾问关心的经济发展问题，与大家深入交流。他说，今年是“十四五”规划收官之年，也是为“十五五”良好开局打牢基础的关键之年。北京市将以新时代首都发展为统领，坚持“五子”联动服务和融入新发展格局，扎实推动高质量发展，进一步全面深化改革，扩大高水平对外开放，着力扩大内需、稳定预期、激发活力，统筹好发展和安全，推动经济持续回升向好。\n殷勇指出，面对国内外环境变化带来的影响，首都经济长期向好的基本趋势没有变，北京将充分运用地方政府的五大政策工具，为经济增长提供充足的“加油量”。进一步用好财政政策，充分发挥财政资金对经济增长的支撑作用。充分运用国家支持政策扩展可用财力，完善重大项目储备机制，强化项目审批制度改革。加强财政资源和预算统筹，处理好增量和存量的关系。深化预算绩效管理改革，推进绩效管理和预算管理深度融合，切实提高财政资金使用效益。进一步用好金融政策，发挥政府投资基金逆周期调节作用，更好满足实体经济需要。\n殷勇强调，用好产业政策，加快以科技创新引领新质生产力发展。深化科技体制机制改革，持续提升科技创新体系效能。建立未来产业促进机制，加强集成电路、生物医药、新能源等重点产业布局，大力推进数据基础制度先行区建设，巩固拓展高精尖产业优势。用好区域政策，着力促进城乡区域协同发展。纵深推进京津冀协同发展，不断深化三地协同创新和产业协作。统筹推进市域内的协同发展，推动各类优质资源均衡布局。用好社会政策，着力提高民生保障水平。毫不松懈抓好空气污染治理，推动生态环境质量持续提升。构建以轨道交通为骨干、地面公交为支撑、多种出行方式为补充的综合交通体系，逐步改善交通出行条件。积极发挥平台企业作用，实现群众日常需求精准对接管理。\n殷勇感谢各位委员和顾问长期以来对北京工作的关心支持，希望大家继续参政议政，为政府工作提供更多宝贵意见建议，共同推动新时代首都发展。\n市政协副主席崔述强、陈军，党组成员韩子荣，市政府秘书长曾劲，市政协秘书长韩昱参加。")
# print(r)
