# -*- coding: utf-8 -*-
import requests
import json
import time 
import os
os.system('clear')

url = "http://g12.guodata.com:5602/v1/chat/completions"
headers={"Content-Type":"application/json"}


def is_excluded_person(text, name):
    prompt = r'判断 【{}】 文本中 【{}】 是不是政治人物，返回是或否，不要输出其他。' #如果不是，说明理由'
    data = {'message': prompt.format(text, name)}
    start_time = time.time()
    r = requests.post(url, headers=headers, data=json.dumps(data))
    end_time = time.time()
    print('runtime:{} s'.format(end_time-start_time))
    print(data)
    print(json.dumps(r.json(), indent=4, ensure_ascii=False))   #r.json())
    return r.json()

def fill_leader_position_details(text, name):
    prompt = r'请从 {} 中提取{}的任职机构和职务，并以json格式输出。如果任职机构不确定，请输出none。不要输出其他内容'
    data = {'message': prompt.format(text, name)}
    start_time = time.time()
    r = requests.post(url, headers=headers, data=json.dumps(data))
    end_time = time.time()
    print('runtime:{} s'.format(end_time-start_time))
    print(data)
    print(json.dumps(r.json(), indent=4, ensure_ascii=False))   #r.json())
    return r.json()

def othors(prompt):
    # prompt = r'请从 {} 中提取{}的任职机构和职务，并以json格式输出。如果任职机构不确定，请输出none。不要输出其他内容'
    data = {'message': prompt}
    start_time = time.time()
    r = requests.post(url, headers=headers, data=json.dumps(data))
    end_time = time.time()
    print('runtime:{} s'.format(end_time-start_time))
    print(data)
    print(json.dumps(r.json(), indent=4, ensure_ascii=False))   #r.json())

def extract_names(text):
    prompt = r'请从 {} 中抽取人名，以list格式输出，不要输出其他内容'
    data = {'message': prompt.format(text)}
    start_time = time.time()
    r = requests.post(url, headers=headers, data=json.dumps(data))
    end_time = time.time()
    print('runtime:{} s'.format(end_time-start_time))
    print(data)
    print(json.dumps(r.json(), indent=4, ensure_ascii=False))   #r.json())
    return r.json()


if __name__=='__main__':
    # text = '游客许宁：我觉得1∶1还原之后的文物特别有意思'
    # is_excluded_person(text=text, name='许宁')
    # text = '省委统战部部长、省民族宗教委党组书记、主任拉玛·新高。省委统战部副部长、省民族宗教委党组书记、主任拉玛·兴高'
    # extract_names(text)
    # text = '4月22日至24日，教育部部长王毅率团访问新加坡'
    # extract_names(text)
    # fill_leader_position_details(text=text, name='拉玛·兴高')
    # text = '社区代表张三，劳模李进，游客王名，摄影张云  记者赵月   岳东编辑   山西省政府党组成员、秘书长，省政府机关党组书记、办公厅主任李武'
    # extract_names(text=text)
    # is_excluded_person(text=text, name='李武')
    # text = '江西省委常委卢小青副省长莅临本市。卢小青常委指导工作'
    # text = '前中共中央总书记,中国国家主席江泽民的逝世,对中国共产党,中国政府和中国人民都是不可估量的损失'
    # extract_names(text=text)
    # is_excluded_person(text=text, name='江泽民')
    # prompt = r'请根据【{}】抽取【{}】的任职信息，按照机构和职务输出为json格式，不要输出其他'.format(text, '江泽民')
    # text = '高涛，男，辽宁省人民政府原副省长、党组成员，汉族，1969年11月出生，山东乳山人，1992年8月参加工作，1991年6月加入中国共产党，工程博士，研究员级高级工程师。曾任辽宁省人民政府副省长、党组成员。'
    # prompt = r'请根据【{}】抽取【{}】现在的任职信息，如无输出none，不要输出其他'.format(text, '高涛')
    # othors(prompt=prompt)
    text = '黄强当选四川省省长'
    fill_leader_position_details(text=text, name='黄强')
    
    text = '汉东省吕州市美食城负责人赵瑞'
    text = '社区代表张三，劳模李进，游客王名，摄影张云  记者赵月   岳东编辑   山西省政府党组成员、秘书长，省政府机关党组书记、办公厅主任李武'
    fill_leader_position_details(text=text, name = '李武')

    text = "本报讯（记者 陈昭淋）12月20日，喀什地区文学艺术界联合会第六次代表大会开幕。地委书记聂壮，自治区文联党组书记、副主席邓选斌出席开幕式并讲话，地委副书记、行署专员艾尼瓦尔·吐尔逊主持开幕式。"
    othors(prompt="{},抽取这段话中的所有的人名和他对应的职务信息，根据上下文信息，涉及到地域的根据全文补全对应的地域信息，返回json数据".format(text))



# data = {"message":"基于'习'，'近'，'平'这两个字生成音近和形近字"}
#"安康市委常委、副市长殷勇来市发投集团调研指导工作"
# data = {"message": "你知道蛤蟆说的是中国的那一任领导人吗"}
# message='''
# 书记: 魏小东：党组书记、主席
# 副书记
# 崔述强：党组副书记 
# 张家明：党组副书记
# 杨晋柏：党组副书记（兼） 
# 副主席
# 卢彦：党组成员、副主席 
# 王红：副主席（女，满族）
# 林抚生：党组成员、副主席 
# 刘忠范：副主席
# 陈军：副主席（女，高山族）
# 燕瑛：副主席（女）
# 王金南：副主席 
# 党组成员
# 韩子荣、郭延红 
# 秘书长
# 韩昱 '''
# data = {"message": "已知{}。2025年1月新闻报道：王红、崔述强、张家明、卢彦、刘忠范、陈军、燕瑛，党组成员韩子荣，秘书长韩昱分别参加。新闻报道里领导的先后顺序有误吗？如果有，给出正确的顺序".format(message)}
# message = '''
# 2007-2012年 中央政治局委员，广东省委书记
# 2012-2013年 中央政治局委员
# 2013-2017年 中央政治局委员，国务院副总理、党组成员 [1]
# 2017-2018年 中央政治局常委，国务院副总理、党组成员 [2]
# 2018-2022年 中央政治局常委，十三届全国政协主席、党组书记 [3-6]
# 2022-2023年 十三届全国政协主席、党组书记 [9-10]'''
# data = {"message":"已知：{}。新闻报道：2016年5月3日至5日，受两地政协主席委托，时任北京朝阳区政协副主席汪洋、王文远，时任昆明市政协副主席张建伟率先带队对东川区进行扶贫攻坚考察调研。"}

# print("运行时间：", end_time-start_time, "秒")



