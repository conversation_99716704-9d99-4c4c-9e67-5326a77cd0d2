# PersonInfoVerifier

## 项目概述
人物信息验证系统，通过LTP4抽取文本中的人名，结合大模型API和ES数据库验证人物信息的准确性。

## 功能特性
- 文本中人物姓名抽取
- 职务信息自动补充  
- ES数据库检索验证
- 人物状态判断（是否在任）
- 详细的日志记录

## 项目结构
```
PersonInfoVerifier/
├── config/
│   └── settings.yaml    # 配置文件
├── model/               # LTP4模型文件
├── logs/                # 日志文件
├── src/                 # 源代码
│   ├── main.py          # 主程序
│   ├── ltp_extractor.py # 人名抽取
│   ├── llm_api.py       # 大模型接口
│   ├── es_client.py     # ES客户端
│   ├── utils.py         # 工具函数
│   └── logger.py        # 日志配置
├── requirements.txt     # 依赖项
└── README.md            # 项目说明
```

## 安装指南
1. 克隆仓库
2. 安装依赖：`pip install -r requirements.txt`
3. 放置LTP4模型文件到`model/`目录
4. 配置`config/settings.yaml`文件

## 注意事项
⚠️ 安全警告：
- 不要将配置文件提交到版本控制
- 建议使用环境变量存储敏感信息
- 日志文件可能包含敏感数据，需定期清理

📝 使用说明：
1. 确保LTP4模型文件已正确放置
2. 首次运行前检查配置文件
3. 日志文件默认保存在`logs/`目录
