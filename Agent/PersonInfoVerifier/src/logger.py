import logging
from logging.handlers import TimedRotatingFileHandler
import os
from pathlib import Path

def setup_logger(name='PersonInfoVerifier'):
    """配置并返回日志记录器
    
    Args:
        name (str): 日志记录器名称
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)

    # 创建logs目录(如果不存在)
    log_dir = Path(__file__).parent.parent / 'logs'
    log_dir.mkdir(exist_ok=True)
    
    # 日志文件处理器(按天轮转，保留7天)
    log_file = log_dir / 'app.log'
    file_handler = TimedRotatingFileHandler(
        log_file,
        when='midnight',
        backupCount=7,
        encoding='utf-8'
    )
    
    # 日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(module)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(formatter)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

# 默认日志记录器
logger = setup_logger()
