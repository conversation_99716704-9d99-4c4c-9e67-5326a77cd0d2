from logger import logger
from typing import List, Dict, Any

def validate_input(text: str, session_id: str) -> bool:
    """验证输入参数
    
    Args:
        text (str): 输入文本
        session_id (str): 会话ID
        
    Returns:
        bool: 参数是否有效
    """
    if not text or not isinstance(text, str):
        logger.error("Invalid input text")
        return False
    if not session_id or not isinstance(session_id, str):
        logger.error("Invalid session ID")
        return False
    return True

def format_result(names: List[str], positions: List[str], 
                 is_current: List[bool]) -> List[Dict]:
    """格式化结果输出
    
    Args:
        names (List[str]): 姓名列表
        positions (List[str]): 职务列表
        is_current (List[bool]): 是否在任列表
        
    Returns:
        List[Dict]: 格式化后的结果列表
    """
    return [
        {
            "name": name,
            "position": pos,
            "is_current": current
        }
        for name, pos, current in zip(names, positions, is_current)
    ]

def merge_duplicates(results: List[Dict]) -> List[Dict]:
    """合并重复的人物信息
    
    Args:
        results (List[Dict]): 原始结果列表
        
    Returns:
        List[Dict]: 合并后的结果列表
    """
    merged = {}
    for item in results:
        name = item["name"]
        if name not in merged:
            merged[name] = item
        else:
            merged[name]["position"] += f", {item['position']}"
    return list(merged.values())
