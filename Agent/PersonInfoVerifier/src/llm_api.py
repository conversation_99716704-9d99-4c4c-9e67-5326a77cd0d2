from logger import logger
import requests
import yaml
from pathlib import Path
from typing import Dict, List, Optional

class LLMAPI:
    def __init__(self, config_path=None):
        """初始化大模型API客户端
        
        Args:
            config_path (str): 配置文件路径
        """
        if config_path is None:
            config_path = Path(__file__).parent.parent / 'config' / 'settings.yaml'
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        self.endpoint = config['llm_api']['endpoint']
        self.api_key = config['llm_api']['api_key']
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        logger.info(f"LLM API client initialized for endpoint: {self.endpoint}")

    def get_position_info(self, name: str) -> Optional[Dict]:
        """获取人物的职务信息
        
        Args:
            name (str): 人物姓名
            
        Returns:
            dict: 包含职务信息的字典，格式为:
                {
                    'name': str,
                    'positions': List[str],
                    'is_current': bool
                }
        """
        try:
            payload = {
                "name": name,
                "task": "get_position_info"
            }
            
            response = requests.post(
                self.endpoint,
                json=payload,
                headers=self.headers,
                timeout=30
            )
            response.raise_for_status()
            
            return response.json()
        except Exception as e:
            logger.error(f"Error getting position info for {name}: {str(e)}")
            return None
