from logger import logger
from ltp import LTP
from pathlib import Path
import yaml

class LTPExtractor:
    def __init__(self, config_path=None):
        """初始化LTP4人名提取器
        
        Args:
            config_path (str): 配置文件路径
        """
        if config_path is None:
            config_path = Path(__file__).parent.parent / 'config' / 'settings.yaml'
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        model_path = config['ltp']['model_path']
        self.ltp = LTP(model_path)
        logger.info(f"LTP4 initialized with model: {model_path}")

    def extract_names(self, text):
        """从文本中提取人名
        
        Args:
            text (str): 输入文本
            
        Returns:
            list: 提取到的人名列表
        """
        try:
            seg, hidden = self.ltp.seg([text])
            ner = self.ltp.ner(hidden)
            names = [seg[0][i] for i, (tag, start, end) in enumerate(ner[0]) 
                      if tag == 'Nh']
            logger.debug(f"Extracted names: {names}")
            return names
        except Exception as e:
            logger.error(f"Error extracting names: {str(e)}")
            raise
