from logger import logger
from elasticsearch import Elasticsearch
import yaml
from pathlib import Path
from typing import List, Dict, Optional

class ESClient:
    def __init__(self, config_path=None):
        """初始化ES客户端 
        Args:
            config_path (str): 配置文件路径
        """
        if config_path is None:
            config_path = Path(__file__).parent.parent / 'config' / 'settings.yaml'
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        es_config = config['elasticsearch']
        self.client = Elasticsearch(
            hosts=[f"{es_config['host']}:{es_config['port']}"],
            http_auth=(es_config['username'], es_config['password']),
            timeout=30
        )
        self.index = es_config['index']
        logger.info(f"ES client initialized for index: {self.index}")

    def search_leaders(self, name: str, positions: List[str]) -> List[Dict]:
        """根据姓名和职务搜索领导信息 
        Args:
            name (str): 领导姓名
            positions (List[str]): 职务列表
            
        Returns:
            List[Dict]: 匹配的领导信息列表，按匹配度排序
        """
        try:
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {"match": {"name": name}},
                            {"terms": {"positions": positions}}
                        ]
                    }
                },
                "sort": [
                    {"_score": {"order": "desc"}}
                ]
            }
            
            response = self.client.search(
                index=self.index,
                body=query,
                size=10
            )
            
            return [hit['_source'] for hit in response['hits']['hits']]
        except Exception as e:
            logger.error(f"Error searching leaders: {str(e)}")
            return []
