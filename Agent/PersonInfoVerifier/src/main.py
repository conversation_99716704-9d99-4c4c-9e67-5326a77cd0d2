from logger import logger
import yaml
from pathlib import Path

def load_config():
    """加载配置文件"""
    config_path = Path(__file__).parent.parent / 'config' / 'settings.yaml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def main():
    logger.info("Starting PersonInfoVerifier")
    try:
        config = load_config()
        logger.info(f"Loaded configuration: {config['llm_api']['endpoint']}")
        
        # TODO: 实现主逻辑流程
        # 1. 初始化LTP4提取器
        # 2. 初始化大模型API客户端
        # 3. 初始化ES客户端
        # 4. 处理输入文本
        
        logger.info("PersonInfoVerifier completed successfully")
    except Exception as e:
        logger.error(f"Error occurred: {str(e)}")
        raise

if __name__ == "__main__":
    main()
