
import traceback
import pypinyin
from fuzzywuzzy import fuzz
import Levenshtein  # 引入编辑距离计算库
import json
import requests
import random
import re

# 示例音近字和形近字字典（实际使用时可以根据需要扩展）

class SimilarCharacters:
    def __init__(self):
        with open("/home/<USER>/leader-info/UpdateLeaderInfo/Agent/tools/SimilarCharacter-master/sijiao_encode_dict70000.json", "r", encoding="utf-8") as fp:
            self.sijiao_encode_dict = json.load(fp=fp)
        with open("/home/<USER>/leader-info/UpdateLeaderInfo/Agent/tools/SimilarCharacter-master/bihua_dict20000.json", "r", encoding="utf-8") as fp:
            self.bihua_dict = json.load(fp=fp)

    def _get_pinyin(self, input_char="字"):
        """获取字的拼音"""
        pinyin = pypinyin.lazy_pinyin(input_char)
        return ''.join(pinyin)

    def get_yin_similar(self, input_char='字', threhold_edit_dist=1):
        """返回音近字，加入编辑距离筛选"""
        pinyin = self._get_pinyin(input_char=input_char)
        yin_similar_result = []
        
        # 遍历常见的字，计算它们的拼音相似度
        for i in range(0x4e00, 0x9fa5):  # 遍历常用汉字Unicode范围（这个范围包含大部分汉字）
            current_char = chr(i)
            if current_char == input_char:
                continue  # 跳过自己
            current_pinyin = _get_pinyin(current_char)
            
            # 使用拼音的相似度来计算音近字（基于拼音相似度，类似拼音匹配）
            pinyin_similarity_score = fuzz.ratio(pinyin, current_pinyin)
            
            if pinyin_similarity_score > 80:  # 拼音相似度大于80
                # 计算编辑距离
                if Levenshtein.distance(pinyin, current_pinyin) <= threhold_edit_dist:  # 编辑距离小于等于2
                    yin_similar_result.append(current_char)
        if len(yin_similar_result)>10:
            yin_similar_result = random.sample(yin_similar_result, 5)
        return yin_similar_result


    def LLM_generate(message):
        url = "http://g12.guodata.com:5602/v1/chat/completions"
        headers={"Content-Type":"application/json"}
        data = {"message": message}
        r = requests.post(url, headers=headers, data=json.dumps(data))
        print(r.json())
        return r.json()


    def get_xing_similar(self, input_char, bihua_diff_threshold=3):
        """返回形近字, bihua_diff_threshold用语表示笔画差异阈值，默认值是3"""
        # 用四角编码方式查找相似字符，然后用笔画阈值控制相似字符
        xing_similar_result = []
        try:
            sijiao_value = self.sijiao_encode_dict.get(input_char, None)
            if not sijiao_value:
                return xing_similar_result
            sijiao_similar_results = []
            for key, value in self.sijiao_encode_dict.items():
                if value==sijiao_value:
                    sijiao_similar_results.append(key)
            input_char_bihua_value = self.bihua_dict.get(input_char, None)
            if sijiao_value and input_char_bihua_value:
                pass
            else:
                return xing_similar_result
            for sijiao  in sijiao_similar_results:
                bihua = self.bihua_dict.get(sijiao, None)
                if bihua and abs(int(bihua)-int(input_char_bihua_value))<=bihua_diff_threshold and sijiao!=input_char:
                    if sijiao not in xing_similar_result:
                        xing_similar_result.append(sijiao)
        except Exception as errmsg:
            print(errmsg)
        return xing_similar_result

            
                
 
            
        

        # return xing_similar_result

# def 生成音近形近字(字, 编辑距离阈值=2):
#     """生成音近或形近字"""
#     音近结果 = 音近字(字, 编辑距离阈值)
#     形近结果 = 形近字(字)   #暂时不可用
#     return {"音近字": 音近结果, "形近字": 形近结果}

# 测试
similarcharacters = SimilarCharacters()
similarcharacters.get_xing_similar(input_char="强", bihua_diff_threshold=3)