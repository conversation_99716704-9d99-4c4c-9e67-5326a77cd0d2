#!/bin/bash

# 将标准输出和错误输出重定向到日志文件
exec > "/home/<USER>/leader-info/UpdateLeaderInfo/es_sync_log/es_sync_$(date +%Y%m%d).log" 2>&1
# 没有使用set -e的时候，脚本即使遇到错误也可以继续执行下去
set -e
# 检测脚本内的组合命令
set -o pipefail
# 检查脚本内的变量，如果有变量未被定义将终止脚本
set -u
# 将脚本内的变量的值暴露出来
set -x

function urlencode {
	set +x
	value=`echo $1 | tr -d '\n' | xxd -plain | sed 's/\(..\)/%\1/g'`
	echo $value
}

# "organizations" "fallen-leader-clean" "fallen-leader-clean-prov-city" "df_fallen_leader" "fallen-leader-cv" "leader-info-new" "leader-info-v1" "leader-cv-new" "leader-relative"
# es_index_names=("organizations" "fallen-leader-clean" "fallen-leader-clean-prov-city" "df_fallen_leader" "fallen-leader-cv" "leader-info-new" "leader-info-v1" "leader-cv-new" "leader-relative")
es_index_names=("leader-info-v1" "fallen-leader-clean" "leader-info-final" "leader-relative" "sens-people")
# g12.guodata.com
FROM=*************:9212
FROM_PASSWD=gz123@people\!@#
FROM_PWD=`urlencode $FROM_PASSWD`


# localhost
TO=*************:9200
TO_PASSWD=-sAQ=*#tes
TO_PWD=`urlencode $TO_PASSWD`

# curl --user elastic:-sAQ=*#tes *************:9200/_cat/indices

for es_index in ${es_index_names[@]}
do
        echo $es_index
        curl --user elastic:$FROM_PASSWD -XGET http://${FROM}/${es_index}/_count
        curl --user elastic:$TO_PASSWD -XDELETE http://${TO}/${es_index} # 删除原始数据库
        echo
	
	docker run --net=host --rm taskrabbit/elasticsearch-dump \
		--input=http://elastic:${FROM_PWD}@${FROM}/${es_index} \
		--output=http://elastic:${TO_PWD}@${TO}/${es_index} \
		--type=mapping # 新建mapping


        docker run --net=host --rm taskrabbit/elasticsearch-dump \
		--input=http://elastic:${FROM_PWD}@${FROM}/${es_index} \
		--output=http://elastic:${TO_PWD}@${TO}/${es_index} \
		--type=data --limit 10000 # 插入数据
done

curl --user elastic:${TO_PASSWD} -XPUT \
	-H 'Content-Type: application/json' \
	http://${TO}/_settings  \
	-d '{"index": {"max_result_window": 10000000}}'  # 设置窗口大小

