# -*- coding: utf-8 -*-
import hashlib
from http import client
import os, re
import time
from turtle import Turtle
from elasticsearch import helpers, Elasticsearch
import json
import requests
from datetime import datetime
from tqdm import tqdm
from lxml import etree

import sys
import shutil
sys.path.append(os.path.abspath(os.path.dirname(os.path.dirname(__file__))))
sys.path.append("/home/<USER>/leader-info/UpdateLeaderInfo")
import re, os
import json
import logging.config
from my_logger import get_mylogger


current_date = datetime.now().date().strftime("%Y%m%d")
current_path = os.path.dirname(__file__)

log_dir = os.path.join(os.path.abspath(os.path.join(current_path,"..")), "runtime/{}/logs".format(current_date))
if not os.path.exists(log_dir):
    os.system(f'mkdir -p {log_dir}')
esupdate_logger = get_mylogger(name='es_update', filename="es_update.log", log_dir=log_dir, level='info')

# def get_logger():
#     current_date = datetime.now().date().strftime("%Y%m%d")
#     current_path = os.path.dirname(__file__)
#     esupdate_logger = logging.getLogger("ES UpdateV1")
#     esupdate_logger.setLevel(logging.DEBUG)
#     log_dir = os.path.join(os.path.abspath(os.path.join(current_path,"..")), "runtime/{}/logs".format(current_date))
#     # print(log_dir)
#     if not os.path.exists(log_dir):
#         os.system(f'mkdir -p {log_dir}')
#     log_file = os.path.join(log_dir, "es_update.log")
#     filehandler = logging.FileHandler(filename=log_file, mode="a")

#     formatter = logging.Formatter("%(asctime)s | %(lineno)d | %(message)s")
#     filehandler.setFormatter(formatter)
#     filehandler.setLevel(logging.DEBUG)
#     esupdate_logger.addHandler(filehandler)
    

#     cmdhandler = logging.StreamHandler()
#     cmdformatter = logging.Formatter("%(lineno)d | %(message)s")
#     cmdhandler.setFormatter(cmdformatter)
#     cmdhandler.setLevel(logging.DEBUG)
#     esupdate_logger.addHandler(cmdhandler)
#     return esupdate_logger

def get_time():
    now_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    return now_time

def remove_parentheses(text):
    if not text:
        return text
    ret = re.sub("（.*?）", "", text)
    ret = re.sub("（.*?\)", "", ret)
    ret = re.sub("\(.*?）", "", ret)
    ret = re.sub("\[(.*?)\]","", ret)
    ##
    return re.sub("\(.*?\)", "", ret)

# ----------------------------------------------------------
# leader-info 先确认检查是否有错，没错再写
write_to_ES = True   # False不写入ES，True写入ES
if write_to_ES:
    esupdate_logger.info("Write to ES=True")
else:
    esupdate_logger.info("Write to ES=False")

config_file = os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")), "configs/es_config.json")
with open(config_file, "r", encoding='utf-8') as fp:
    CONFIG_ES = json.load(fp)
__index_name__ = "leader-info-final" #CONFIG_ES["leader_info_index"]    #'leader-info-final'  # index新表，增加了leader_type字段                           

es = Elasticsearch(CONFIG_ES['host'], basic_auth=(CONFIG_ES['user'], CONFIG_ES['passwd']), request_timeout=120, max_retries=10, retry_on_timeout=True)

#
# ----------------------------------------------------------


def match_fixed_string(search_string, target_string):
    if search_string in target_string:
        return True
    else:
        return False

def get_leader_title(name, url):
    title = ""
    req_url = "https://baike.baidu.com"+url
    header = {
        "user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "connection" : "close"
    }
    response = requests.request("GET", url=req_url, headers=header, timeout=10)
    page_source = response.text
    response.encoding = "utf-8"
    time.sleep(1)
    response.close()
    page_title_pat = re.compile("<title>(.*?)</title>")
    page_title = "".join(page_title_pat.findall(page_source))
    if not match_fixed_string(name, page_title):
        return ""
    else:
        summary_pat = re.compile(r'<meta name="description" content="(.+?)">')
        summary = summary_pat.findall(page_source)
        summary = "".join(summary)
        post_pat = re.compile(r"现任(.+)")
        title = "".join(post_pat.findall(summary)).strip()
        title = re.sub("/[.*?/]|\xa0|/\n", "", title)
        title = re.sub("。$", "", title)
        print("\n", summary)
        print(f"{name}: {title}")
        return title


def get_item_withurl_missingcposition(es=es, index=__index_name__):
    records = []
    query = {
        "bool": {
            "must": []
        }
    }
    system_date = datetime.now().date().strftime('%Y%m%d')
    save_path = os.path.join(os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")), "runtime/{}".format(system_date)), "save_urlmissingtitle.json")
    save_record_file = open(save_path, "a", encoding="utf-8")
    res = es.search(index=index, query=query, timeout='10m', size=10000)
    current_leader_infos = res["hits"]["hits"]
    print(f"leader-info in ES: {len(current_leader_infos)}")
    for item in current_leader_infos:
        _id = item["_id"]
        _source = item["_source"]
        url = _source["url"]
        name = _source["cName"]
        cPosition = _source["cPosition"]
        ePosition = _source["ePosition"]
        if len(url)!=0 and ((not cPosition) or (not ePosition)):
            temp = {"name": name, "_id": _id, "url":url ,"cPosition": cPosition, "ePosition": ePosition}
            temp = json.dumps(temp, ensure_ascii=False, indent=4)
            save_record_file.write(temp+",")
    return records

# 避免出现/item/%E5%91%A8%E5%BE%B7%E7%9D%BF/350867/item/%E5%91%A8%E5%BE%B7%E7%9D%BF/350867/item/%E5%91%A8%E5%BE%B7%E7%9D%BF/350867的url
def extract_true_url(url):
    true_url = ""
    if url:
        url = re.sub("https://baike.baidu.com|http://baike.baidu.com", "", url)
        parts = url.split("/item/")
        if len(parts)<=1:  # 有可能url="http://baike.baidu.com"
            return true_url
        else:
        # print("parts:", parts[-1])
            true_url = "/item/{}".format(parts[-1])
            return true_url
    else:
        return true_url
    
# 插入人员数据
def insert_leader_info(leader_info_json_path):
    f = open(leader_info_json_path, 'r', encoding='utf-8')
    leader_infos = f.readlines()
    for info in leader_infos:
        leader = json.loads(info)
        leader["url"] = extract_true_url(leader["url"])
        if leader['url']:
            pId = hashlib.md5(leader['url'].encode('utf-8')).hexdigest()
        else:
            pId = ""
        doc = {
            "cName": leader.get('name', ""),
            "eName": leader.get('name', ""),
            "type": leader.get('parent', ""),
            "province": leader.get('province', ""),
            "city": leader.get('city', ""),
            "organization": leader.get('org', ""),
            "oPosition": leader.get('position', ""),
            "order": leader.get("position_order", ""),
            "cPosition": leader.get('present post', ""),
            "ePosition": leader.get('present post', ""),
            "url": leader.get('url', ""),
            "pId": pId,
            "flag": 2,
            'create_time': get_time(),
            'update_time': get_time()
        }
        es.index(index=__index_name__, document=doc, timeout='10m')
    f.close()

# 插入临时表格
def insert_temp(leader_info_json_path):
    mappings = {
            "properties": {
                "cName": {"type": "keyword"},
                "type": {"type": "keyword"},
                "province": {"type": "keyword"},
                "city": {"type": "keyword"},
                "organization": {"type": "keyword"},
                "oPosition": {"type": "keyword"},
                "leader_type":{"type": "integer"},
                "order": {"type": "integer"},
                "cPosition": {"type": "keyword"},
                "url": {"type": "keyword"},
                "shencha":{'type': 'text'}
            }
    }
    # 不存在领导人信息表，则创建表格;
    # jj = es.indices.exists(index='leader-info-temp')
    if es.indices.exists(index='leader-info-temp'): #is True:
        es.indices.delete(index='leader-info-temp')
    es.indices.create(index='leader-info-temp', mappings=mappings, settings={"max_result_window": 10000000})
    with open(leader_info_json_path, 'r', encoding='utf-8') as f:
        leader_infos = f.readlines()
        leader_infos = [json.loads(line.strip()) for line in leader_infos]
        # print(f"All new leaders = {len(leader_infos)}")
        esupdate_logger.info(f"All New-Leaders={len(leader_infos)}")
    for info in tqdm(leader_infos,desc='insert temp'):
        leader = info 
        leader_order = leader.get("position_order", -1)
        if leader_order==-1:
            if leader.get('city', ""):
                leader_order = 1
            else:
                leader_order = 1000
        doc = {
            "cName": leader.get('name', ""),
            "type": leader.get('parent', ""),
            "province": leader.get('province', ""),
            "city": leader.get('city', ""),
            "organization": leader.get('org', ""),
            "oPosition": leader.get('position', ""),
            "leader_type": leader.get("leader_type", 0), 
            "order": leader_order, #leader.get("position_order", 1000000),
            "cPosition": leader.get('present post', ""),
            "shencha": leader.get("shecha",""),
            "url": leader.get('url', "")
        }
        es.index(index='leader-info-temp', document=doc, timeout='10m')
    time.sleep(2)
    f.close()

# 获取指定机构中所有人物
def get_leader_by_org(parent=None, province=None, city=None, org=None, index=__index_name__):
    _doc = {"match_all": {}}
    res = es.search(index=index, query=_doc, timeout='10m', size=10000)
    if res.get("hits", []).get("hits", []):
        result_data = [item for item in res["hits"]["hits"] if item["_source"]["province"]==province and item["_source"]["city"]==city and item["_source"]["organization"]==org]
    else:
        result_data = []
    return result_data

def es_index_operate(doc: dict, operate:str, index_name, _id):
    '''对es表格进行增删改操作'''
    if operate not in ["Insert", "Delete", "Update"]:
        raise ValueError("operate must be Insert, Update, or Delete!")
        return False
    return True

# 更新同一机构中人员信息
def update_org_leader_info(parent, province, city, org, leaders_info_to_be_updated):
    new_leader_list = get_leader_by_org(parent=parent, province=province, city=city, org=org, index='leader-info-temp')  #新解析的数据会存入leader-info-temp中
    old_leader_list = get_leader_by_org(parent=parent, province=province, city=city, org=org, index=__index_name__)   #从默认的__index_name__中读取原有数据
    esupdate_logger.info("===="*20)
    esupdate_logger.info("orgtype=【{}】\t province=【{}】\t city=【{}】\t org=【{}】".format(parent, province, city, org))
    esupdate_logger.info("len(new_leaders)={}".format(len(new_leader_list)))
    esupdate_logger.info("len(old_leaders)={}".format(len(old_leader_list)))
    if not new_leader_list:
        esupdate_logger.warning("ES 新领导人获取失败！")
        return
    
    old_leader_info_list = []
    old_name_list = []
    for old_leader in old_leader_list:
        old_leader_name = old_leader["_source"]["cName"]
        old_position = old_leader["_source"]["oPosition"]
        temp = {"name": old_leader["_source"]["cName"], "position": old_position, "leader_type":old_leader["_source"]["leader_type"], "title": old_leader["_source"].get("cPosition", "")}  
        # temp = {'name': old_leader['_source']['cName'], 'position': old_leader['_source']['oPosition'], 'title': old_leader['_source']['cPosition'], "url":old_leader["_source"]["url"]}  #为二十大修改
        old_leader_info_list.append(temp)
        old_name_list.append(old_leader["_source"]["cName"])


    for new_leader in new_leader_list:
        new_name = new_leader["_source"]["cName"]
        new_position = new_leader["_source"]["oPosition"]

        new_positionorder =  new_leader["_source"]["order"]
        new_url = new_leader["_source"]["url"]
        new_url = extract_true_url(new_url)
        shencha = new_leader["_source"]["shencha"]
        new_info = {"name": new_name, "position": new_position, "leader_type":new_leader["_source"]["leader_type"], "title": new_leader["_source"]['cPosition']} 
        if new_leader["_source"].get("leader_type")==2 or new_leader["_source"].get("leader_type")==1:
            esupdate_logger.info("需要确认 {}".format(new_leader["_source"]))
            continue
        if "违纪违法" in shencha:
            esupdate_logger.warning(r"{} 落马".format(new_leader["_source"]))
            continue

        update_id = 0
        update_type = 2  #更新类型： 0 不需要更新， 1 需要更新职务信息， 2 需要插入新人物
        if new_name in old_name_list:
            if new_info in old_leader_info_list:  #
                update_type = 0    # 名字和职务都相同
                p_idx = old_leader_info_list.index(new_info)
                old_leader_list[p_idx]["_source"]["processed"] = 1
            else:
                update_type = 2   # 名字相同，职务或leader_type不同
                for p_idx, old_temp in enumerate(old_leader_info_list):
                    if old_temp["name"] == new_name:
                        old_leader_list[p_idx]["_source"]["processed"] = 2   # 新表与旧表领导人姓名相同，职务或leader_type不同，需要删除旧表中的人物
        else:
            update_type = 2      # 需要插入的人物
            # 更新职务
        if update_type==0:
            continue
        elif update_type == 1:
            doc = {
                'oPosition': new_position,
                'order': new_positionorder,
                'cPosition': new_leader['_source']['cPosition'],
                'flag': 1,
                'update_time': get_time()
            }
            url = extract_true_url(new_leader['_source']['url'])
            if url:  # 如果有url 则更新url和pId
                pId = hashlib.md5(url.encode('utf-8')).hexdigest()
                doc['url'] = url
                doc['pId'] = pId
            if write_to_ES:
                es.update(index=__index_name__, id=update_id, doc=doc, timeout='10m')
        # 插入新人物
        elif update_type == 2:
            pId = ""
            url = new_leader['_source']['url']
            if url:
                pId = hashlib.md5(url.encode('utf-8')).hexdigest()
            if new_leader["_source"]["cPosition"]:
                new_title = new_leader["_source"]["cPosition"]
            else:
                new_title = ''  # 如果没有url，当前title置为空
            leader_type=0
            if "shencha" in new_leader["_source"].keys():
                shencha = new_leader["_source"]["shencha"]
                if shencha: 
                    if shencha.find("不确定，只有曾任没有现任信息"):
                        leader_type = 1
                    elif shencha.find("涉嫌违纪违法"):
                        leader_type =2
                    else:
                        leader_type=3
                '''                
                # if shencha and shencha.find("退休")!=-1:
                #     leader_type = 1
                # pattern = re.compile("涉嫌.{1,5}违纪违法|接受.{1,10}审查|接受.{1,10}调查")
                # if shencha and pattern.findall(shencha):# and shencha.find("涉嫌违纪违法")!=-1 or shencha.find(""):
                #     leader_type = 2'''
            doc = {
                "cName": new_name,
                "eName": new_name,
                "type": parent,
                "province": province,
                "city": city,
                "organization": org,
                "oPosition": new_position,
                "leader_type": leader_type,
                "order": new_positionorder,
                "cPosition": new_title,
                "ePosition": new_title,
                "url": url,
                "pId": pId,
                "flag": 2,
                'create_time': get_time(),
                'update_time': get_time()
            }
            sss = {"parent": parent, "province": province, "city": city, "org": org, "name": new_name, "position": new_position, "title": new_leader['_source']['cPosition'], "leader_type": leader_type, "url":url}
            if not sss.get("title", ""):
                if new_name in old_name_list:
                    old_title = [item.get("title", "") for item in old_leader_info_list if item["name"]==new_name]
                    for item in old_title:
                        if item.strip():
                            sss["title"]=item.strip()
                            doc["cPosition"], doc["ePosition"] = item.strip(), item.strip() 
                            index_in_leader_to_be_updated = [value for index, value in enumerate(leaders_info_to_be_updated) if (value["url"]==url and url)]
                            if index_in_leader_to_be_updated:
                                shencha = index_in_leader_to_be_updated[0].get("shencha", "")
                                if shencha:
                                    doc["leader_type"]=2
                                    sss["leader_type"]=2
                            esupdate_logger.info(f"{new_name}的 title 来自旧信息 【{item}】")
                            break        
            time.sleep(1)
            if doc["leader_type"]==2:
                esupdate_logger.info("新增疑似落马:{}".format(json.dumps(sss,ensure_ascii=False)))
            elif doc["leader_type"]==1:
                esupdate_logger.info("新增疑似退休:{}".format(json.dumps(sss,ensure_ascii=False)))
            else:
                esupdate_logger.info("新增:{}".format(json.dumps(sss,ensure_ascii=False)))
            if write_to_ES:
                es.index(index=__index_name__, document=doc, timeout='10m')

    for old_leader in old_leader_list:
        old_leader_source = old_leader["_source"]
        sss = {"parent": parent, "province": province, "city": city, "org": org, "name": old_leader_source["cName"], "position": old_leader_source['oPosition'], "title": old_leader_source['cPosition'], "url":old_leader_source["url"]}
        new_leader_name_list = [item["_source"]["cName"] for item in new_leader_list]
        in_new_leader_list = True if old_leader["_source"]["cName"] in new_leader_name_list else False
        old_leader_flag = old_leader.get("flat", 2)
        if in_new_leader_list==False: #在新表里没有人需要删除
            try:
                if old_leader_flag==-1:
                    esupdate_logger.warning("新表没有且flag=-1:{}".format(json.dumps(sss)))
                else:
                    if write_to_ES:
                        es.delete(index=__index_name__, id=old_leader['_id'], timeout='10m')
                    esupdate_logger.warning("旧表删除成功:{}".format(json.dumps(sss, ensure_ascii=False)))
            except Exception as errmsg:
                print("ES delete error:", errmsg)
                esupdate_logger.error("旧表人物删除失败: errmsg={}".format(errmsg))  
        elif "processed" not in old_leader_source: #old_leader["_source"]:
            old_url = old_leader_source["url"]
            index_in_leader_to_be_updated = [index for index, value in enumerate(leaders_info_to_be_updated) if value["url"]==old_url and old_url]
            if index_in_leader_to_be_updated:
                index_temp = index_in_leader_to_be_updated[0]
                title = leaders_info_to_be_updated[index_temp]["XianRen"]
                pre_title = leaders_info_to_be_updated[index_temp]["pre_title"]
                shencha  = leaders_info_to_be_updated[index_temp]["shencha"]
                if shencha:# 领导人疑似落马
                    esupdate_logger.info("落马：{}".format(sss))
                    esupdate_logger.info("疑似落马:{}".format(json.dumps(sss,ensure_ascii=False)))
                    esupdate_logger.info("url:{}".format(old_url))
                    doc = {
                        "cName": old_leader["_source"]["cName"],
                        "eName": old_leader["_source"]["eName"],
                        "type": old_leader["_source"]["type"],
                        "province": old_leader["_source"]["province"],
                        "city": old_leader["_source"]["city"],
                        "organization": old_leader["_source"]["organization"], #org,
                        "oPosition": old_leader["_source"]["oPosition"],#new_position,
                        "leader_type": 2, 
                        "order": old_leader["_source"]["order"],
                        "cPosition": "{},{}".format(title,shencha),# new_title,
                        "ePosition": "{},{}".format(title,shencha), #new_title,
                        "url": old_leader["_source"]["url"],
                        "pId": old_leader["_source"]["pId"],
                        "flag": 2,
                        'create_time': old_leader["_source"]["create_time"],
                        'update_time': get_time()
                    }         
                                    
                    try:
                        if old_leader_flag!=-1:
                            if write_to_ES:  
                                es.update(index=__index_name__, id=old_leader["_id"], doc=doc, timeout="10m")
                        else:
                            esupdate_logger("flag=-1人员需要更新为落马: {}".format(doc))
                    except Exception:
                        esupdate_logger.warning("落马 ES update error:{}".format(Exception))
                        esupdate_logger.warning("落马数据更新失败 {}".format(Exception))
                    continue
                elif pre_title:
                    if not title and old_leader["_source"]["type"] in ["领导机构", "国务院机构"]:
                        leader_type = 1  # 疑似退休 20241024改为不确定  
                        esupdate_logger.info("不确定: {}".format(json.dumps(sss,ensure_ascii=False)))             
                        doc = {
                            "cName": old_leader["_source"]["cName"],
                            "eName": old_leader["_source"]["eName"],
                            "type": old_leader["_source"]["type"],
                            "province": old_leader["_source"]["province"],
                            "city": old_leader["_source"]["city"],
                            "organization": old_leader["_source"]["organization"], #org,
                            "oPosition": old_leader["_source"]["oPosition"],#new_position,
                            "leader_type": leader_type, 
                            "order": old_leader["_source"]["order"],
                            "cPosition": pre_title,# new_title,
                            "ePosition": pre_title, #new_title,
                            "url": old_leader["_source"]["url"],
                            "pId": old_leader["_source"]["pId"],
                            "flag": 2,
                            'create_time': old_leader["_source"]["create_time"],
                            'update_time': get_time()
                        }
                        if write_to_ES:
                            try:
                                if old_leader_flag!=-1:
                                    es.update(index=__index_name__, id=old_leader["_id"], doc=doc, timeout="10m")
                                else:
                                    esupdate_logger.warning("flag=-1人员改为不确定：{}".format(json.dumps(doc, ensure_ascii=False)))
                            except Exception:
                                esupdate_logger.error("退休 ES update error: {}".format(old_leader["_id"], old_leader["_source"]["cName"], Exception))
                        # es.update_by_query()
                        continue
                    else:
                        esupdate_logger.info("删除：".format(json.dumps(sss, ensure_ascii=False)))
                        time.sleep(2)
                        try:
                            if old_leader_flag!=-1:
                                if write_to_ES:
                                    es.delete(index=__index_name__, id=old_leader['_id'], timeout='10m')
                                esupdate_logger.warning("删除_id={}".format(old_leader["_source"]))
                            else:
                                esupdate_logger.waring("flag=-1人员需要删除:{}".format(json.dumps(old_leader, ensure_ascii=False)))
                        except Exception:
                            print("ES delete error:", Exception)
                            esupdate_logger.error("ES delete error: errms={}".format(Exception))
                else:
                    time.sleep(2)               
                    try:
                        if old_leader_flag!=-1:
                            if write_to_ES:
                                es.delete(index=__index_name__, id=old_leader['_id'], timeout='10m')
                            esupdate_logger.warning("旧表删除成功：{}".format(json.dumps(old_leader["_source"], ensure_ascii=False)))
                        else:
                            esupdate_logger.warning("flag=-1人员需要删除：{}".format(json.dumps(old_leader["_source"], ensure_ascii=False)))
                    except Exception as errmsg:
                        # print("ES delete error:{}".format(old_leader['_id'], Exception) )
                        esupdate_logger.error("ES 删除 error: _id={} errmsg={}".format(old_leader['_id'], errmsg))
            else:      
                try:
                    if old_leader_flag!=-1:
                        if write_to_ES:
                            es.delete(index=__index_name__, id=old_leader['_id'], timeout='10m')
                        esupdate_logger.warning("删除：{}".format(json.dumps(old_leader.get("_source", {}), ensure_ascii=False)))
                    else:
                        esupdate_logger.warning("flag=-1人员需要删除：{}".format(json.dumps(old_leader, ensure_ascii=False)))
                except Exception:
                    # print("删除not in to-be-updated Error", old_leader["cName"], timeout="10m")
                    esupdate_logger.error("删除not in to-be-updated Error {}".format(old_leader["cName"]))
        else:
            if old_leader['_source']["processed"] == 2:            
                try:
                    if old_leader_flag!=-1:
                        if write_to_ES:
                            es.delete(index=__index_name__, id=old_leader['_id'], timeout='10m')
                        esupdate_logger.warning("旧表删除成功：{}".format(json.dumps(old_leader["_source"], ensure_ascii=False)))
                    else:
                        esupdate_logger.warning("flag=-1人员需要删除：{}".format(json.dumps(old_leader["_source"], ensure_ascii=False)))
                except Exception:
                    esupdate_logger.error("ES delete error:{}".format(Exception))

# 更新采集的人员数据
def update_leader_info(leader_info_json_path, leaders_info_to_be_updated, org_dict=[]):
    if not org_dict:
        print("Update leader info Error: org_dict is empty")
        return
    # 将新采集的新数据插入一个临时表，方便查询
    insert_temp(leader_info_json_path)
    time.sleep(1)

    cpc_org_list = ["中华人民共和国国务院", "中共中央", "中国共产党中央军事委员会", "中华人民共和国国家主席"]
    for parent in org_dict.keys():
        if parent not in ["地方"]:  #[中直机构、中管高校、军事机构...]
            if parent in ["领导机构"]:
                for org in org_dict[parent]:
                    update_org_leader_info(parent=parent, province="", city="", org=org, leaders_info_to_be_updated=leaders_info_to_be_updated)
            else:
                for org in org_dict[parent]:
                    update_org_leader_info(parent=parent, province="", city="", org=org, leaders_info_to_be_updated=leaders_info_to_be_updated)
        else:  # 地方
            for province in org_dict["地方"].keys():
                update_org_leader_info(parent=parent, province=province, city="", org="中共", leaders_info_to_be_updated=leaders_info_to_be_updated)
                update_org_leader_info(parent=parent, province=province, city="", org="政协", leaders_info_to_be_updated=leaders_info_to_be_updated)
                update_org_leader_info(parent=parent, province=province, city="", org="人大", leaders_info_to_be_updated=leaders_info_to_be_updated)
                update_org_leader_info(parent=parent, province=province, city="", org="政府", leaders_info_to_be_updated=leaders_info_to_be_updated)
                for city in org_dict["地方"][province]:
                    if "city"=="辛集市":
                        print(city)
                    update_org_leader_info(parent="", province=province, city=city, org="中共", leaders_info_to_be_updated=leaders_info_to_be_updated)
                    update_org_leader_info(parent="", province=province, city=city, org="政府", leaders_info_to_be_updated=leaders_info_to_be_updated)



def copyfile2newdir(oldpath: str, newpath: str, filename: str):
    oldname = os.path.join(oldpath, filename)
    newname = os.path.join(newpath, filename)
    if os.path.exists(newname):
        os.remove(newname)
    shutil.copyfile(oldname, newname)

def copy_ES_index(escon = es, src_index = "leader-info", dest_index="leader-new"): # 从表src复制数据到表desc
    query = {
        "bool": {
            "must": []
        }
    }
    res = es.search(index=src_index, query=query, timeout='10m', size=10000)
    date_in_src = res["hits"]["hits"]
    mappings = {
            "properties": {
                "cName": {"type": "text"}, # 采集姓名
                "eName": {"type": "text"}, # 编辑姓名
                "type": {"type": "keyword"}, # 机构类型
                "province": {"type": "keyword"},  # 省
                "city": {"type": "keyword"}, # 市
                "organization": {"type": "keyword"}, # 机构名称
                "oPosition": {"type": "text"},  # 机构职位
                "order": {"type": "integer"},  # 职务顺序
                "cPosition": {"type": "text"}, # 现任职位
                "ePosition": {"type": "text"}, # 编辑职位
                "leader_type":{"type": "integer"}, # 领导人类型，0是时政人物，1是其他重点考虑退休人员
                "url": {"type": "keyword"}, # url链接
                "pId": {"type": "keyword"}, # 人员id
                "flag": {"type": "keyword"}, # 标志位，-1:锁定;0:已编辑;1:职务更新;2:职务姓名都更新
                "create_time": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"},
                "update_time": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"}
            }
    }
    dest_exist = es.indices.exists(index=dest_index)
    if not dest_exist:
        es.indices.create(index=dest_index, mappings=mappings, settings={"max_result_window":10000000})
        for leader_info in date_in_src:
            if leader_info["_source"]["cName"]:
                doc = {
                        "cName": leader_info["_source"]["cName"],
                        "eName": leader_info["_source"]["eName"],
                        "type": leader_info["_source"]["type"],
                        "province": leader_info["_source"]["province"], #province,
                        "city": leader_info["_source"]["city"], #city,
                        "organization": leader_info["_source"]["organization"], #org,
                        "oPosition": leader_info["_source"]["oPosition"], #new_position,
                        "order": leader_info["_source"]["order"], #new_positionorder,
                        "cPosition": leader_info["_source"]["cPosition"], #new_leader['_source']['cPosition'],
                        "ePosition": leader_info["_source"]["ePosition"], #new_leader['_source']['cPosition'],
                        "leader_type": 0,
                        "url": leader_info["_source"]["url"], #url,
                        "pId": leader_info["_source"]["pId"], #pId,
                        "flag": leader_info["_source"]["flag"], #2,
                        'create_time': leader_info["_source"]["create_time"], #get_time(),
                        'update_time': leader_info["_source"]["update_time"] #get_time()
                    }
            else:
                doc = {
                        "cName": leader_info["_source"]["eName"],
                        "eName": leader_info["_source"]["eName"],
                        "type": leader_info["_source"]["type"],
                        "province": leader_info["_source"]["province"], #province,
                        "city": leader_info["_source"]["city"], #city,
                        "organization": leader_info["_source"]["organization"], #org,
                        "oPosition": leader_info["_source"]["oPosition"], #new_position,
                        "order": 100, #new_positionorder,
                        "cPosition": leader_info["_source"]["cPosition"], #new_leader['_source']['cPosition'],
                        "ePosition": leader_info["_source"]["ePosition"], #new_leader['_source']['cPosition'],
                        "leader_type": 0,
                        "url": leader_info["_source"]["url"], #url,
                        "pId": leader_info["_source"]["pId"], #pId,
                        "flag": leader_info["_source"]["flag"], #2,
                        'create_time': leader_info["_source"]["create_time"], #get_time(),
                        'update_time': leader_info["_source"]["update_time"] #get_time()
                    } 
            es.index(index=dest_index, document=doc, timeout='10m')
        # print("Copy es({}->{}) success!".format(src_index, dest_index))
    else:
        print("Dest index exist!")
    time.sleep(2) 
    return


def main():
    mappings = {
            "properties": {
                "cName": {"type": "text"}, # 采集姓名
                "eName": {"type": "text"}, # 编辑姓名
                "type": {"type": "keyword"}, # 机构类型
                "province": {"type": "keyword"},  # 省
                "city": {"type": "keyword"}, # 市
                "organization": {"type": "keyword"}, # 机构名称
                "oPosition": {"type": "text"},  # 机构职位
                "order": {"type": "integer"},  # 职务顺序
                "cPosition": {"type": "text"}, # 现任职位
                "ePosition": {"type": "text"}, # 编辑职位
                "leader_type":{"type": "interger"}, # 领导人类型，0是时政人物，1是其他重点考虑退休人员
                "url": {"type": "keyword"}, # url链接
                "pId": {"type": "keyword"}, # 人员id
                "flag": {"type": "keyword"}, # 标志位，-1:锁定;0:已编辑;1:职务更新;2:职务姓名都更新
                "create_time": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"},
                "update_time": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"}
            }
    }
    # 不存在领导人信息表，则创建表格
    current_file_path = os.path.dirname(__file__)   #获取当前文件的绝对路径
    read_write_path = os.path.join(os.path.abspath(os.path.join(current_file_path, '..')), r'runtime/20241028')
    es_indx = es.indices.exists(index=__index_name__)
    # if es.indices.exists(index=__index_name__) is not True:
    timestr = str(time.strftime("%Y_%m_%d", time.localtime()))
    # timestr = "2023_02_03"

    message_file_name = os.path.join(read_write_path, "message.txt")
    if os.path.exists(message_file_name):
        os.remove(message_file_name)
    message_file_obj = open(message_file_name, 'w', encoding='utf-8')

    if not es_indx:
        es.indices.create(index=__index_name__, mappings=mappings, settings={"max_result_window":10000000})
        # 插入数据
        insert_leader_info(os.path.join(read_write_path, 'whole_leader_info.json'))
        message_file_obj.write("{} is not exist!!!\n".format(__index_name__))
    else:
        # 更新数据
        whole_data_file = os.path.join(read_write_path, "whole_leader_info.json")   #= os.path.abspath(os.path.join(os.path.dirname(__file__),'..','leader_info'))
        """# print('----',whole_data_file)
        # if os.path.exists(whole_data_file):
        #     os.remove(whole_data_file)"""
        whole_leader_info = open(whole_data_file, 'w', encoding='utf-8')
        minister_leader_file = "".join(["leader_info_", timestr, ".json"])
        # copyfile2newdir(read_write_path, os.path.join(read_write_path, "standby"), minister_leader_file)
        with open(os.path.join(read_write_path, minister_leader_file), 'r', encoding='utf-8') as zy_leader_obj:
            lines = zy_leader_obj.readlines()
            if len(lines) == 0:
                message_file_obj.write("中央领导人 uodate failed\n")
                fff = open(os.path.join(read_write_path, "standby", "".join(["leader-info", ".json"])), 'r', encoding='utf-8')
                lines = fff.readlines()
                fff.close()
            else:
                message_file_obj.write("中央领导人 update success\n")
                message_file_obj.write("len(zongyang):{}\n\n".format(len(lines)))

            for line in lines:
                line = eval(line)
                strr = json.dumps(line, ensure_ascii=False) + '\n'
                whole_leader_info.write(strr)
        zy_leader_obj.close()

        municipal_leader_file = "".join(["dfldly-leader-info-", timestr, ".json"])
        # copyfile2newdir(read_write_path, os.path.join(read_write_path, "standby"), municipal_leader_file)
        with open(os.path.join(read_write_path, municipal_leader_file), 'r', encoding='utf-8') as city_leader_obj:
            lines = city_leader_obj.readlines()
            if len(lines)==0:
                message_file_obj.write("地方领导人 upate failed\n")
                fff = open(os.path.join(read_write_path, "standby", "".join(["dfld-leader-info", ".json"])), 'r', encoding='utf-8')
                lines = fff.readlines()
                fff.close()
            else:
                message_file_obj.write("地方领导人 upate success\n")
                message_file_obj.write("len(difang):{}\n\n".format(len(lines)))

            for line in lines:
                line = eval(line)
                strr = json.dumps(line, ensure_ascii=False) + '\n'
                whole_leader_info.write(strr)

        whole_leader_info.close()
        with open(os.path.join(read_write_path, "whole_leader_info.json"), 'r', encoding='utf-8') as fobj:
            lines = fobj.readlines()
            if len(lines)==0:
                message_file_obj.write("The file of [whole-leaader-info.json] is empty")
            else:
                message_file_obj.write("The file of [whole-leaader-info.json] isnot empty\n")
                message_file_obj.write("len(whole):{}\n\n".format(len(lines)))
        message_file_obj.close()
        # time.sleep(2)
        # add_url_info(r'C:\Users\<USER>\Desktop\whole_leader_info.json')
        update_leader_info(os.path.join(read_write_path, 'whole_leader_info.json'))

def update_by_id(es, index_name, doc_id):
    update_script = {
    "script": {
        "source": "ctx._source.oPosition = param.c1; ctx._source.cPosition=param.c2;",
        "params":{
            "c1": "shili",
            "c2": "lizi2"
        },
        "lang": "painless"
        }
    }
    es.update(index=index_name, id=doc_id, body=update_script)

def main2(work_dir="", org_dict=[]):
    import json, requests
    if work_dir=="":
        work_dir="/home/<USER>/leader-info/UpdateLeaderInfo/runtime/20250416"
    if not org_dict:
        resp = requests.get("http://g12.guodata.com:9204/api/v2/pdetect/organizations?type=0")
        org_dict = resp.json()
    with open(os.path.join(work_dir, 'leader_desc.json'), "r", encoding="utf-8") as fp:
        leaders_info_to_be_updated=json.load(fp)

    update_leader_info(leader_info_json_path=os.path.join(work_dir, "whole_leader_info.json"), leaders_info_to_be_updated=leaders_info_to_be_updated, org_dict=org_dict)
     

if __name__=="__main__":
    main2()
    # 筛查leader-info-final中有url确没有cPosition和ePosition的记录
    # get_item_withurl_missingcposition(es=es, index=__index_name__)   