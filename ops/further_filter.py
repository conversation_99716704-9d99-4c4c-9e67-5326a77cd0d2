## 从现任领导人信息中过滤落马官员
## 采用ner

from venv import logger
import requests, re, os, json, sys, warnings, time
from datetime import datetime
from fuzzywuzzy import fuzz, process
import traceback
from elasticsearch import Elasticsearch
sys.path.append("/home/<USER>/bazhuayu_data/dangshi_ner")
# from ner_service_people import NERService
from datetime import datetime
import logging

def check_overlap(list1, list2):
    for item in list1:
        if item in list2:
            return True
    return False

def ignore_warnings():
    warnings.filterwarnings("ignore", category=DeprecationWarning)
    warnings.filterwarnings("ignore", category=UserWarning)

class Leader_Info_Filter():
    def __init__(self, logger_path="/home/<USER>/leader-info/UpdateLeaderInfo/runtime/{}/logs".format(datetime.now().strftime("%Y%m%d"))):
        '''将落马官员从时政人物中删除
        log信息保存在/home/<USER>/leader-info/UpdateLeaderInfo/runtime/{}/logs'''
        configfile = os.path.join(os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__),"..")), "configs"), "es_config.json")
        # self.nerservice = NERService()
        try:
            with open(configfile, "r", encoding="utf-8") as fp:
                CONFIG_ES = fp.read()
                CONFIG_ES = json.loads(CONFIG_ES)
                self.es = Elasticsearch(CONFIG_ES['host'], basic_auth=(CONFIG_ES['user'], CONFIG_ES['passwd']), request_timeout=120, max_retries=10, retry_on_timeout=True)
                self.fallen_leader_index = CONFIG_ES["fallen_leader_index"]
                self.leader_info_index = CONFIG_ES["leader_info_index"]

            self.filterlogger = logging.getLogger("fallen_filter")
            self.filterlogger.setLevel(logging.INFO)
            filehandler = logging.FileHandler(filename=os.path.join(logger_path, "fallen_filt_result.log") , mode="w")
            formatter = logging.Formatter("%(levelname)s | %(message)s")
            filehandler.setFormatter(formatter)
            self.filterlogger.addHandler(filehandler)
            self.runing_path = os.path.abspath(os.path.join(logger_path, ".."))
        except:
            print(traceback.format_exc(limit=3, chain=True))
            return
        pass
    
    # 从es的index中获取领导人信息
    def get_alldocuments_from_index(self, index_name):   
        '''从index_name中获取doc信息
        返回res[hits][hits]''' 
        query = {
        "bool": {
            "must": []
            }
         }
        try:
            res = self.es.search(index=index_name, query=query, timeout='10m', size=10000)
        except:
            print(traceback.format_exc(limit=3, chain=True))
            return []
        return res['hits']['hits']
    
    def delete_document(self, index_name, doc_id):
        '''从index_name中删除doc_id'''
        try:
            self.es.delete(index=index_name, id=doc_id, timeout="10m")
        except:
            self.filterlogger.warning(traceback.format_exc(limit=5, chain=True))
            self.filterlogger.warning("删除失败：{}->{}".format(index_name, id))
            # print(traceback.format_exc(limit=5, chain=True))
        return


    # 获取落马官员数据信息
    def get_fallen_leader_info(self,  fallen_index):
        fallen_leader_info = []
        try:
            fallen_leader_info = self.get_alldocuments_from_index(self.fallen_leader_index)
        except:
            fallen_leader_info = []
            self.filterlogger.warning(traceback.format_exc(limit=3,chain=True))
            self.filterlogger.warning("读取落马index失败")
            # print(traceback.format_exc(limit=3, chain=True))
        return fallen_leader_info

    # 获取时政人物信息
    def get_leader_info(self, leader_info_index):
        leader_info = []
        try:
            leader_info = self.get_alldocuments_from_index(self.leader_info_index)
        except:
            self.filterlogger.warning(traceback.format_exc(limit=3,chain=True))
            self.filterlogger.warning("读取时政index失败")
            leader_info = []
            # print(traceback.format_exc(limit=3, chain=True))
        return leader_info

    # 从时政人物数据库中删除落马官员信息
    def del_fallenleader_from_leaderinfo(self):
        #
        jsondata = [] # 写入json文件的数据
        # Step1: 从index中读取数据
        self.filterlogger.info("delete fallen leaders from {}".format(self.leader_info_index))
        fallen_leader_info = self.get_fallen_leader_info(self.fallen_leader_index)
        leader_info = self.get_leader_info(self.leader_info_index)
        
        
        # 将删除记录写入json文件
        # self.runing_path = /home/<USER>/leader-info/UpdateLeaderInfo/runtime/{}
        writefile = os.path.join(self.runing_path, "fallen_filter待确认.json")
        if not os.path.exists(self.runing_path):
            os.mkdir(self.runing_path)

        # Step2: 删除落马官员
            '''
            遍历落马，在时政中检索同名人。
            规则：二者的地域信息 + 相似度，
            规则一：地域信息相同，相似度>=60
            规则二：地域信息不同，相似度>=90，且二者职务信息至少有要7个字符重合
            '''
        with open(writefile, "w", encoding="utf-8") as fff:
            location_pat = r"([\u4e00-\u9fa5]{2}省|[\u4e00-\u9fa5]{2}市|[\u4e00-\u9fa5]\{3,5}自治区).*?"
            for fallen in fallen_leader_info:
                name = fallen["_source"]["name"]
                fallen_position = fallen["_source"]["position"]
                location = re.findall(location_pat, fallen_position)
                in_leader_info = [leader for leader in leader_info if leader["_source"]["cName"]==name]
                if in_leader_info:
                    for oneitem in in_leader_info:
                        delete_data = False
                        leader_info_position = oneitem["_source"]["oPosition"] if oneitem["_source"]["oPosition"] else oneitem["_source"]["ePosition"]
                        if leader_info_position is None:
                            leader_info_position = oneitem["_source"]["cPosition"]
                            continue    
                        whole_leaderinfo_position = oneitem["_source"]["province"]+oneitem["_source"]["city"]+leader_info_position    
                        match_radio = fuzz.partial_ratio(fallen_position, whole_leaderinfo_position) 
                        if name=="杨珊华":
                            print("name:{}".format(name))
                        if name == "张建春" and match_radio>70:
                            _id = oneitem["_id"]
                            try:
                                self.delete_document(self.leader_info_index, doc_id=_id)    
                            except Exception as errmsg:
                                self.filterlogger.info("--"*30)
                                self.filterlogger.warning("张建春时政人物删除失败 index={}\t_id={}".format(self.leader_info_index, _id))
                                self.filterlogger.warning(errmsg)
                                self.filterlogger.info("--"*30)
                        if match_radio==100:
                            _id=oneitem["_id"]
                            try:
                                self.delete_document(self.leader_info_index, doc_id=_id)    
                            except Exception as errmsg:
                                self.filterlogger.info("--"*30)
                                self.filterlogger.warning("match_radio=100 {} 时政人物删除失败 index={}\t_id={}".format(name,self.leader_info_index, _id))
                                self.filterlogger.warning(errmsg)
                                self.filterlogger.info("--"*30)
                        else:
                            if check_overlap(location, [oneitem["_source"]["province"], oneitem["_source"]["city"]]) or match_radio>90:
                                whole_leaderinfo_position = oneitem["_source"]["province"]+oneitem["_source"]["city"]+leader_info_position
                                _id = oneitem["_id"]
                                if check_overlap(location, [oneitem["_source"]["province"], oneitem["_source"]["city"]]) and match_radio>=60:
                                    delete_data = True  # 删除
                                    try:
                                        self.delete_document(self.leader_info_index, doc_id=_id)
                                    except Exception as errmsg:
                                        self.filterlogger.info("--"*30)
                                        self.filterlogger.warning("{} 时政人物删除失败 index={}\t_id={}".format(name, self.leader_info_index, _id))
                                        self.filterlogger.warning(errmsg)
                                        self.filterlogger.info("--"*30)
                                elif not check_overlap(location, [oneitem["_source"]["province"], oneitem["_source"]["city"]]) and match_radio>90:
                                    str_len = min(7, min(len(fallen_position), len(whole_leaderinfo_position)))
                                    if fallen_position[:str_len]==whole_leaderinfo_position[:str_len]:
                                        delete_data = True
                                        try:
                                            self.delete_document(self.leader_info_index, doc_id=_id)
                                        except Exception as errmsg:
                                            self.filterlogger.info("--"*30)
                                            self.filterlogger.warning("{} 时政人物删除失败 index={}\t_id={}".format(name, self.leader_info_index, _id))
                                            self.filterlogger.warning(errmsg)
                                            self.filterlogger.info("--"*30)
                                    else:
                                        delete_data = False
                                else:
                                    delete_data = False
                            
                                pending_data = {"name": name, "fallen_position": fallen_position,\
                                                "location": location, "match_radio": match_radio,\
                                                "_id":_id, "delete": delete_data,\
                                                "leader_info_index": oneitem}
                                if pending_data not in jsondata:
                                    jsondata.append(pending_data)  # jsondata是写入json文件的数据，可以通过这个文件确认是否需要删除该数据
                                self.filterlogger.info("--"*30)
                                if delete_data:
                                    self.filterlogger.warning(json.dumps(pending_data, ensure_ascii=False))
                                else:
                                    self.filterlogger.info(json.dumps(pending_data, ensure_ascii=False))   
            fff.write(json.dumps(jsondata, ensure_ascii=False, indent=2))
                                    
        return



if __name__=="__main__":
    ignore_warnings()
    leader_filter = Leader_Info_Filter()
    leader_filter.del_fallenleader_from_leaderinfo()