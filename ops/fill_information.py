from dataclasses import is_dataclass
# from lib2to3.pytree import _Results
from email.quoprimime import body_check
import os, json, re, requests, random, math, time, pdb, sys, logging
from urllib.parse import quote_plus
from lxml import etree
from elasticsearch import Elasticsearch
sys.path.append(os.path.abspath(os.path.dirname(__file__)))
from anti_crawl import anti_yanzheng, page_evaluate
from datetime import datetime
from fuzzywuzzy import fuzz
import traceback
import sys, asyncio
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from models.http_client import HttpClient


from runner import UpdateLeaderInfo
from esops import get_date_from_ES
from update_ES_leader_info import get_time, update_leader_info
from leader_cv.leader_cv_main import parse_pagecontent
import datetime, schedule
from urllib.parse import quote_plus
from parsers.parser_leader_html import ParseLeaderPage



'''
def get_lemmas(page_resource, max_li_num=50):
    results = []
    pat = re.compile("<script>window.PAGE_DATA=(.*?)</script>")#('{"lemmaId":.*?,"lemmaTitle":.*?,"lemmaDesc":".{2,50}","isRedirect":false,"redirectId":0,"redirectTitle":"","redirectDesc":""}')
    #{"lemmaId":16014723,"lemmaTitle":"王力军","lemmaDesc":"新疆乌鲁木齐市中级法院党组书记、副院长","isRedirect":false,"redirectId":0,"redirectTitle":"","redirectDesc":""}
    page_data_script = "".join(pat.findall(page_resource))
    if page_data_script:
        page_data = json.loads(page_data_script)
        lemmacnt = int(page_data.get("lemmaCnt")) if page_data.get("lemmaCnt") else 0
        if lemmacnt>1:
            lemmas = page_data.get("navigation").get('lemmas')
            for idx in range(min(max_li_num, len(lemmas))):
                lemma = lemmas[idx]
                if 'uid' in lemma:
                    continue
                _id = lemma.get("lemmaId")#"".join(re.compile('"lemmaId":(.*?),').findall(lemma))
                _desc = lemma.get("lemmaDesc")#"".join(re.compile('"lemmaDesc":\"(.*?)\",').findall(lemma))
                _title = lemma.get("lemmaTitle")#"".join(re.compile('"lemmaTitle":\"(.*?)\"').findall(lemma))
                _url = "/item/{}/{}".format(quote_plus(_title), _id) if _id else "/item/{}".format(quote_plus(_title))
                # print(f"{idx}: {_desc}----{_id}")
                tmp = {"href": _url, "title": _desc}
                if tmp not in results:
                    results.append(tmp)
        elif lemmacnt==1:
            _id = page_data.get("lemmaId")
            _desc = page_data.get("lemmaDesc")
            _title = page_data.get("lemmaTitle")
            _url = "/item/{}/{}".format(quote_plus(_title), _id) if _id else "/item/{}".format(quote_plus(_title))
            tmp = {"href": _url, "title": _desc}
            if tmp not in results:
                results.append(tmp)
        else:
            return results      
    else:
        root = etree.HTML(page_resource)
        li_list = root.xpath("//div[contains(@class, 'lemmaListTips')]//ul/li[contains(@class, lemmaItem)]")
        for li in li_list[:min(max_li_num, len(li_list))]:
            tag_a = li.xpath("./a")
            _title = "".join(tag_a.xpath("./text()")).strip()
            _url = "".join(tag_a.xpath("./@href")).strip()
            _url = re.sub("?fromModule=disambiguation", "", _url)
            tmp = {"href": _url, "title": _title}
            # print(f"{idx}: {_desc}----{_id}")
            if tmp not in results:
                results.append(tmp)
    return results
'''
# 获取es数据， leader-info-final
# 有url，根据url更新cPosition和ePosition
# 没有url，百科搜索
# 2024年4月24日 新增判断是否是退休、是否逝世，是否已经卸任当前机构的对应职务 
class FillLeaderInformation(object):
    def __init__(self, runningfile):
        # time.sleep(60)
        print("*"*60)
        print("补充当前领导人的url信息")
        logging.getLogger('elasticsearch').setLevel(logging.WARNING)
        file_path = os.path.dirname(__file__)
        leader_info = UpdateLeaderInfo(os.path.join(os.path.abspath(os.path.join(file_path, "..")),'configs/config.yaml'), use_cache=True)
        cwd = os.path.dirname(__file__)
        with open(os.path.join(os.path.abspath(os.path.join(cwd, "..")),"configs/es_config.json"), "r", encoding="utf-8") as fp:
            esconfig = json.load(fp=fp)
        self.es = Elasticsearch(esconfig["host"], basic_auth=(esconfig["user"], esconfig["passwd"]), request_timeout=120, max_retries=10, retry_on_timeout=True)
        self.leader_info_indexname = esconfig["leader_info_index"]
        self.current_leader_info = get_date_from_ES(self.es, index_name=esconfig["leader_info_index"])
        self.runningfile = open(runningfile, "w", encoding="utf-8")
        
        cvquery = {
            "bool":{
                "must":[]
                }
            }
        # 获取落马官员简历信息
        self.fallen_leader_cv_indexname = esconfig["fallen_leader_cv_index"] 
        self.old_fallen_leader_cv = self.es.search(index=esconfig["fallen_leader_cv_index"], query=cvquery, timeout='10m', size=10000)
        # 获取时政人物简历信息
        self.leader_cv_indexname = esconfig["leader_cv_new_index"]#"leader-cv-new"
        self.old_leader_cv = self.es.search(index=self.leader_cv_indexname, query=cvquery, timeout="10m", size=10000)
        time.sleep(5)
        
        self.with_url, self.without_url, self.update_position, self.dailyupdatedCount = 0, 0, 0, 0

    # 补充人物的职务和url信息
    def fill_position_url_info(self, index_name = "leader-info-final", start_time="2023-11-11 00:00:00", end_time = "2023-11-18 00:00:00"):
        
        # STEP1:获取领导人信息和_id
        if not self.current_leader_info:
            print("step1: ES leader_info obtained failed!")
            return
        # current_leader_info = [{**d, "processed": False} for d in self.current_leader_info]
        url_title_dict = {}

        # STEP2:遍历领导人信息，得到领导人的url， cposition， eposition信息
        for item_idx, item in enumerate(self.current_leader_info):
            time1 = datetime.datetime.now()
            if not item:
                continue
            item_source = item.get("_source")
            name, url, _org, _position, create_time, update_time, processed, _id, eposition = item_source["cName"], item_source["url"].strip(), item_source["organization"], item_source["oPosition"], item_source["create_time"], item_source["update_time"], item.get("processed"), item["_id"], item_source["ePosition"]
            create_time = str(create_time)
            update_time = str(update_time)
            tt1 = create_time>=start_time and create_time<=end_time
            tt2 = update_time>=start_time and update_time<=end_time
    
            if tt1 or tt2:
                self.dailyupdatedCount = self.dailyupdatedCount+1
            else:
                continue

            url = url.strip()
            if name in ["习近平"] or "/item/%E4%B9%A0%E8%BF%91%E5%B9%B3" in url: # 特殊处理
                leader_type, title, leader_url = 0, '中国共产党中央委员会总书记，中华人民共和国主席，中共中央军事委员会主席，中华人民共和国中央军事委员会主席', '/item/%E4%B9%A0%E8%BF%91%E5%B9%B3/515617'

            print("*"*60)
            print(f"{item_idx} : 【{_org}】  【{_position}】  【{name}】")
            self.runningfile.write("\n"+"*"*60+"\n")
            self.runningfile.write(r"{} : {}\n".format(item_idx, name))

            title, leader_url, leader_cv = "", "", ""
            try:
                # 保证相同的url，title都相等
                if url and url in url_title_dict.keys():
                    tmp_title = url_title_dict.get(url)
                    if eposition in tmp_title:
                        continue
                    else:
                        update_script = {
                            "script": {
                                "source": "ctx._source.update_time=params.c2; ctx._source.url=params.c3;ctx._source.ePosition=params.c1; ",
                                "params":{"c2": get_time(),"c3": url,"c1": tmp_title},
                                "lang": "painless"
                                }
                            }
                        self.es.update(index=index_name, id=_id, body=update_script, timeout="10m")
                        continue             
                leader_type, title, leader_url, leader_cv= self.process_leader_info(item_idx=item_idx, item=item, index_name=index_name)
            except:
                print(traceback.format_exc())
                self.runningfile.write(traceback.format_exc())
                continue

            # STEP3: 判断是否是落马、退休和去世
            if leader_type==1:
                self.runningfile.write(f"该官员已落马\t\tindex-name={index_name}\t_id={_id}\n")
                fallen_url = url if url else leader_url
                fallen_leader_info = {"name": name, "url": fallen_url, "position": title, "leader_cv": leader_cv}
                try:
                    self.es.delete(index=index_name, id=_id, timeout="10m")
                except Exception as e:
                    print(e)
                    print(f"id={_id} 落马官员删除失败\n")
                    self.runningfile.write(f"id={_id} 落马官员删除失败\n")
                    self.runningfile.write(traceback.format_exc())
                try:
                    update_status = self.update_leader_cv(leader_info=fallen_leader_info, index_name=self.fallen_leader_cv_indexname, flag="fallen")
                    self.runningfile.write("该官员已加入落马列表\n")
                except Exception as errmsg:
                    print(errmsg)
                    self.runningfile.write("{} 落马官员简历更新失败".format(fallen_leader_info))
                    self.runningfile.write(traceback.format_exc())
            elif leader_type==2: # 已经退休
                self.runningfile.write(f"该官员已退休\t\tindex-name={index_name}\t_id={_id}\n")
                print((f"该官员已退休\t\tindex-name={index_name}\t_id={_id}\n"))
                try:
                    updated_leader_info = item
                    updated_leader_info["_source"]["cPosition"] = title
                    updated_leader_info["_source"]["ePosition"] = title
                    update_status = self.update_leader_cv(leader_info=updated_leader_info, index_name=self.leader_info_indexname, flag="retired")
                    self.runningfile.write("该官员已经退休")
                except Exception as errmsg:
                    print(errmsg)
                    self.runningfile.write("{} 退休官员更新失败")
                    self.runningfile.write(traceback.format_exc())
               
            elif leader_type==3: # 已经去世
                updated_leader_info = item
                if title:
                    update_leader_info["_source"]["cPosition"] = title
                    update_leader_info["_source"]["ePosition"] = title
                update_status = self.update_leader_cv(leader_info=updated_leader_info, index_name=self.leader_info_indexname, flag="death")
                self.runningfile.write(f"该官员已逝世\t\tindex-name={index_name}\t_id={_id}\n")
                print(f"该官员已逝世\t\tindex-name={index_name}\t_id={_id}\n")
            

            # STEP4:判断是否需要补充: 
            try:
                if leader_type==0: # 如果现任不为空，则更新，考虑有些人工编辑epostion
                    update_url = url if url else leader_url
                    if update_url=="":
                        continue
                    else:
                        if leader_url not in url_title_dict.keys():
                            url_title_dict[leader_url] = title
                        tmp_title = url_title_dict[leader_url]
                        if eposition != tmp_title:
                            update_script = {
                            "script": {
                                "source": "ctx._source.update_time=params.c2; ctx._source.url=params.c3;ctx._source.ePosition=params.c1;ctx._source.cPosition=params.c1;",
                                "params":{"c2": get_time(),"c3": leader_url,"c1": tmp_title},
                                "lang": "painless"
                                }
                            }
                            # self.es.update(index=index_name, id=_id, body=update_script, timeout="10m")
                            self.es.update(index=index_name, id=_id, body=update_script, timeout="10m")
                            self.runningfile.write(f"Update: name={name}\t现eposition={title}\t\t原eposition={eposition}\t\turl={update_url}\n")
                            self.runningfile.write(f"Update: _id={_id}\n")
                            self.update_position += 1
                            # time.sleep(2)
                            if leader_cv:
                                new_leader_info = {"name": name, "url": update_url, "leader_cv": leader_cv}
                                update_status = self.update_leader_cv(index_name=self.leader_cv_indexname, leader_info=new_leader_info, flag="shizheng")
            except:
                print(traceback.format_exc())
            
            time2 = datetime.datetime.now()-time1
            print("运行时间：{}s".format(time2))
        self.runningfile.write(f"count(with_url)={self.with_url}\tcount(without_url)={self.without_url}\tupdate_position={self.update_position}") 
        self.runningfile.write(f"dailyUpdateCount={self.dailyupdatedCount}")        
        return
    

    # 简化原代码中的step2的内容
    def process_leader_info(self, item_idx, item, index_name = "leader-info-final"):
        time.sleep(1)
        item_source = item.get("_source")
        _org, _position,_id, eposition, url = item_source.get("organization"),item_source.get("oPosition"),item["_id"], item_source["ePosition"], item_source["url"].strip()

        leader_item = item.copy()
        title, leader_url, leader_cv= "", "", ""

        self.runningfile.write("\t"+item_source.get("organization")+"\n")
        # 有url，判断cposition和eposition是否和现网页上的信息一
        if len(url)>0: 
            # 根据请求信息得到最新的 ： 现任******。
            self.with_url += 1
            print("有url")
            self.runningfile.write(f"有url\n")
            url = re.sub("\?fromModule=disambiguation|https://baike.baidu.com | http://baike.baidu.com", "", url)
            item_pat = r"/item"
            parts = re.split(item_pat, url)
            parts = ['/item'+p for p in parts if p.startswith('/')]
            if len(parts)>1:    #因机构解析错误，导致部分领导url由多个item拼接而成
                url = parts[0]
                update_script = {
                            "script": {
                                "source": "ctx._source.update_time=params.c2; ctx._source.url=params.c3;",
                                "params":{
                                    "c2": get_time(),
                                    "c3": url
                                },
                                "lang": "painless"
                                }
                            }
                self.es.update(index=index_name, id=_id, body=update_script, timeout="10m")
                self.runningfile.write("update url(多item重叠)\n")
            leader_type, title, _, leader_cv, is_retirement, is_death = self.get_leader_title(leader_item, url=url)
            
            print(f"Parse-RES:leadertype={leader_type}\n\ttitle={title}\n\tleaderurl={url}")
            print(f"\tretirement={is_retirement}\t death={is_death}")
            self.runningfile.write(f"Parse-RES:leadertype={leader_type}\n\ttitle={title}\n\tleaderurl={url}")
            self.runningfile.write(f"\tretirement={is_retirement}\t death={is_death}")
            if is_retirement:
                leader_type=2
            if is_death:
                leader_type=3
            time.sleep(1)
            return leader_type, title, url, leader_cv
        else:
            print(r"没有url")
            self.runningfile.write(f"\t没有url\n")
            leader_type, title, leader_url, leader_cv, is_retirement, is_death = self.get_leader_title(leader_item, url="")   # 在get_leader_title，用姓名拼接url，requests请求
            if leader_url:
                self.without_url += 1
            print((f"Request-RES:leadertype={leader_type}\n\ttitle={title}\n\tleaderurl={leader_url}"))
            self.runningfile.write(f"Request-RES:leadertype={leader_type}\n\ttitle={title}\n\tleaderurl={leader_url}")
            if is_retirement:
                leader_type=2
            if is_death:
                leader_type=3
            return leader_type, title, leader_url, leader_cv
        

    # 更新官员信息，包括落马官员的简历、退休和去世官员信息
    # leader_name:姓名， fallen_leader_cv：官员简历, leader_url：百科主页, 
    def update_leader_cv(self, leader_info, index_name, flag = "shizheng"):
        if flag not in ["shizheng", "fallen", "retired", "death"]:
            print("update leader cv failed! flag should be shizheng or fallen.")
            return False
        if flag=="retired":
            try:
                _id = leader_info["_id"]
                update_script = {
                                "script": {
                                    "source": "ctx._source.update_time=params.c2; ctx._source.cPosition=params.c1;ctx._source.ePosition=params.c1;ctx._source.leader_type=params.c3;",
                                    "params":{"c2": get_time(),"c1": leader_info["_source"]["ePosition"],"c3": 2},
                                    "lang": "painless"
                                    }
                                }
                self.es.update(index=index_name, id=_id, body=update_script, timeout="10m")
            except:
                print(traceback.format_exc())
            return True
            

        elif flag=="death":
            try:
                _id = leader_info["_id"]
                update_script = {
                                "script": {
                                    "source": "ctx._source.update_time=params.c2; ctx._source.cPosition=params.c1;ctx._source.ePosition=params.c1;ctx._source.leader_type=params.c3;",
                                    "params":{"c2": get_time(),"c1": leader_info["_source"]["ePosition"],"c3": 3},
                                    "lang": "painless"
                                    }
                                }
                self.es.update(index=index_name, id=_id, body=update_script, timeout="10m")
            except:
                print(traceback.format_exc())
            return True
        
        
        else:
            new_url = leader_info["url"]
            new_url = new_url if new_url.startswith("http") else f"https://baike.baidu.com{new_url}"
            new_cv = leader_info["leader_cv"]
            if flag=="shizheng":
                leader_cv_data = self.old_leader_cv["hits"]["hits"]
                doc = {
                        "name": leader_info["name"],
                        "url": new_url,
                        "cv": new_cv,
                        "create_time": get_time(),
                        "update_time": get_time()
                    }
            if flag=="fallen":
                leader_cv_data = self.old_fallen_leader_cv["hits"]["hits"]
                doc = {
                        "name": leader_info["name"],
                        "url": new_url,
                        "cv": new_cv,
                        "position": leader_info["position"],
                        "create_time": get_time(),
                        "update_time": get_time()
                    }
            
                

            exist_url_list = [i_item["_source"]["url"] for i_item in leader_cv_data]

            if new_url in exist_url_list:
                _id = [i_item["_id"] for i_item in leader_cv_data if i_item["_source"]["url"].find(new_url)!=-1]
                if _id:
                    _id = _id[0]
                    query_by_id={
                                "match": {
                                    "_id": _id
                                }                            
                            }
                    old_leader_cv_hits = self.es.search(index=index_name, query=query_by_id, timeout="10m")
                    old_leader_cv = old_leader_cv_hits["hits"]["hits"][0]["_source"]["cv"] if old_leader_cv_hits and len(old_leader_cv_hits["hits"]["hits"])>0 else ""
                    if old_leader_cv!=new_cv:
                        doc["create_time"] = old_leader_cv_hits["hits"]["hits"][0]["_source"]["create_time"] 
                        self.es.update(index=index_name, id=_id, doc=doc, timeout="10m")     
                        # print("-------------------------------------------------------")
                        self.runningfile.write(r"【Update】name:[{}]    url:[{}]\n".format(leader_info["name"], new_url))
                        self.runningfile.write(f"index-name={index_name}\t\t_id={_id}\n")
                        time.sleep(2)
                else:
                    self.runningfile.write("【Insert】name:[{}]    url:[{}]\n".format(leader_info["name"], new_url))
                    self.es.index(index=index_name, document=doc, timeout="10m")     
                    time.sleep(2)
            else:
                self.runningfile.write("【Insert】name:[{}]    url:[{}]\n".format(leader_info["name"], new_url))
                self.es.index(index=index_name, document=doc, timeout="10m")     
                time.sleep(2)
            return True


    # 根据页面得到现任职务信息, url为空，需要检索，url不为空，可以直接请求url，得到title信息
    def get_leader_title(self, leader_info, url=""):
        # print("\n", leader_info)
        title, leader_url, leader_type, leader_cv = "", "", 0, ""

        if url:
            query_url = url if url.startswith("http") else "{}{}".format("https://baike.baidu.com", url) 
            page_resource, leader_url = self.my_requests(url=query_url)
            if not page_resource:
                print("get_leader_title: url requests failed-{}".format(leader_info["_source"]["cName"]))
                return leader_type, title, leader_url, leader_cv, False, False
            else:
                desc, polysemantlist, summary, leader_cv = self.get_page_info(page_resource=page_resource)
                is_retirement = self.check_retirement(page_resource=page_resource)
                is_death = self.check_death(page_resource=page_resource)
        else:
            name = quote_plus(leader_info["_source"]["cName"])
            query_url = "https://baike.baidu.com/item/{}".format(name) 
            # 请求网页
            page_resource, response_url = self.my_requests(url=query_url)   # response_url是有多义词时返回的默认url
            if not page_resource:
                print("get_leader_title: url requests failed-{}".format(leader_info["_source"]["cName"]))
                return leader_type, title, "", "", False, False
            # 获取页面信息，desc, summary, polysemantlist, leader信息
            desc, polysemantlist, summary, leader_cv = self.get_page_info(page_resource=page_resource, page_url=response_url)
            try:
                break_condition = False
                shengshi_type = leader_info["_source"]["type"]
                province = leader_info["_source"]["province"]
                city     = leader_info["_source"]["city"]
                oposition= leader_info["_source"]["oPosition"]
                organization = leader_info["_source"]["organization"]
                if polysemantlist:
                    for polyidx, polyitem in enumerate(polysemantlist):
                        polyposition = polyitem["title"]
                        if city and city[0:min(2, len(city))] in polyposition:
                            if polyidx==0:
                                break_condition = True
                                leader_url = polyitem["href"]
                                if leader_url not in response_url: # 有些人search的时候只出现polysemantlist列表, summary, desc, leader_cv全是空
                                    query_url = "{}{}".format("https://baike.baidu.com", polyitem["href"]) 
                                    page_resource, response_url = self.my_requests(url=query_url)
                                    desc, _, summary, leader_cv = self.get_page_info(page_resource=page_resource, page_url=response_url)
                                    is_retirement = self.check_retirement(page_resource=page_resource)
                                    is_death = self.check_death(page_resource=page_resource)
                            else:
                                query_url = "{}{}".format("https://baike.baidu.com", polyitem["href"]) 
                                page_resource, response_url = self.my_requests(url=query_url)
                                desc, polysemantlist, summary, leader_cv = self.get_page_info(page_resource=page_resource,  page_url=response_url)
                                break_condition = True
                                leader_url = polyitem["href"]
                                is_retirement = self.check_retirement(page_resource=page_resource)
                                is_death = self.check_death(page_resource=page_resource)
                            break
                        else:
                            joint_infomation = f"{province}{city}{organization}{oposition}"
                            partial_ratio = fuzz.partial_ratio(joint_infomation, polyposition)
                            if partial_ratio>=90:
                                query_url = "{}{}".format("https://baike.baidu.com", polyitem["href"])
                                page_resource, response_url = self.my_requests(url=query_url)
                                desc, polysemantlist, summary, leader_cv = self.get_page_info(page_resource=page_resource, page_url=response_url)
                                is_retirement = self.check_retirement(page_resource=page_resource)
                                is_death = self.check_death(page_resource=page_resource)
                                break_condition = True
                                leader_url = polyitem["href"]
                                break
                            else:
                                if organization in polyposition:
                                    query_url = "{}{}".format("https://baike.baidu.com", polyitem["href"])
                                    page_resource, response_url = self.my_requests(url=query_url)
                                    desc, polysemantlist, summary, leader_cv = self.get_page_info(page_resource=page_resource, page_url=response_url)
                                    is_retirement = self.check_retirement(page_resource=page_resource)
                                    is_death = self.check_death(page_resource=page_resource)
                                    break_condition = True
                                    leader_url = polyitem["href"]
                                    break
                    if break_condition:
                        pass
                    else:
                        leader_type, title, leader_url, leader_cv = 0, "", "", ""
                        return leader_type, title, leader_url, leader_cv, False, False
                else:
                    joint_infomation = f"{province}{city}{organization}{oposition}"
                    partial_ratio = fuzz.partial_ratio(joint_infomation, desc)
                    if city and city[0:min(2, len(city))] in desc:
                        str_len = min(6, min(len(joint_infomation), len(desc)))
                        if joint_infomation[0:str_len]==desc[0:str_len]:
                            leader_url = response_url
                        else:
                            if partial_ratio>=90:
                                leader_url = response_url
                    else:
                        if partial_ratio>=90:
                            leader_url = response_url
                        else:
                            title, summary, leader_cv = "", "", ""
            except:
                print(traceback.format_exc())
                self.runningfile.write(traceback.format_exc())
        
        shencha_res = ParseLeaderPage.check_fallen(page_content=page_resource)
        leader_type = 1 if shencha_res else 0
        title = ParseLeaderPage.get_xianren_title(page_content=page_resource)
        return leader_type, title, leader_url, leader_cv, is_retirement, is_death
        
        '''
        shencha_pat = re.compile("涉嫌.*?违纪违法|严重.*?违纪违法|开除.*?公职|开除.*?党籍|涉嫌.*?罪|依法.*?逮捕")
        if shencha_pat.findall(summary) and summary:
            leader_type = 1    # 可以更新fallen-leader-cv
            title_pat = re.compile("曾任(.*?)。")
            title = desc if desc else "".join(title_pat.findall(summary))
            return leader_type, title, leader_url, leader_cv, is_retirement, is_death

        title_pat = re.compile("现任(.*?)。")
        if title_pat.findall(summary) and summary:
            leader_type=0
            title = "".join(title_pat.findall(summary)) if "".join(title_pat.findall(summary)) else desc
            return 0, title, leader_url, leader_cv, is_retirement, is_death
        if not leader_url:
            title, leader_cv = "", ""
        return 0, title, leader_url, leader_cv, is_retirement, is_death
        '''
    
    # 找到输入内容中的中文字符
    def get_chinese_content(self,s):
        pattern = r'[\u4e00-\u9fa5]'  # 匹配汉字字符的正则表达式
        match = re.search(pattern, s)
        if match:
            start_index = match.start()
            chinese_char = match.group()
            content = s[start_index :]
            return content
        else:
            return ""
    
    # 解析页面，获取页面summary, desc, polysemantlist
    def get_page_info(self, page_resource,  page_url=""):
        summary_text=""
        desc_text=""
        polysemantlist = []
        leader_cv = ""
        if not page_resource:
            return desc_text, polysemantlist, summary_text, leader_cv
        if page_resource.find("抱歉，百度百科尚未收录词条")!=-1:
            return desc_text, polysemantlist, summary_text, leader_cv

        desc_text = ParseLeaderPage.get_description(page_content=page_resource)
        summary_text = ParseLeaderPage.get_summary(page_content=page_resource)
        summary_text = re.sub("\n|\[.*?\]|\(.*?\)|\xa0", "", summary_text) if summary_text else ""
        polysemantlist = ParseLeaderPage.get_polysemantlist(page_conten=page_resource, max_li_num=50)
        leader_cv = ParseLeaderPage.get_cv(page_content=page_resource)
        leader_cv = re.sub("\ax0", "", leader_cv) if leader_cv else ""
        return desc_text, polysemantlist, summary_text, leader_cv
        '''        
        page_tree = etree.HTML(page_resource)
        
        desc_text = "".join(page_tree.xpath("//div[contains(@class, 'lemmaDesc')]/text()"))
        #"".join(page_tree.xpath("//div[@class='lemma-desc']/text()"))  # 一般是代表人物身份的信息
        desc_text = re.sub("\[.*?\]|\(.*?\)", "", desc_text)
      
        summary_text = "".join(page_tree.xpath("//div[contains(@class, 'lemmaSummary')]//text()"))
        #"".join(page_tree.xpath("//div[@class='lemma-summary J-summary']//text()"))
        summary_text = re.sub("\n|\[.*?\]|\(.*?\)|\xa0", "", summary_text)
        '''
        # 新版百科
        max_li_num=50
        polysemantlist= get_lemmas(page_resource=page_resource, max_li_num=max_li_num)
        # print("len(polysemant)=", len(polysemantlist))

        leader_cv = parse_pagecontent(page=page_tree) #self.get_leader_cv(page_tree=page_tree)
        leader_cv = re.sub("\ax0", "", leader_cv) if leader_cv else ""

        return desc_text, polysemantlist, summary_text, leader_cv
    

    def join_text(self, text_list):
        text = ""
        for t in text_list:
            t = t.replace('\n','')
            if not t or t[0] == '[': # 剔除引用的方括号文本
                continue
            text += t
        pattern = re.compile('^\S{1}\s.*')
        if re.match(pattern, text):
            lis_text = list(text)
            lis_text.pop(1)
            text = "".join(lis_text)
        return text
    

    def my_request(self, url):
        page_resource = ""
        resp_url = ""
        headers = {"Content-Type": "application/json"}
        if url:
            body = {'url': url}
            try:
                response = requests.post("http://g12.guodata.com:9208/detect", headers=headers, data=json.dumps(body, ensure_ascii=False))
                data = response.json()
                resp_url = data.get("url")
                resp_url = re.sub("[https://baike.baidu.com|http://baike.baidu.com]", "", resp_url)
                page_resource = data.get("html")
            except:
                print("error in my_request")
                # print(traceback.format_exc())
        return page_resource, resp_url
    
    def my_requests2(self, url):
        import random
        user_agent_list = ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36 Edg/129.0.0.0",
                           "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36",
                           "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:130.0) Gecko/20100101 Firefox/130.0"]
        page_resource = ""
        resp_url = ""
        headers = {"user-agent":random.choice(user_agent_list)}
        if url:
            body = {'url': url}
            try:
                time.sleep(random.randint(20,30))
                response = requests.request("GET", headers=headers, url=url)
                if response.status_code==200:
                    response.encoding = response.apparent_encoding
                    page_resource = response.text
                    resp_url = response.url
                    resp_url = re.sub("[https://baike.baidu.com|http://baike.baidu.com]", "", resp_url)
            except:
                print("error in my_requests2")
                # print(traceback.format_exc())
        return page_resource, resp_url
    '''
    # 判断是否逝世
    def check_death(self, page_resource):
        is_death = False
        pattern = re.compile(r"\d+年\d+月.*?因病.*?逝世.*?享年\d+岁|\d+年\d+月.*?因车祸.*?逝世.*?享年\d+岁|\d+年\d+月.*?因病.*?去世.*?享年\d+岁|\d+年\d+月.*?因.*?逝世.*?享年\d+岁|\d+年\d+月.*?因.*?离世.*?享年\d+岁|\d+年\d+月.*?在.*?逝世.*?享年\d+岁")
        results = pattern.findall(page_resource)
        if results:
            is_death=True
        return is_death
    
    # 判断是否退休
    def check_retirement(self, page_resource):
        is_retirement = False
        pattern = re.compile("\d+年\d+月.*?免去{0,1}.*，退休|\d+年\d+月.*?因年龄原因.*?辞去.*?|\d+年\d+月.*?已到年{0,1}龄退休")
        results = pattern.findall(page_resource)
        if results:
            is_retirement=True
        return is_retirement
    '''
    # 判断是否已经卸任当前机构的职务
    def check_resignation_status(page_resource):
        is_resignation = False
        pattern = re.compile(r"不在担任.*?。|免去.*?职务。|从.*?卸任。")
        results = pattern.findall(page_resource)
        return is_resignation
        
    # 获取领导人信息和对应的_id
    def get_leader_id_source(self):
        leader_info = []
        for item in self.current_leader_info:
            _id = item["_id"]
            _source = item["_source"]
            info = {"_id": _id, "_source": _source}
            if info not in leader_info:
                leader_info.append(info)
        return leader_info
    
class FillFallenLeaderInformation():
    def __init__(self, fallenrunningfile, fillinfofile):
        self.fillinformation = FillLeaderInformation(runningfile=fillinfofile)   # 需要用到FillLeaderInformation里面的部分函数
        # 获取ES已经存储的fallen_leader姓名和职务信息
        fallen_leader_index = "fallen-leader-clean"
        self.old_fallen_leader = get_date_from_ES(self.fillinformation.es, fallen_leader_index)
        # 获取ES已经存储的fallen_leader的简历信息
        self.fallen_leader_cvindex = "fallen-leader-cv"
        self.fallen_leader_cv = get_date_from_ES(self.fillinformation.es, self.fallen_leader_cvindex)
        self.execute_file = open(fallenrunningfile, "w", encoding="utf-8") #open(os.path.join(os.path.dirname(__file__), "fallen_leader_cv_{}.txt".format(datetime.now().date())), "w", encoding="utf-8")
    
    def execute_fill(self, start_time="2023-11-01 00:00:00", end_time = "2023-11-10 00:00:00" ):
        start_idx = int(0)
        for leader_idx, leader_info in enumerate(self.old_fallen_leader[start_idx:]):
            _name = leader_info["_source"]["name"]
            _position = leader_info["_source"]["position"]
            _create_time = leader_info["_source"]["create_time"]
            if _create_time>=start_time and _create_time<=end_time:  
                print(f"\n-----------------{leader_idx+start_idx} {_name}:{_position}------------------")
                self.execute_file.write("*"*60)
                self.execute_file.write(f"\n{_name}:{_position}\n")
                query_body = {
                        "query": {
                            "match_phrase": {
                                "name": _name
                            }  # 查询所有文档
                        },
                        "sort": [
                            {"create_time": {"order": "desc"}}  # 按 create 字段降序排列
                        ]
                    }
                cv_search_result = get_date_from_ES(self.fillinformation.es, index_name=self.fallen_leader_cvindex, query=query_body)
                is_update = False
                if cv_search_result:
                    for cv_search_idx, cv_search_value in enumerate(cv_search_result):  # 专门为了显示,用的enumerate
                        name_in_cvindex = cv_search_value["_source"]["name"]
                        self.execute_file.write(f"{cv_search_idx}: name_in_cvindex={name_in_cvindex}\n")
                        position_in_cvindex = cv_search_value["_source"]["position"]
                        print(f"name_in_cvindex={name_in_cvindex}\t position_in_cv={position_in_cvindex}")
                        self.execute_file.write(f"{cv_search_idx}: name_in_cvindex={name_in_cvindex} \t position_in_cv={position_in_cvindex}\n")

                    for cv_search_idx, cv_search_value in enumerate(cv_search_result):
                        if is_update:
                            break
                        name_in_cvindex = cv_search_value["_source"]["name"]
                        self.execute_file.write(f"{cv_search_idx}: name_in_cvindex={name_in_cvindex}\n")
                        position_in_cvindex = cv_search_value["_source"]["position"]
                        # print(f"name_in_cvindex={name_in_cvindex}\t position_in_cv={position_in_cvindex}")
                        # self.execute_file.write(f"{cv_search_idx}: name_in_cvindex={name_in_cvindex} \t position_in_cv={position_in_cvindex}\n")
                        
                        match_radio = fuzz.partial_ratio(_position, position_in_cvindex)
                        str_len = min(6, min(len(position_in_cvindex), len(_position)))
                        if position_in_cvindex[0:str_len] in _position:
                            break
                        if match_radio>=90:
                            break
                        else:
                            query_url = "https://baike.baidu.com/item/{}".format(quote_plus(_name))
                            page_resource, resp_url = self.fillinformation.my_requests2(url = query_url)
                            desc, polysemantlist, summary, leader_cv = self.fillinformation.get_page_info(page_resource=page_resource, page_url=resp_url)
                            print(f"desc={desc}\npolysemantlist={polysemantlist}\nsummary={summary}")
                            shencha_pat = re.compile("涉嫌.*?违纪违法|严重.*?违纪违法|开除.*?公职|开除.*?党籍|涉嫌.*?罪|依法.*?逮捕")
                            
                            if not polysemantlist:
                                if shencha_pat.findall(summary) and summary:
                                    title_pat = re.compile("曾任(.*?)。")
                                    title = desc if desc else "".join(title_pat.findall(summary))
                                    leader_info = {"name":_name, "position":title, "leader_cv": leader_cv, "url": resp_url}
                                    self.fillinformation.update_leader_cv(leader_info=leader_info, index_name=self.fallen_leader_cvindex, flag="fallen")
                                    self.execute_file.write("position={}\turl={}\tleader_cv={}\n".format(title, resp_url, leader_cv))
                                    is_update=True
                            for polyidx, polyvalue in enumerate(polysemantlist):
                                if is_update:
                                    break
                                if polyidx==0:
                                    if shencha_pat.findall(summary) and summary:
                                        title_pat = re.compile("曾任(.*?)。")
                                        title = desc if desc else "".join(title_pat.findall(summary))
                                        leader_info = {"name":_name, "position":title, "leader_cv": leader_cv, "url": resp_url}
                                        self.fillinformation.update_leader_cv(leader_info=leader_info, index_name=self.fallen_leader_cvindex, flag="fallen")
                                        is_update=True
                                else:
                                    if is_update:
                                        break
                                    else:
                                        match_radio_1 = fuzz.partial_ratio(polyvalue["title"], _position)
                                        str_len_1 = min(6, min(len(polyvalue["title"]), len(_position)))
                                        if match_radio_1>=90 or _position[0:str_len_1] in polyvalue["title"]:
                                            query_url = polyvalue["href"] if polyvalue["href"].startswith("http") else r"https://baike.baidu.com{}".format(polyvalue["href"])
                                            page_resource, resp_url = self.fillinformation.my_requests2(url = query_url)
                                            desc, polysemantlist, summary, leader_cv = self.fillinformation.get_page_info(page_resource=page_resource, page_url=resp_url)
                                            print(f"desc={desc}\npolysemantlist={polysemantlist}\nsummary={summary}")
                                            if shencha_pat.findall(summary) and summary:
                                                title_pat = re.compile("曾任(.*?)。")
                                                title = desc if desc else "".join(title_pat.findall(summary))
                                                leader_info = {"name":_name, "position":title, "leader_cv": leader_cv, "url": resp_url}
                                                self.fillinformation.update_leader_cv(leader_info=leader_info, index_name=self.fallen_leader_cvindex, flag="fallen")
                                                is_update=True
                                        
                else:
                    query_url = "https://baike.baidu.com/item/{}".format(quote_plus(_name))
                    page_resource, resp_url = self.fillinformation.my_requests2(url = query_url)
                    if not page_resource:
                        self.execute_file.write("get page_resource failed!")
                        continue
                    desc, polysemantlist, summary, leader_cv = self.fillinformation.get_page_info(page_resource=page_resource, page_url=resp_url)
                    shencha_pat = re.compile("涉嫌.*?违纪违法|严重.*?违纪违法|开除.*?公职|开除.*?党籍|涉嫌.*?罪|依法.*?逮捕")
                    is_update = False
                    if not polysemantlist:
                        if shencha_pat.findall(summary) and summary:
                            title_pat = re.compile("曾任(.*?)。")
                            title = desc if desc else "".join(title_pat.findall(summary))
                            leader_info = {"name":_name, "position":title, "leader_cv": leader_cv, "url": resp_url}
                            self.fillinformation.update_leader_cv(leader_info=leader_info, index_name=self.fallen_leader_cvindex, flag="fallen")
                            self.execute_file.write("position={}\turl={}\tleader_cv={}\n".format(title, resp_url, leader_cv))
                            is_update=True
                    for polyidx, polyvalue in enumerate(polysemantlist):
                        if is_update:
                            break
                        if polyidx==0:
                            if shencha_pat.findall(summary) and summary:
                                title_pat = re.compile("曾任(.*?)。")
                                title = desc if desc else "".join(title_pat.findall(summary))
                                leader_info = {"name":_name, "position":title, "leader_cv": leader_cv, "url": resp_url}
                                self.fillinformation.update_leader_cv(leader_info=leader_info, index_name=self.fallen_leader_cvindex, flag="fallen")
                                self.execute_file.write("position={}\turl={}\tleader_cv={}\n".format(title, resp_url, leader_cv))
                                is_update=True
                        else:
                            if is_update:
                                break
                            else:
                                query_url = polyvalue["href"] if polyvalue["href"].startswith("http") else r"https://baike.baidu.com{}".format(polyvalue["href"])
                                page_resource, resp_url = self.fillinformation.my_requests2(url = query_url)
                                desc, polysemantlist, summary, leader_cv = self.fillinformation.get_page_info(page_resource=page_resource,  page_url=resp_url)
                                if shencha_pat.findall(summary) and summary:
                                    title_pat = re.compile("曾任(.*?)。")
                                    title = desc if desc else "".join(title_pat.findall(summary))
                                    leader_info = {"name":_name, "position":title, "leader_cv": leader_cv, "url": resp_url}
                                    self.fillinformation.update_leader_cv(leader_info=leader_info, index_name=self.fallen_leader_cvindex, flag="fallen")
                                    self.execute_file.write("position={}\turl={}\tleader_cv={}\n".format(title, resp_url, leader_cv))
                                    is_update=True

        return                
    
    def get_newest_url(self, start_time="2023-11-01 00:00:00", end_time = "2023-11-10 00:00:00", save_dir=""):
        time.sleep(10)
        newest_leader_cv = get_date_from_ES(self.fillinformation.es, self.fallen_leader_cvindex)
        if not save_dir:
            currenttime = datetime.datetime.today().strftime("%Y%m%d")
            save_dir = os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")),"runtime/{}".format(currenttime)) 
        save_file = os.path.join(save_dir, "fallen_leader_url.json")
        fallen_list = []
        with open(save_file, "w", encoding="utf-8") as fp:
            for item in newest_leader_cv:
                update_time = item.get("_source").get("update_time")
                # if update_time>=start_time and update_time<=end_time:
                _name = item.get("_source").get("name")
                _position = item.get("_source").get("position")
                _url = item.get("_source").get("url")
                _url = re.sub("/?from.*?", "", _url)
                info = {"name": _name, "position": _position, "url":_url}
                if info not in fallen_list:
                    fallen_list.append(info)
            json_data = json.dumps(fallen_list, ensure_ascii=False, indent=2)
            fp.write(json_data)
                
def test_update_by_id():
    es = Elasticsearch(["http://************:9203"], basic_auth=('elastic', 'gz123@people!@#'), request_timeout=120, max_retries=10, retry_on_timeout=True)
    _id = "5zDmB4cBwz4XxXv-EmSj"
    update_script = {
        "script": {
            "source": "ctx._source.cPosition = params.c1; ctx._source.ePosition=params.c1; ctx._source.update_time=params.c2; ctx._source.url=params.c3;",
            "params":{
                "c1": "河南省政协副主席，省残联第八届主席团名誉主席",
                "c2": get_time(),
                "c3": "/item/%E6%88%B4%E6%9F%8F%E5%8D%8E/11070624"
            },
            "lang": "painless"
            }
        }
    es.update(index="leader-info-final", id=_id, body=update_script, timeout="10m")

# 执行信息补充任务，每周运行一次
def execute_fill_info():
    current_day = datetime.datetime.today().strftime("%Y-%m-%d")
  
    workdir = os.path.join(os.path.dirname(__file__), "tmp")
    # # 补充时政人物现任职务、简历
    runningfile = os.path.join(os.path.join(os.path.abspath(os.path.dirname(__file__)), "fill_info_log"), "leader-info-{}.txt".format(datetime.datetime.now().date()))
    fillinformation=FillLeaderInformation(runningfile=runningfile)
    # fillinformation.fill_position_url_info(start_time="{} 00:00:00".format("2024-04-20"), end_time = "{} 23:59:59".format(current_day))
    fillinformation.fill_position_url_info(start_time="{} 00:00:00".format("2000-01-01"), end_time = "{} 23:59:59".format(current_day))
    
    '''
    # 补充落马官员简历
    fallenrunningfile = os.path.join(os.path.join(os.path.abspath(os.path.dirname(__file__)),"fill_info_log"), "fallen-leader-cv-{}.txt".format(datetime.datetime.now().date()))
    falleninfofile = os.path.join(os.path.abspath(os.path.dirname(__file__)), "tmp/fallen-leader-info-{}.txt".format(datetime.datetime.now().date()))
    fill_fallen_leader_cv = FillFallenLeaderInformation(fallenrunningfile=fallenrunningfile, fillinfofile=falleninfofile)
    fill_fallen_leader_cv.execute_fill(start_time=r"{} 00:00:00".format(current_day), end_time = f"{current_day} 23:59:59" )
    # 获取当天update后的fallen-leader-cv的人物姓名、职务、url
    fill_fallen_leader_cv.get_newest_url(start_time=r"{} 00:00:00".format(current_day), end_time = f"{current_day} 23:59:59", save_dir="")
    '''

def schedule_job():
    schedule.every().day.at("15:03").do(execute_fill_info)
    while True:
        schedule.run_pending()
        time.sleep(90)

"""
def test_get_pageinfo():
    current_day = datetime.datetime.today().strftime("%Y-%m-%d")
  
    workdir = os.path.join(os.path.dirname(__file__), "tmp")
    runningfile = os.path.join(os.path.join(os.path.abspath(os.path.dirname(__file__)), "fill_info_log"), "leader-info-{}.txt".format(datetime.datetime.now().date()))
    fillinformation = FillLeaderInformation(runningfile=runningfile)

    html_file = os.path.join(workdir, 'xijinping.html')
    with open(html_file, "r", encoding="utf-8") as fp:
        page_resource = fp.read()
        root = etree.HTML(page_resource)
        desc_text, polysementlist, summary_text, leader_cv = fillinformation.get_page_info(page_resource=page_resource, page_tree=root, page_url="item")
        print(f"desc_text = {desc_text}\npolysementlist = {polysementlist}\nsummary_text = {summary_text}\nleader_cv = {leader_cv}")
"""

if __name__=="__main__":
    execute_fill_info()
    
    # schedule_job()
    # test_get_pageinfo()

    
    # fallenrunningfile = os.path.join(os.path.join(os.path.abspath(os.path.dirname(__file__)),"fill_info_log"), "fallen-leader-cv-{}.txt".format(datetime.datetime.now().date()))
    # falleninfofile = os.path.join(os.path.abspath(os.path.dirname(__file__)), "fill_info_log/fallen-leader-info-{}.txt".format(datetime.datetime.now().date()))
    # fill_fallen_leader_cv = FillFallenLeaderInformation(fallenrunningfile=fallenrunningfile, fillinfofile=falleninfofile)
    # fill_fallen_leader_cv.get_newest_url(start_time="2023-12-01 00:00:00", end_time="2023-12-10 00:00:00", save_dir="")