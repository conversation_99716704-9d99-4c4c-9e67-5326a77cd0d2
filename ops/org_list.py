import sys, os, json
from urllib import request
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from spiders import MPSpider
import parsers
import traceback, json


class OrgList(object):
    def __init__(self, spider=None):
        self.base_dir = os.path.join(os.path.dirname(__file__))
        self.spider = spider if spider is not None else MPSpider(os.path.join(os.path.abspath(os.path.join(self.base_dir, "..")),"configs/config.yaml"))#(os.path.join(os.path.abspath(self.base_dir, '../configs/config.yaml')))

        with open(os.path.join(os.path.abspath(os.path.join(self.base_dir, "..")),"configs/configall.json"), "r", encoding='utf-8') as fp:
            json_data = json.load(fp=fp)
            self.http_organizations = json_data.get("http_organizations").get("value")
            self.liuyan_url = json_data.get("liuyan_url").get("value")
        # print(json_data)

    def get_org_list(self):
        # print(os.path.join(os.path.abspath(os.path.join(self.base_dir, "..")),"configs/org_list.json"))
        with open(os.path.join(os.path.abspath(os.path.join(self.base_dir, "..")),"configs/org_list.json"), "r", encoding="utf-8") as fp:
            json_data = json.load(fp=fp)
            if not json_data:
                raise ConnectionError("爬取机构列表失败")
        res_orgs = [item for item in json_data if item["parent"] in ["地方","中直机构","中管高校", "军事机构", "国务院机构","国家级协会","央企","政策性银行", "领导机构"]]
        return res_orgs
        # import requests
        # url = self.http_organizations
        # print("==================",url, "======================")
        # resp = self.spider.run([url], callbacks=[parsers.org_list])[0]
        # # resp = requests.request("get", url=url)
        # if resp["code"]!=0: #  resp.status_code != 200:  # 
        #     return_org_list =[]
        #     raise ConnectionError("爬取机构列表失败")
        # return resp['resp'] #eturn_org_lisrt

    def get_df_org_list(self):
        import requests
        url = self.liuyan_url 
        if os.path.exists("/home/<USER>/leader-info/UpdateLeaderInfo/configs/df_org_list.json"):
            with open("/home/<USER>/leader-info/UpdateLeaderInfo/configs/df_org_list.json", "r", encoding="utf-8") as fp:
                data=json.load(fp)
            return data
        else:
            headers = {"user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36 Edg/128.0.0.0"}
            res = requests.request("GET", url=url, headers=headers)
            if res.status_code!=200:
                raise ConnectionError("爬取地方机构列表失败")
            else:
                return json.loads(res.text)


if __name__=="__main__":
    orglist = OrgList()
    lst = orglist.get_org_list()
    print(lst)
    print(len(lst),  type(lst))