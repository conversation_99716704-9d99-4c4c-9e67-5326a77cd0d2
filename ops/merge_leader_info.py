# from nturl2path import url2pathname
import os
import json
# from pipes import quote
import re
from datetime import datetime

from urllib.parse import quote_plus
import logging


def are_all_chacacters_present(source:str, target:str) -> bool:
    '''判断source是否是target的子集'''
    source_set = set(source)
    target_set = set(target)
    return source_set.issubset(target_set)

class MergeLeaderInfo(object):
    def __init__(self, logger=None) -> None:
        # self.logger = logger if logger is not None else get_logger()
        current_dir = os.path.dirname(__file__)
        current_date = datetime.now().date().strftime("%Y%m%d")
        log_dir = os.path.join(os.path.abspath(os.path.join(current_dir, "..")), 'runtime/{}/logs'.format(current_date))
        self.logger = self.update_log_file(log_dir=log_dir, log_file='merge_result.log')
  
    def update_log_file(self, log_dir, log_file="parser_result.log"):
        # 设置file处理器
        if not os.path.exists(log_dir):
            os.mkdir(log_dir)
        log_file_path = os.path.join(log_dir, f"{log_file}")
        # print(f"log file path: {log_file_path}")

        #创建一个日志记录器
        parser_result_logger = logging.getLogger("MergeLeaderInfo")
        parser_result_logger.setLevel(logging.DEBUG)
        try:
            filehandler = logging.FileHandler(filename=log_file_path, mode="a", encoding="utf-8")
            filehandler.setLevel(logging.DEBUG)
        except Exception as e:
            print(f"Error setting up file handler: {e}")
            return 
        formater = logging.Formatter("%(levelname)s | %(asctime)s | %(filename)s:%(lineno)d | %(message)s")
        filehandler.setFormatter(formater)
        parser_result_logger.addHandler(filehandler)

        # 设置console处理器
        streamhandler = logging.StreamHandler()
        streamhandler.setLevel(logging.DEBUG)
        streamhandler.setFormatter(formater)
        parser_result_logger.addHandler(streamhandler)
        return parser_result_logger


    def merge(self, shengbu_leader_info, difang_leader_info, leaders_info_to_be_updated):
        assert shengbu_leader_info
        assert leaders_info_to_be_updated

        new_whole_leader_info=[]  
        if  difang_leader_info:
            for leader in difang_leader_info:
                name = leader.get("name")
                df_leader_xianren= leader.get("present post")
                if name:
                    leader_list_tobe_updated = [item for item in leaders_info_to_be_updated if item.get("name", "")==name]
                    if leader_list_tobe_updated:
                        fuzhi = False
                        for newitem in leader_list_tobe_updated:
                            xianren = newitem.get("XianRen", "")
                            if are_all_chacacters_present(df_leader_xianren, xianren):#df_leader_xianren in xianren: # or df_leader_xianren in description: 利用百科页面的现任信息，补全地方领导人的信息
                                df_leader_url = newitem.get("url")
                                df_leader_url = re.sub("https://baike.baidu.com|http://baike.baidu.com", "", df_leader_url)
                                df_leader_xianren = xianren
                                one_df_leader = leader
                                one_df_leader["leader_type"]=0
                                one_df_leader["shencha"] = ""
                                one_df_leader["url"] = df_leader_url
                                one_df_leader["present post"] = xianren
                                new_whole_leader_info.append(one_df_leader)
                                self.logger.info("判断是否同一人")
                                self.logger.info("df_leader:{}".format(leader))
                                self.logger.info("tobe update leader: {}".format(newitem))
                                fuzhi=True
                                break 
                        if fuzhi==False:
                            new_whole_leader_info.append(leader)
                    else:
                       one_df_leader = leader
                       one_df_leader["leader_type"]=0
                       one_df_leader["shencha"] = ""
                       new_whole_leader_info.append(one_df_leader)     
                else:
                    self.logger.waring("difang_leader name is empty!")
                    continue
            # new_whole_leader_info.extend(difang_leader_info)
        else:
            print("地方领导人信息为空")
            self.logger.warning("市级领导人信息为空！")
        
        if shengbu_leader_info:
            # new_whole_leader_info.extend(difang_leader_info)   # 将获取的地方领导人信息并入整个领导人信息表
            for new_leader in shengbu_leader_info:     #省部级领导人
                
                leader_url = new_leader["url"]   
                leader_url = re.sub(" |\u3000|%20|%E3%80%80|https://baike.baidu.com|\?.*", "", leader_url).strip()  
                new_leader["position"] = new_leader["position"].strip()
                new_leader["position"] = re.sub("现任", "", new_leader["position"])
                item_idx = [item_iidx for item_iidx, value in enumerate(leaders_info_to_be_updated) if value["url"]==leader_url or quote_plus(value["url"])==leader_url]
                if item_idx:
                    item_idx = int(item_idx[0])
                    title = leaders_info_to_be_updated[item_idx]["XianRen"]
                    pre_title = leaders_info_to_be_updated[item_idx]["pre_title"]
                    shencha = leaders_info_to_be_updated[item_idx]["shencha"]
                else:
                    title=""
                    pre_title=""
                    shencha=""
                if shencha:
                    leader_type=2   # 审查，有落马风险
                    whole_leader_info = new_leader.copy()
                    whole_leader_info["present post"] = title
                    whole_leader_info["leader_type"]=leader_type
                    whole_leader_info["url"] = leader_url
                    whole_leader_info["shencha"] = "涉嫌违纪违法"
                    new_whole_leader_info.append(whole_leader_info)
                else:
                    if pre_title:
                        if  not title:
                            leader_type = 1   # 只有曾任信息，考虑退休
                            whole_leader_info = new_leader.copy()
                            whole_leader_info["present post"] = title
                            whole_leader_info["leader_type"]=leader_type
                            whole_leader_info["url"] = leader_url
                            whole_leader_info["shencha"] = "不确定，只有曾任没有现任信息"
                            new_whole_leader_info.append(whole_leader_info)
                        else:
                            leader_type = 0
                            whole_leader_info = new_leader.copy()
                            whole_leader_info["present post"] = title
                            whole_leader_info["leader_type"]=leader_type
                            whole_leader_info["url"] = leader_url
                            whole_leader_info["shencha"]= ""
                            new_whole_leader_info.append(whole_leader_info)
                    else:
                        leader_type = 0
                        whole_leader_info = new_leader.copy()
                        whole_leader_info["present post"] = title
                        whole_leader_info["leader_type"]=leader_type
                        whole_leader_info["url"] = leader_url
                        whole_leader_info["shencha"]=""
                        if leader_url in ['/item/%E6%9D%8E%E5%B8%8C/7584']:
                            whole_leader_info["present post"] = title+"，中央党的建设工作领导小组副组长"#"，".join(title, '中央党的建设工作领导小组副组长')
                        if leader_url in ['/item/%E8%94%A1%E5%A5%87/10748789']:
                            whole_leader_info["present post"] = title+"，中央党的建设工作领导小组副组长"#"，".join(title, '中央党的建设工作领导小组组长')
                        new_whole_leader_info.append(whole_leader_info)       
        else:
            self.logger.error("省部级领导人信息为空！")
            print("省部级领导人信息为空！")
        return new_whole_leader_info
    
    def dump(self, merged_leader_info, save_name):
        pos_pattern = re.compile("主席|省长|书记|主任|调研员|委员|会长|社长|行长|首席|监事|党组|长官|局长|部长|司长|司令|政委|参谋长|专员|市长|市　长|区长|州长|盟长|县长|干部|巡视|督学|总理|常委|编辑|编委|秘书|会计|组长|牵头人|外长|成员|领导|领袖|经理|董事|总监|校长|副校|院长|检察长|经济师|工程师|畜牧师|农艺师|兽医师|署长|副署长|总设计师|总会计师|总审计师|上将|教育长|总审计师|空缺|届次|任期|职务|姓名|秘书长")
        person_name_pattern = re.compile(r'[\u4E00-\u9FA5]{2,6}(·[\u4E00-\u9FA5]{2,6}){0,2}')
        not_name_pattern = re.compile("总飞行师|名单如下|选举产生|聘期三年|暂无|其他|空缺|书记处书记|主席团委员|截止|截至|成立于|控股子企业")
        # print(whole_file)
        if not merged_leader_info:
            self.logger.error("领导人信息合并失败")
        with open(save_name, "w", encoding="utf8") as fp:
            for leader_str in merged_leader_info:
                if "shencha" in leader_str.keys():
                    shencha = leader_str["shencha"]
                else:
                    shencha = ""
                if shencha:
                    print("write2json\t", leader_str["name"], shencha, leader_str.get("url", ""))
                    self.logger.info("write2json\t {} {} url={}".format(leader_str["name"], shencha, leader_str.get("url", "")))
                    # continue
                name = leader_str["name"]         
                name = re.sub(pos_pattern, "", name)
                name = name.strip()
                if not_name_pattern.findall(name):
                    continue
                if person_name_pattern.findall(name):
                    name = "".join(person_name_pattern.findall(name))
                    if pos_pattern.findall(name):
                        name = re.sub(pos_pattern, "", name)                    
                        if name:
                            leader_str["name"]=name
                            sstr = json.dumps(leader_str, ensure_ascii=False)+"\n"
                        else:
                            continue
                    else:
                        if not_name_pattern.findall(name):
                            continue
                        sstr = json.dumps(leader_str, ensure_ascii=False)+"\n"
                    fp.write(sstr)
                else:
                    continue
        self.logger.info(f'saved {save_name}')
