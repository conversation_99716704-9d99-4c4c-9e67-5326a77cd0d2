# 查询过程中发现有些领导人的url出现item重复的问题，还有将/读成%2F，这里解决这个问题
# _index_name_默认是leade-info-finale

from elasticsearch import Elasticsearch
import datetime, hashlib
from urllib.parse import unquote, quote
import re

__index_name__ = 'leader-cv-new'  # index新表，增加了leader_type字段
CONFIG_ES = {
    "host": [
        { "host": "************", "port": 9203, "scheme": "http" },
        { "host": "************", "port": 9203, "scheme": "http" },
        { "host": "*************", "port": 9203, "scheme": "http" },
        { "host": "*************", "port": 9203, "scheme": "http" }
    ],
    "user":"elastic",
    "passwd":"gz123@people!@#"
}
myes = Elasticsearch(CONFIG_ES['host'], basic_auth=(CONFIG_ES['user'], CONFIG_ES['passwd']), request_timeout=120, max_retries=10, retry_on_timeout=True)

def extract_true_url(url, flag='leader-info'):
    true_url = ""
    if url:
        url = "".join(re.sub(re.compile("\?from.*"),"",url))
        url = quote(unquote(url, encoding='utf-8'),encoding="utf-8", safe=":/")
        if flag=='leader-info':
            url = re.sub("https://baike.baidu.com|http://baike.baidu.com", "", url)
        
        parts = url.split("/item/")
        # print(parts)
        if len(parts)<=1:  # 有可能url="http://baike.baidu.com"
            return true_url
        else:
        # print("parts:", parts[-1])
            true_url = "{}/item/{}".format(parts[0], parts[-1])
            return true_url
    else:
        return true_url

def elastic_search(index=__index_name__, query_body={"match_all":{}}):
    search_hits = myes.search(index=index, query=query_body, timeout="10m", size=100000)
    search_hits = search_hits["hits"]["hits"]
    print(len(search_hits))
    # print(search_hits)
    return search_hits

def get_time():
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    # print(current_time)
    return current_time

def update_by_id(doc, indexname=__index_name__, updateid=""):
    myes.update(index=indexname, id=updateid, doc=doc, timeout="10m")

# 更新leader-info-final时需要构建的doc格式
def get_doc_leaderinfo(item, true_url):
    doc = {}
    for key, value in item.items():
        doc[key] = value
    doc["url"]=true_url
    doc["update_time"] = get_time()
    if true_url:
        pId = hashlib.md5(true_url.encode('utf-8')).hexdigest()
        doc["pId"] = pId
    else:
        doc["pId"] = ""
    return doc

# 更新leader-cv-new时需要构建的doc格式
def get_doc_leadercv(item, true_url):
    doc = {}
    doc = item.copy()
    doc['url'] = true_url
    doc["update_time"] = get_time()
    return doc

if __name__=="__main__":
    search_res =  elastic_search(index=__index_name__)
    count=0
    for item in search_res:
        _id = item.get("_id")
        url = item.get("_source").get("url")
        true_url = extract_true_url(url, flag="leader-cv")  # flag决定url是否需要http打头，leader-info是需要，其他是不需要
        if url==true_url:
            pass
        else:
            count+=1
            print(_id, "\t", true_url, "\t", item.get("_source").get("name"), ":", true_url)
            # print("\t\t\t", item.get("_source").get("name"))
            item_source = item.get("_source")
            doc = get_doc_leadercv(item=item_source, true_url=true_url)
            doc["url"] = "https://baike.baidu.com/item/{}".format(quote(doc["name"], encoding='utf-8'))
            print("\t\t\t",doc["url"], "\n")
            # doc = get_doc_leaderinfo(item=item_source, true_url=true_url)  # 这里获取的doc是leader-info-final的mapping
            # if doc.get("url"):
            #     update_by_id(doc, indexname=__index_name__, updateid=_id)
            # break
    print("count=", count)