import os
import json
import re
import requests
import traceback
import time
import pandas as pd
import sqlalchemy
from sqlalchemy import text as sqltext
from lxml import etree
# from leader_cv import octopus_utilize


def get_org_info_from_orgname(org_name="中国共产党上海市委员会"):
    '''根据org_name，如中国共产党上海市委员会，返回org_info={province:上海市, city:"", org:中共}'''
    org_info = {}
    zhonggong_pat = re.compile(r"中国共产党(.*)委员会")
    zhengfu_pat = re.compile(r"(.*?)人民政府")
    renda_pat = re.compile(r"(.*?)人民代表大会")
    zhengxie_pat = re.compile(r"中国人民政治协商会议(.*)委员会")
    if zhonggong_pat.findall(org_name):
        org_info["province"]="".join(zhonggong_pat.findall(org_name))
        org_info["org_name"]=org_name
        org_info["org"] = "中共"
        return org_info
    if zhengfu_pat.findall(org_name):
        org_info["province"]="".join(zhengfu_pat.findall(org_name))
        org_info["org_name"]=org_name
        org_info["org"] = "政府"
        return org_info
    if renda_pat.findall(org_name):
        org_info["province"]="".join(renda_pat.findall(org_name))
        org_info["org_name"]=org_name
        org_info["org"] = "人大"
        return org_info
    if zhengxie_pat.findall(org_name):
        org_info["province"]="".join(zhengxie_pat.findall(org_name))
        org_info["org_name"]=org_name
        org_info["org"] = "政协"
        return org_info


def compare_new_old_leader_info(new_leader_info, old_leader_info, org_list, logger, work_dir):
    to_be_update_url_list = os.path.join(work_dir, "tobe_updated_url_list.txt")
    if os.path.exists(to_be_update_url_list):
        update_leader_url_ls = []
        with open(to_be_update_url_list, "r", encoding="utf-8") as fffp:
            for line in fffp:
                line = re.sub(" |\u3000|%20|%E3%80%80", "", line).strip()
                if line in ["https://baike.baidu.com/item/"]:
                    continue
                if line not in update_leader_url_ls:
                    update_leader_url_ls.append(line)
            return update_leader_url_ls
    else:
        ## 获取es表中所有的url列表，并储存到txt文件
        all_url_list = []
        for item in new_leader_info:
            url = item["url"]
            if url:
                url = url if url.startswith("http") else "https://baike.baidu.com{}".format(url)
                if url not in all_url_list:
                    url  = re.sub(" |\u3000|%20|%E3%80%80|\?fromModule.*?", "", url).strip()
                    if url in ["https://baike.baidu.com/item/"]:
                        continue
                    if url not in all_url_list:
                        all_url_list.append(url)
            with open(to_be_update_url_list, "w", encoding="utf-8") as fffp:
                for item in all_url_list:
                    fffp.write(item+"\n")
        return all_url_list
    
    #     for item in old_leader_info:
    #         url = item["url"]
    #         if url:
    #             url = url if url.startswith("http") else "https://baike.baidu.com{}".format(url)
    #             if url not in all_url_list:
    #                 all_url_list.append("{} {}".format("2", url))

    #     update_leader_url_ls = []
    #     to_be_updated_leades = []
    #     if len(old_leader_info)==0 or len(new_leader_info)==0 or len(org_list)==0:
    #         logger.error("新旧两个领导人信息比较：有信息表为空！oldleaderinfo={}\tnewleaderinfo={}\torglist={}".format(len(old_leader_info), len(new_leader_info), len(org_list)))
    #         print("新旧两个领导人信息比较警告：有信息表为空！")
    #         logger.info("len(old_leader_info)={}, len(new_leader_info)={}, len(org_list)={}".format(len(old_leader_info), len(new_leader_info), len(org_list)))
    #         return []
        
    #     new_leader_info = pd.DataFrame(new_leader_info)
    #     old_leader_info = pd.DataFrame(old_leader_info)
    #     for org in org_list:
    #         org_type = org["parent"]
    #         org_name = org["name"]
    #         if org_type not in ["地方"]:
    #             province = ""
    #             city = ""                 
    #         else:
    #             org_info = get_org_info_from_orgname(org_name=org_name)  
    #             province = org_info["province"]
    #             city = ""
    #             org_name = org_info["org_name"]
    #         new_leader_items = new_leader_info[(new_leader_info["org"]==org_name) & (new_leader_info["province"]==province)].to_dict(orient="records")   
    #         old_leader_items = old_leader_info[(old_leader_info["org"]==org_name) & (old_leader_info["province"]==province)].to_dict(orient="records") 
            
    #         for item in new_leader_items:
    #             new_leader_name = item["name"]
    #             new_leader_position = item["position"]
    #             # xiugai为空，表示旧数据中没有当前记录
    #             xiugai = [old_item for old_item in old_leader_items if old_item["name"]==new_leader_name and old_item["position"]==new_leader_position]

    #             if not xiugai and item["url"]:
    #                 url = re.sub(r"\?.*", "", item["url"]) 
    #                 url = "https://baike.baidu.com"+url
    #                 if url not in update_leader_url_ls:
    #                     update_leader_url_ls.append(url)
    #         '''
    #         # 为了减少url采集频率，下面这段不要了  20240920          
    #         for item in old_leader_items:
    #             old_leader_name = item["name"]
    #             old_leader_position = item["position"]
    #             # xiugai为空，表示新数据中没有旧数据中保存的当前记录
    #             xiugai = [new_item for new_item in new_leader_items if new_item["name"]==old_leader_name and new_item["position"]==old_leader_position]
    #             if not xiugai and item["url"]:
    #                 url = re.sub(r"\?.*", "", item["url"])
    #                 url = "https://baike.baidu.com"+url
    #                 if url not in update_leader_url_ls:
    #                     update_leader_url_ls.append(url)
    #         '''
    #     # print("待查url列表长度:", len(update_leader_url_ls))
    #     logger.info("待查url列表长度: {}".format(len(update_leader_url_ls)))
    #     to_be_update_url_list = os.path.join(work_dir, "tobe_updated_url_list.txt")
    #     with open(to_be_update_url_list, "w", encoding="utf8") as fp:
    #         for item in update_leader_url_ls:
    #             fp.write(item+"\n")
    # return update_leader_url_ls


def write_to_sql(to_sql_data, table_name, create_string):
    if not to_sql_data or not table_name:
        print("leader_info_tobe_updateds to sql failed")
        return
    cur_dir = os.path.dirname(__file__)
    with open(os.path.join(os.path.abspath(os.path.join(cur_dir, "..")),"configs/configall.json"), "r", encoding="utf-8") as fp:
        sql_config = json.load(fp=fp)
        sql_config = sql_config["mysql"]
        # print(sql_config["host"])

    # # 新建数据表 in database
    myengine = sqlalchemy.create_engine(r"mysql+pymysql://{}:{}@{}:{}/peopledata?charset=utf8".format(sql_config["user"],sql_config["passwd"],sql_config["host"]["host"],sql_config["host"]["port"]))
    cursor = myengine.connect()
    del_string = "drop table if exists {}".format(table_name)
    try:
        cursor.execute(sqltext(del_string))
    except:
        print(traceback.format_exc())
    time.sleep(2)
    # create_string = "create table if not exists {} (numid int primary key auto_increment, Title varchar(255) character set utf8mb4, Url mediumtext character set utf8mb4, PageContent mediumtext character set utf8mb4, SearchText varchar(255) character set utf8mb4) character set utf8mb4".format(table_name)
    try: 
        cursor.execute(sqltext(create_string))
    except Exception:
        print("create table 【{}】 failed in update_leader_info.py [line 141]".format(table_name))  
        print(traceback.format_exc())
    time.sleep(2)

    datas = []
    if table_name == "org_page_content":
        for i_item in to_sql_data:
            pgtitle = i_item["Pgtitle"]
            searchtext = i_item["SearchText"]
            pagecontent = i_item["PageContent"]
            sss={"Pgtitle": pgtitle, "SearchText": searchtext, "PageContent":pagecontent}
            datas.append(sss)
    else:
        for i_item in to_sql_data:
            Title = i_item["Title"]
            Url = i_item["Url"]
            PageContent = i_item["PageContent"]
            SearchText = i_item["SearchText"]
            sss = {"Title": Title, "Url": Url, "PageContent":PageContent, "SearchText": SearchText}
            if sss not in datas:
                datas.append(sss)
    # datas = pd.DataFrame(datas)
    # try:
    #     datas.to_sql(name=table_name, con=myengine, if_exists="append", index=None, index_label=None)
    # except:
    #     print(traceback.format_exc(chain=False))
    cursor.close()
    # myengine.close()
    return


"""从领导人desc得到领导人的title，pre_title和shencha信息"""
def get_leader_info_tobe_updated(url_list, type_get_leaderdesc, spider, logger, work_dir):
    if not url_list:
        # logging.log(logging.INFO, "Url_list_tobe_updated is empty!")
        logger.error("待更新的领导人url列表为空")
        print("待更新的领导人url列表为空")
        return []

    leader_desc_from_urllist = get_leader_desc_from_url_list(url_list, type_get_leaderdesc, max_iter=1, spider=spider, logger=logger)  # type_get_leaderdesc=1表示从数据库读取领导人信息
    print("", len(leader_desc_from_urllist)) 
    logger.info("待更新领导人信息: {}".format(len(leader_desc_from_urllist)))
    # 表结构"(Title VARCHAR(255), Url VARCHAR(255), PageContent MEDIUMTEXT, SearchText VARCHAR(255))"
    print("写入数据库：")
    leader_desc_table = "tobe_updated_leader_info"
    try:
        create_string = "create table if not exists {} (numid int primary key auto_increment, Title varchar(255) character set utf8mb4, Url mediumtext character set utf8mb4, PageContent mediumtext character set utf8mb4, SearchText varchar(255) character set utf8mb4) character set utf8mb4".format(leader_desc_table)
        write_to_sql(to_sql_data=leader_desc_from_urllist, table_name=leader_desc_table, create_string=create_string)
        print("写入成功")
        logger.info(f"待更新领导人信息成功写入数据库--table_name={leader_desc_table}")
    except Exception:
        print("sql [{}] 数据写入失败".format(leader_desc_table))
        print(Exception, traceback.format_exc(chain=True))
        logger.error("sql [{}] 数据写入失败".format(leader_desc_table))
    
    leaders_to_be_updated = []
    with open(os.path.join(work_dir, "leaders_to_be_updated.json"), "w", encoding="utf8") as fp:
        for leader in leader_desc_from_urllist:
            name = leader["Title"]
            name = re.sub("_百度百科","", name)
            name = re.sub(r"\（.*?\）", "", name)
            url = leader["Url"]
            url = re.sub("https://baike.baidu.com", "", url)
            url = re.sub("\?.*", "", url)
            desc = leader["Desc"]
            desc = re.sub(r"\[.*?\]", "", desc)
            desc = re.sub(r"\xa0", "", desc)
            desc = re.sub(r"\n", "", desc)
            title_pat = re.compile("现任(.*?)。")
            shencha_pat = re.compile("涉嫌.*?违纪违法")
            shencha = "".join(shencha_pat.findall(desc))

            title = "".join(title_pat.findall(desc))
            title = re.sub(r"\[.*?\]", "", title)
            # split_desc = desc.split("。")
            pre_title_pat = re.compile("曾任(.*)。")
            pre_title = "".join(pre_title_pat.findall(desc))
            if leader["desc2"]:
                prepos_pattern = re.compile("原主席|原省长|原书记|原主任|原调研员|原委员|原会长|原社长|原行长|原首席|原监事|原党组|原长官|原局长|原部长|原司长|原司令|原政委|原参谋长|原专员|原市长|原市　长|原区长|原州长|原盟长|原县长|原干部|原巡视|原督学|原总理|原常委|原编辑|原编委|原秘书|原会计|原组长|原牵头人|原外长|原成员|原领导|原领袖|原经理|原董事|原总监|原校长|原副校长|原院长|原检察长|原经济师|原工程师|原畜牧师|原农艺师|原兽医师|原署长|原副署长|原总设计师|原总会计师|原总审计师|原上将|原教育长|原总审计师|空缺|届次|任期|职务|姓名|原秘书长")
                pre_title_temp = "".join(prepos_pattern.findall(leader["desc2"]))
                if pre_title_temp:
                    pre_title = leader["desc2"]
                else:
                    pre_title=""     
                    if title:
                        pass
                    else:
                        title = leader["desc2"]
        
            info = {"name":name, "url":url, "title":title, "pre_title": pre_title, "shencha":shencha}
            leaders_to_be_updated.append(info)  
            info_str = json.dumps(info, ensure_ascii=False)+"\n"
            fp.write(info_str)
    
    return leaders_to_be_updated


"""合并省部级、市级领导人信息"""
def merge_leader_info(shengbu_leader_info, difang_leader_info, leaders_info_to_be_updated, logger):
    new_whole_leader_info=[]  
    if  difang_leader_info:
        new_whole_leader_info.extend(difang_leader_info)
    else:
        print("地方领导人信息为空")
        logger.warning("市级领导人信息为空！")
    if shengbu_leader_info:
        # new_whole_leader_info.extend(difang_leader_info)   # 将获取的地方领导人信息并入整个领导人信息表
        for new_leader in shengbu_leader_info:     #省部级领导人
            leader_url = new_leader["url"]
            leader_url = re.sub("https://baike.baidu.com", "", leader_url)   
            leader_url = re.sub("\?.*", "", leader_url)      
            new_leader["position"] = new_leader["position"].strip()
            new_leader["position"] = re.sub("现任", "", new_leader["position"])
            item_idx = [item_iidx for item_iidx, value in enumerate(leaders_info_to_be_updated) if value["url"]==leader_url]
            if item_idx:
                item_idx = int(item_idx[0])
                title = leaders_info_to_be_updated[item_idx]["title"]
                pre_title = leaders_info_to_be_updated[item_idx]["pre_title"]
                shencha = leaders_info_to_be_updated[item_idx]["shencha"]
            else:
                title=""
                pre_title=""
                shencha=""
            if shencha:
                leader_type=2   # 审查，有落马风险
                whole_leader_info = new_leader.copy()
                whole_leader_info["present post"] = title
                whole_leader_info["leader_type"]=leader_type
                whole_leader_info["url"] = leader_url
                whole_leader_info["shencha"] = "涉嫌违纪违法"
                new_whole_leader_info.append(whole_leader_info)
            else:
                if pre_title:
                    if  not title:
                        leader_type = 1   # 只有曾任信息，考虑退休
                        whole_leader_info = new_leader.copy()
                        whole_leader_info["present post"] = title
                        whole_leader_info["leader_type"]=leader_type
                        whole_leader_info["url"] = leader_url
                        whole_leader_info["shencha"] = "退休"
                        new_whole_leader_info.append(whole_leader_info)
                    else:
                        leader_type = 0
                        whole_leader_info = new_leader.copy()
                        whole_leader_info["present post"] = title
                        whole_leader_info["leader_type"]=leader_type
                        whole_leader_info["url"] = leader_url
                        whole_leader_info["shencha"]= ""
                        new_whole_leader_info.append(whole_leader_info)
                else:
                    leader_type = 0
                    whole_leader_info = new_leader.copy()
                    whole_leader_info["present post"] = title
                    whole_leader_info["leader_type"]=leader_type
                    whole_leader_info["url"] = leader_url
                    whole_leader_info["shencha"]=""
                    new_whole_leader_info.append(whole_leader_info)       
        return new_whole_leader_info
    else:
        logger.error("省部级领导人信息为空！")
        print("省部级领导人信息为空！")
        return []


if __name__=="__main__":
    write_to_sql(to_sql_data=[{"name": "jdfi"}], table_name="tobe_updated_leader_info", create_string="")
