from math import log
import os
import re
import json
import time, sys, random
excute_path = "/home/<USER>/leader-info/UpdateLeaderInfo/"
os.chdir(excute_path)
# print("新的当前工作目录:", os.getcwd())
# print("sys.path:", sys.path)
if os.getcwd() not in sys.path:
    sys.path.append(os.getcwd())
from ops.org_list import OrgList
from utils import url_join, gen_signature
from spiders import MPSpider
import requests, datetime
from ip_proxy import IPProxyPool
import logging



snapshot_path = os.path.join(excute_path, "runtime/{}/snapshot/liuyan.people.cn".format(datetime.datetime.today().strftime("%Y%m%d")))
os.system(f'mkdir -p {snapshot_path}')



class DfLeaderInfo(object):
    def __init__(self, spider=None):
        # self.spider = spider if spider is not None else MPSpider('/home/<USER>/work/spider/configs/config.yaml')
        # self.spider = spider if spider is not None else MPSpider("/home/<USER>/leader-info/UpdateLeaderInfo/configs/config.yaml")
        self.ipproxypool = IPProxyPool(runing_path=os.path.join(excute_path, "runtime/{}".format(datetime.datetime.today().strftime("%Y%m%d"))), snapshot_path="snapshot", ip_num=10)
        date = time.strftime('%Y%m%d',time.localtime(time.time()))
        logfile_dir = os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')), f'runtime/{date}/logs')#os.path.abspath((os.path.dirname(__file__), f'runtime/{date}/logs'))
        print(logfile_dir)
        self.df_logger = self.update_log_file(log_path=logfile_dir, log_file='df_leader.log')
    
    def update_log_file(self, log_path, log_file):
        # 常见和更新 ippoolmonitor 日志
        # 创建一个日志记录器
        newlogger = logging.getLogger("DfLeaderInfo")
        newlogger.setLevel(logging.DEBUG)

        filehandler = logging.FileHandler(filename=os.path.join(log_path, log_file), mode="a", encoding="utf-8")
        filehandler.setLevel(logging.DEBUG)
        formater = logging.Formatter("%(levelname)s | %(asctime)s | %(filename)s:%(lineno)d | %(message)s")
        filehandler.setFormatter(formater)
        newlogger.addHandler(filehandler)

        # 设置console处理器
        streamhandler = logging.StreamHandler()
        streamhandler.setLevel(logging.DEBUG)
        streamhandler.setFormatter(formater)
        newlogger.addHandler(streamhandler)
        return newlogger

    def get_df_leader_data(self, df_org_list):
        output = []
        province_dict = self.get_province_dict() 
        timestamp = int(time.time() * 1000)
        param = {'fid': -2, '_t': timestamp}
        params = {
            'appCode': "PC42ce3bfa4980a9",
            'token': "",
            'param': json.dumps(param, separators=(',', ':'), ensure_ascii=False),
            'signature': gen_signature("/v1/forum/getChildForums", param, 'PC42ce3bfa4980a9')
        }
        base_url = "http://liuyan.people.com.cn"

        res_data = list()
        ddd = df_org_list['data']['list']
        ddd=ddd[:-4]
        self.df_logger.info("df_province: {}".format(len(ddd)))
        for idx in range(0,len(ddd)):#range(0, len(ddd)):
            self.df_logger.info("="*70)
            fid = ddd[idx]['fid']
            province = ddd[idx]['name']
            childfids = ddd[idx]["childIds"] if "childIds" in ddd[idx] else ""

            #获取city名单列表
            param['fid'] = fid
            params["param"] = json.dumps(param, separators=(',', ':'))
            params['signature'] = gen_signature("/v1/forum/getChildForums", param=param)
            headers = {"user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36 Edg/129.0.0.0"}
            url = url_join(base_url + "/v1/forum/getChildForums", query_dict=params)
            if os.path.exists(os.path.join(snapshot_path, "{}".format(fid))):
                self.df_logger.info("load:{}".format(os.path.join(snapshot_path, "{}".format(fid))))
                with open(os.path.join(snapshot_path, "{}".format(fid)), "r", encoding="utf-8") as fp:
                    city_info = json.load(fp=fp)
            else:
                self.df_logger.info("requests:{}".format(os.path.join(snapshot_path, "{}".format(fid))))
                time.sleep(random.randint(3,5))
                bool_get_ip = True
                while bool_get_ip:
                    ip = self.ipproxypool.get_available_proxy()
                    if ip:
                        bool_get_ip=False
                    else:
                        time.sleep(5)
                proxyMeta = "http://{}".format(ip)
                proxydata={"https": proxyMeta, "http":proxyMeta}
                try:
                    response = requests.request("GET", url=url, headers=headers, proxies=proxydata, timeout=(5,9))
                    self.ipproxypool.release_proxy(ip)
                except Exception as errmsg:
                    self.df_logger.error(errmsg)
                    self.ipproxypool.release_proxy(ip)
                    continue             
                try:
                    city_info = json.loads(response.text)
                except json.JSONDecodeError:
                    self.df_logger.error(url)
                    self.df_logger.error("Failed to decode JSON, data is empty or invalid.")
                self.df_logger.info("request succeed : ip={}".format(ip))
                with open(os.path.join(snapshot_path, "{}".format(fid)), "w", encoding="utf-8") as fp:
                    fp.write(response.text)
            if city_info:
                city_fid_list = [{"province":province, "city":item["name"], "fid":item["fid"]} for item in city_info.get("data")[2:]]
                with open("/home/<USER>/leader-info/UpdateLeaderInfo/configs/city_fid_list.json","a", encoding="utf-8") as fp:
                    json.dump(city_fid_list, fp, ensure_ascii=False, indent=4)
                    fp.write("\n")
            
            if len(childfids) > 2:
                for child_i in range(2, len(childfids)):
                    self.df_logger.info("-"*70)
                    self.df_logger.info("childfids={}".format(child_i))
                    # time.sleep(random.randint(1,2))
                    fid = childfids[child_i]
                    param['fid'] = childfids[child_i]
                    params["param"] = json.dumps(param, separators=(',', ':'))
                    params['signature'] = gen_signature("/v1/forum/getChildForums", param=param)
                    url = url_join(base_url + "/v1/forum/getChildForums", query_dict=params)
                    if os.path.exists(os.path.join(snapshot_path, "{}".format(fid))):
                        self.df_logger.info("Load Fid: {}".format(param['fid']))
                        with open(os.path.join(snapshot_path, "{}".format(fid)), "r", encoding="utf-8") as fp:
                            json_response = json.load(fp=fp)
                    else:
                        time.sleep(random.randint(5,15))
                        bool_get_ip = True
                        while bool_get_ip:
                            ip = self.ipproxypool.get_available_proxy()
                            if ip:
                                bool_get_ip=False
                            else:
                                time.sleep(5)
                        proxyMeta = "http://{}".format(ip)
                        proxydata={"https": proxyMeta, "http":proxyMeta}
                        try:
                            self.df_logger.info("Request Fid: {}".format(param['fid']))
                            response = requests.request("GET", url=url, headers=headers, proxies=proxydata, timeout=(5,9))
                            self.ipproxypool.release_proxy(ip)
                        except Exception as errmsg:
                            self.ipproxypool.release_proxy(ip)
                            self.df_logger.error("###url={}".format(url))
                            self.df_logger.error("###{}".format(errmsg))
                            try: #失败之后再请求一次
                                response = requests.request("GET", url=url, headers=headers, timeout=(5,9))
                            except:
                                continue
                                             
                        if response.status_code !=200:
                            self.df_logger.error(province, ":", url)
                            continue
                        # print("request : ip={}".format(ip))
                        json_response = json.loads(response.text)
                        with open(os.path.join(snapshot_path, "{}".format(fid)), "w", encoding="utf-8") as fp:
                            fp.write(response.text)

                    city_list = province_dict.get(province, [])
                    _leader_info = self.parse_province_liuyan(json_response, province, city_list)
                    self.df_logger.info(_leader_info)
                    if _leader_info:
                        output.extend(_leader_info)
                    res_data.append({
                        'province': province,
                        'url': url,
                        "leader_info":_leader_info
                    })
        return output
                    

    @staticmethod
    def get_province_dict():
        data_file = os.path.join(os.path.dirname(__file__), '../data/中央组织机构/province_and_city_list_v0923.json')
        with open(data_file, 'r', encoding='utf-8') as f:
            province_list = json.loads(f.read())
        province_dict = {
            item['province']: item['city_list'] for item in province_list
        }
        return province_dict

    @staticmethod
    def transfer_for_write(province, city, name_cpc, name_gov, oposition):
        if city == "":
            return
        differ_province_name = ["内蒙古", "广西", "西藏", "宁夏", "新疆"]
        prov_pattern = re.compile(r'省|市|自治区')
        if prov_pattern.search(province) != -1:
            pass
        else:
            if province not in differ_province_name:
                if province in ["北京", "上海", "重庆", "天津"]:
                    province = province + "市"
                else:
                    province = province + "省"
            elif province == "内蒙古" or province == "西藏":
                province = province + "自治区"
            elif province == "广西":
                province = "广西壮族自治区"
            elif province == "宁夏":
                province = "宁夏回族自治区"
            else:
                province = "新疆生产建设兵团"
        parent = ""
        url = ""
        df_leader_info = []
        # 用关键词检索是否可以提取到url
        if len(name_cpc) > 0 and name_cpc not in ["空缺"]:
            name = name_cpc
            position = "书记"
            org = "中共"
            keyword = "(" + province + city + position + ")"
            title = province+city+"委"+position
            url = ""
            df_ld_info = {"parent":"地方", "province":province, "city":city, "org":org, "position":position, "order":1, "leader_type":0, "name":name, "url":url, "present post": title}
            df_leader_info.append(df_ld_info)

        if len(name_gov) > 0 and name_gov not in ["空缺"]:
            name = name_gov
            position = oposition if oposition else "市（区、州）长行署专员" 
            org = "政府"
            keyword = "(" + province + city + ")"
            # title = province+city+"政府"+position
            title = province+city+position
            url = ""
            df_ld_info = {"parent":"地方", "province":province, "city":city, "org":org, "position":position, "order":1, "leader_type":0, "name":name, "url":url, "present post": title}
            df_leader_info.append(df_ld_info)

        return df_leader_info

    def parse_province_liuyan(self, response, province, city_list):
        """解析liuyan版获取的json数据，response是json数据"""
        # logging.log(logging.INFO, "地方领导:{}".format(province))
        pos_pattern = re.compile(r'书记|市长|区长|省长|主席|盟长|州长|行署专员|管理委员会主任|县长|管委会主任|行政长官|助理')
        province = province
        city = ""
        name_cpc = ""
        name_gov = ""
        df_leader_info = []
        if not response:
            return df_leader_info
        leader_data = response['data'] if "data" in response else ""
        if not leader_data:
            return df_leader_info
        for leader in leader_data[:2]:
            pos_search = pos_pattern.search(leader['name'])
            oposition = pos_search.group() if pos_search else ""
            if pos_search and pos_search.span()[1] < len(leader['name']) - 1:
                if pos_search.group() in ['书记']:
                    name_cpc = leader['name'][pos_search.span()[1]:]
                    name_cpc = re.sub(r'\（.*\）', "", name_cpc)
                else:
                    name_gov = leader['name'][pos_search.span()[1]:]
                    name_gov = re.sub(r'\（.*\）', "", name_gov)
            for ci in city_list:
                if leader['name'].find(ci) != -1:
                    city = ci
        # print(city)
        
        df_leader_for_city = self.transfer_for_write(province=province, city=city, name_cpc=name_cpc, name_gov=name_gov, oposition=oposition)
        # print("province:", province, "\tcity:", city, "\tleaders:", len(df_leader_for_city))
        if df_leader_for_city:
            pass
            # print("province:{:<12}city:{:<8}leaders:{}".format(province, city, len(df_leader_for_city)))
        else:
            print("失败 province:{:<12}city:{:<8}leaders:{}".format(province, city, 0))
        if df_leader_for_city:
            df_leader_info.extend(df_leader_for_city)
        return df_leader_info

    def get_df_leader_info(self, df_org_list):
        """弃用"""
        df_leader_info = []
        province_dict = self.get_province_dict()  # {'北京市': ['朝阳区', "东城区"]}

        data = self.get_df_leader_data(df_org_list)  # [{'province': str, 'url': str, 'resp': dfld_data} ...]        
        for item in data:
            province = item['province']
            response = item.get("resp", "")
            city_list = province_dict.get(province, [])
            _leader_info = self.parse_province_liuyan(response, province, city_list)
            df_leader_info += _leader_info

        return df_leader_info


if __name__=="__main__":
    # 获取地方机构列表
    import json
    orglist = OrgList()
    df_org_list = orglist.get_df_org_list()

    dfleaderinfo = DfLeaderInfo()

    df_leaders = dfleaderinfo.get_df_leader_data(df_org_list=df_org_list)

    with open("/home/<USER>/leader-info/UpdateLeaderInfo/runtime/20250325/df_leaders_info1111.json", "w", encoding="utf-8") as fp:
        json.dump(df_leaders, fp, ensure_ascii=False, indent=2)
    # for idx, item in enumerate(df_leaders):
    #     print(idx, ":", item)