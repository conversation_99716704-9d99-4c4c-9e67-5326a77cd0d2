from elasticsearch import Elasticsearch
from ops.esops import get_date_from_ES
import os, sys, json, random
from elasticsearch import Elasticsearch
# print(sys.path)
# from esops import get_date_from_ES
from ops.esops import get_date_from_ES

from elasticsearch.helpers import bulk, scan

cwd = os.path.dirname(__file__)


class OldLeaderInfo(object):
    '''获取es的历史数据'''
    @staticmethod
    def get_old_leader_info():
        with open(os.path.join(os.path.join(os.path.abspath(os.path.join(cwd, '..')), "configs"), 'es_config.json'), "r", encoding="utf-8") as fp:
            esconfig = json.load(fp=fp)
        # es = Elasticsearch(["http://************:9203"], basic_auth=('elastic', 'gz123@people!@#'), request_timeout=120, max_retries=10, retry_on_timeout=True)
        es = Elasticsearch(hosts=esconfig["host"], basic_auth=(esconfig["user"], esconfig["passwd"]), request_timeout=120, max_retries=10, retry_on_timeout=True)
        print(esconfig["leader_info_index"])
        leader_info_ESold = get_date_from_ES(es, index_name=esconfig["leader_info_index"])

        old_leader_info_es = [item['_source'] for item in leader_info_ESold]
        # print(f"old_leader_info_es：{len(old_leader_info_es)} \n【{old_leader_info_es[0]} ...】")
        old_leader_info = []
        noepostion_count = 0   # 有epositio的数量
        url_noeposition_count = 0 # 有url但没有eposition
        url_count = 0
        urls = []
        for item in old_leader_info_es:
            if 'order' not in item.keys():
                item_order = 100
            else:
                item_order = item['order']

            if 'leader_type' not in item.keys():
                info = {"parent": item["type"], "province": item["province"], "city": item["city"], "org": item["organization"], "position": item["oPosition"], "order": item_order, "name": item["cName"],  "url": item["url"]}
                old_leader_info.append(info)
            else:
                if item['leader_type'] == 0:
                    info = {"parent": item["type"], "province": item["province"], "city": item["city"], "org": item["organization"], "oposition": item["oPosition"], "order": item_order, "name": item["cName"], "url": item["url"],"cposition":item["cPosition"], "eposition":item["ePosition"]}
                else:
                    continue
                old_leader_info.append(info)

            if not item["ePosition"]:
                noepostion_count+=1
                if item["url"]:
                    url_noeposition_count+=1
            if item["url"] and item["url"] not in urls:
                urls.append(item["url"])
                url_count+=1

        print("总条目：", len(old_leader_info))
        print("没有epostion：", noepostion_count)
        print("有url没有eposition：", url_noeposition_count)
        print("有url的条目：", len(urls), "\t", url_count)

        # print(f"old_leader_info：{len(old_leader_info)} \n【{old_leader_info[0]} ...】")

        return old_leader_info
    
    def new_get_leader_data(self):
        with open(os.path.join(os.path.join(os.path.abspath(os.path.join(cwd, '..')), "configs"), 'es_config.json'), "r", encoding="utf-8") as fp:
            esconfig = json.load(fp=fp)
        es = Elasticsearch(hosts=esconfig["host"], basic_auth=(esconfig["user"], esconfig["passwd"]), request_timeout=120, max_retries=10, retry_on_timeout=True)
        print(esconfig["leader_info_index"])
        doc = {'query': {'match_all': {}}}
        res = scan(client=es, query=doc, scroll='10m', index='leader-info-final', timeout='10m') 
        mylist = []
        with open("mylist1.json", "w", encoding="utf-8") as fp:
            for item in res:
                _id = item["_id"]
                cposition = item["_source"]["cPosition"]
                eposition = item["_source"]["ePosition"]
                if cposition and not eposition:
                    # item["_source"]["ePosition"]=item["_source"]["cPosition"]
                    update_body = {
                        "doc":{
                            "ePosition": cposition
                        }
                    }
                    es.update(index="leader-info-final", id=_id, body=update_body)
                    mylist.append(item)
            json.dump(mylist, fp=fp, ensure_ascii=False, indent=2)
        return res

    
if __name__=="__main__":
    obj = OldLeaderInfo()
    obj.get_old_leader_info()
    