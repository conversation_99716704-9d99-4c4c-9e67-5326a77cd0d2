import enum
import re
import sys, os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from spiders import MPSpider
from logger import get_logger
from urllib.parse import quote_plus
import parsers.parser_html as parser_html
import json, time
import os
from datetime import datetime
import traceback, logging
from scrapy.http import HtmlResponse
from ip_proxy.ip_pool_client import main_ip_monitor
import logging.config
from configparser import ConfigParser
import sys
sys.path.append("/home/<USER>/leader-info/UpdateLeaderInfo")
import logging

current_date = datetime.now().date().strftime("%Y%m%d")
current_path = os.path.dirname(__file__)
log_dir = os.path.join(os.path.abspath(os.path.join(current_path,"..")), "runtime/{}/logs".format(current_date))


# def main():
#     import os
#     with open(os.path.join(os.path.dirname(__file__), 'tmp/info.json')) as f:
#         info = json.load(f)[0]
#     leader_info = parse_leader_info(info)
#     print(leader_info)

# 对输入的input_str中的汉字进行编码
def encode_chinese(input_str):
    # 定义一个函数来对汉字进行编码
    def encode_match(match):
        return quote_plus(match.group(0))
    
    # 使用正则表达式查找汉字并进行编码
    encoded_str = re.sub(r'[\u4e00-\u9fff]', encode_match, input_str)
    return encoded_str
'''
# def init_logger(runing_path):
#     parser_log_path = runing_path #os.path.join(runing_path, "logs")
#     # 确保日志目录存在
#     if not os.path.exists(parser_log_path):
#         os.makedirs(parser_log_path)
    
#     # 创建一个新的日志记录器
#     logger = logging.getLogger("NewLeader")
#     logger.setLevel(logging.DEBUG)
#     handler = logging.FileHandler(os.path.join(parser_log_path, "parser_result.log"), mode="a")
#     print("++++parser_log = ", os.path.join(parser_log_path, "parser_result.log"))
#     formatter = logging.Formatter('%(asctime)s - %(message)s')
#     handler.setFormatter(formatter)
#     logger.addHandler(handler)
#     return logger
'''
class NewLeaderInfo(object):

    def __init__(self, spider=None, runing_path = os.path.abspath(os.path.join(log_dir, ".."))):  #os.path.abspath(os.path.join(log_dir, ".."))
        # self.spider = spider if spider is not None else MPSpider('/home/<USER>/leader-info/UpdateLeaderInfo/configs/config.yaml')
        # self.spider = spider if spider is not None
        self.runing_path = runing_path 
        self.logger = get_logger(log_dir=log_dir)    #self._get_logger()
        self.parser_result_logger = self.update_log_file(log_file="parser_result.log")
        

    def _get_logger(self):
        logger = logging.getLogger("NewLeaderInfo")
        logger.setLevel(logging.DEBUG)
        return logger
  
    def update_log_file(self, log_file="parser_result.log"):
        # 设置file处理器
        if not os.path.exists(log_dir):
            os.mkdir(log_dir)
        log_file_path = os.path.join(log_dir, f"{log_file}")
        # print(f"log file path: {log_file_path}")

        #创建一个日志记录器
        parser_result_logger = logging.getLogger("parser_result")
        parser_result_logger.setLevel(logging.DEBUG)
        try:
            filehandler = logging.FileHandler(filename=log_file_path, mode="a", encoding="utf-8")
            filehandler.setLevel(logging.DEBUG)
        except Exception as e:
            print(f"Error setting up file handler: {e}")
            return 
        formater = logging.Formatter("%(levelname)s | %(asctime)s | %(filename)s:%(lineno)d | %(message)s")
        filehandler.setFormatter(formater)
        parser_result_logger.addHandler(filehandler)

        # 设置console处理器
        streamhandler = logging.StreamHandler()
        streamhandler.setLevel(logging.DEBUG)
        streamhandler.setFormatter(formater)
        parser_result_logger.addHandler(streamhandler)
        return parser_result_logger

    def get_new_leader_info(self, org_list):
        self.parser_result_logger.warning("begin get_new_leader_info")
        self.parser_result_logger.info("dfjjdkf")
        #  print("begin get_new_leader_info")
        return
    
    def get_new_leader_info2(self, org_list):
        search_data = {f"https://baike.baidu.com/search/word?fromModule=lemma_search-box&lemmaId=&word={quote_plus(org['name'].encode('utf-8'))}": org for org in org_list if org["name"] is not None}
        search_urls = list(search_data.keys())
        # search_urls = search_urls[:10]
        
        #多次循环，尽量让failed_org减少
        quanbu_org_info =[]
        count = 0
        xunhuancaiji = True
        while xunhuancaiji:
            count+=1
            time.sleep(15)
            if count>1:
                xunhuancaiji=False
            org_info, failed_org = self._get_org_info(search_urls, search_data)
            if org_info:
                quanbu_org_info.extend(org_info)
                if len(failed_org)<40:
                    xunhuancaiji=False
            else:
                continue
            search_data=dict()
            for org in failed_org:
                org_name = org.get("name", "")
                if org_name:
                    search_data={f"https://baike.baidu.com/search/word?fromModule=lemma_search-box&lemmaId=&word={quote_plus(org_name)}": org for org in org_list if org["name"]==org_name}
            search_urls = list(search_data.keys())
        self.parser_result_logger.info(r"待采集省部级机构数={}\t采集失败的省部级机构数={}".format(len(org_list), len(org_list)-len(quanbu_org_info)))
        try:
            tmp_leader_info = self.parse_leader_info(org_info=quanbu_org_info, parser_logger=self.parser_result_logger)
        except:
            self.logger.warning(traceback.format_exc())
            print(traceback.format_exc())
            return []
        new_leader_info = list()
        for item in tmp_leader_info:
            new_leader_info += item
        return new_leader_info
    
    @staticmethod
    def checkif_yanzheng(response):
        try:
            title_pat = re.compile("<title>(.*?)</title>")
            content = response.text
            title = "".join(title_pat.findall(content))
            return '验证' in title
        except:
            return True

    def _get_org_info(self, search_urls, search_data):
        self.parser_result_logger.info("开始采集机构信息")
        try:
            search_result = main_ip_monitor(urls=search_urls, runing_path=os.path.abspath(self.runing_path), ip_nums=min(5, len(search_urls)))
        except Exception as e:
            self.parser_result_logger.error(f"Error: {e}")
            return [], []
        self.parser_result_logger.setLevel(logging.DEBUG)
        for idx, item in enumerate(search_result):
            try:
                if self.checkif_yanzheng(item['resp']):
                    item['code'] = 403
                    self.parser_result_logger.warning(f"验证: {item['url']}")#logger.warning(f"{item['proxy']}被反爬: {item['url']}")
                else:
                    if "code" not in item.keys():
                        item["code"] = 0
            except Exception as errmsg:
                self.parser_result_logger.error("Error:{}\nitemurl:{}\nitem={}".format(errmsg, item["url"], item))

        org_info = list()
        org_info_failed = list()
        title_pat = re.compile("<title>(.*?)</title>")
        search_data_keys = list(search_data.keys())

        for item in search_result:
            url = item['url']
            url_temp = encode_chinese(url)
            if url in search_data_keys:
                org_type = search_data[url].get("parent", "")#search_data[url]['parent']
                org_name = search_data[url].get("name", "")
            elif url_temp in search_data_keys:
                org_type = search_data[url_temp].get("parent", "")
                org_name = search_data[url_temp].get("name", "")
            else:
                continue

            # org_name = search_data[url].get("name", "")  #search_data[url]['name']
            if len(org_type)<1 or len(org_name)<1:
                self.parser_result_logger.info("search_data={}".format(search_data))
                self.parser_result_logger.error(f"org_type={org_type}, org_name={org_name} 为空")
                continue
            resp = item['resp']
            if isinstance(resp,str):
                page_content = resp
            else:
                page_content = resp.text
            title = "".join(title_pat.findall(page_content))
            info = {
                'url': url,
                'org_type': org_type,
                'org_name': org_name,
                'page_content': page_content,
                'title': title
            }
            if item['code'] == 0:
                org_info.append(info)
            else:
                org_info_failed.append(info)
        self.parser_result_logger.info("采集成功的机构：{}".format(len(org_info)))
        self.parser_result_logger.info("采集失败的机构：{}".format(len(org_info_failed)))
        self.parser_result_logger.info("采集识别失败的机构列表：{}".format([item['org_name'] for item in org_info_failed]))
        return org_info, org_info_failed


    def get_org_info_from_orgname(self, org_name="中国共产党上海市委员会"):
        org_info = {}
        zhonggong_pat = re.compile(r"中国共产党(.*)委员会")
        zhengfu_pat = re.compile(r"(.*?)人民政府")
        renda_pat = re.compile(r"(.*?)人民代表大会")
        zhengxie_pat = re.compile(r"中国人民政治协商会议(.*)委员会")
        if zhonggong_pat.findall(org_name):
            org_info["province"]="".join(zhonggong_pat.findall(org_name))
            org_info["org_name"]=org_name
            org_info["org"] = "中共"
            return org_info
        if zhengfu_pat.findall(org_name):
            org_info["province"]="".join(zhengfu_pat.findall(org_name))
            org_info["org_name"]=org_name
            org_info["org"] = "政府"
            return org_info
        if renda_pat.findall(org_name):
            org_info["province"]="".join(renda_pat.findall(org_name))
            org_info["org_name"]=org_name
            org_info["org"] = "人大"
            return org_info
        if zhengxie_pat.findall(org_name):
            org_info["province"]="".join(zhengxie_pat.findall(org_name))
            org_info["org_name"]=org_name
            org_info["org"] = "政协"
            return org_info


    def parse_leader_info(self, org_info, parser_logger=logging.getLogger("pp")):   
        cmdinfo_file = os.path.join(os.path.abspath(os.path.join(current_path,"..")), "runtime/{}/parse_html_details.json".format(current_date))

        parser_logger.warning("机构领导人解析结果保存在：{}".format(cmdinfo_file))
        with open(cmdinfo_file, mode="w+", encoding="utf-8") as fp:
            leader_infos = list()
            for item_idx, item in enumerate(org_info):
                org_type = item['org_type']
                if org_type not in ["领导机构", "中直机构", '中管高校', "军事机构", "国务院机构", "国家级协会","央企",'政策性银行', '地方']:
                    continue
                # if item['org_name'] not in ['湖北省人民政府']:
                #     continue
                parser_logger.info('--'*20)
                parser_logger.info(item['org_name'])
                org_name = item['org_name']
                content = item['page_content']
                if not content:
                    continue     
                if org_type == '领导机构':
                    leader_info = parser_html.parse_cpc(org=org_name, page_content=content)
                elif org_type == '中直机构':
                    leader_info = parser_html.parse_zz(org=org_name, page_content=content)
                elif org_type == '中管高校':
                    leader_info = parser_html.parse_school(org=org_name, page_content=content)
                elif org_type == '军事机构':
                    leader_info = parser_html.parse_pla(org=org_name, page_content=content)
                elif org_type == '国务院机构':
                    leader_info = parser_html.parse_gov(org=org_name, page_content=content)
                elif org_type == '国家级协会':
                    leader_info = parser_html.parse_xh(org=org_name, page_content=content)
                elif org_type == '央企':
                    leader_info = parser_html.parse_yq(org=org_name, page_content=content)
                elif org_type == '政策性银行':
                    leader_info = parser_html.parse_yh(org=org_name, page_content=content)
                elif org_type == '地方':
                    org_info = self.get_org_info_from_orgname(org_name)  
                    try:                 
                        leader_info = parser_html.parse_cppcc(org_info=org_info, page_content=content)
                    except:
                        parser_logger.warning("{}:{}".format(org_name, traceback.format_exc()))
                        
                    # print(org_info, ":", len(leader_info))
                if "leader_info" not in locals():
                    print("-"*60)
                    print(org_type, "\t", org_name)
                    parser_logger.warning('leader_info=None')
                    print("-"*60)
                    continue
                temp = {"idx": item_idx, "orgtype": org_type, "orgname": org_name, "num_leaders": len(leader_info),"leaders":leader_info}
                json.dump(temp, fp=fp, ensure_ascii=False, indent=2)
                fp.write("\n")
                leader_infos.append(leader_info)
                # if org_type not in ["地方"]:
                #     print(org_name,":", len(leader_info))   
                parser_logger.info("len(leaders)={}".format(len(leader_info)))       
        return leader_infos


if __name__ == '__main__':
    from ops.org_list import OrgList
    org = OrgList()
    org_list = org.get_org_list()
    leader = NewLeaderInfo()
    res = leader.get_new_leader_info2(org_list)
    print(res)