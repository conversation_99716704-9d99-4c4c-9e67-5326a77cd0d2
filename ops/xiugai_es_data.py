from socket import timeout
from elasticsearch import Elasticsearch
from datetime import datetime
import os, json

def get_time():
    _time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return _time

# print(get_time())
def get_date_from_ES(escon="", index_name="", query =""):
    if not query:
        query = {
            "query": {
                "match_all": {}  # 查询所有文档
            },
            "sort": [
                {"create_time": {"order": "desc"}}  # 按 create 字段降序排列
            ]
        }

    res = escon.search(index=index_name, body=query, timeout='10m', size=10000)
    return res["hits"]["hits"]

if __name__=="__main__":
    import csv
    import pandas as pd
    cwd = os.path.dirname(__file__)
    with open(os.path.join(os.path.abspath(os.path.join(cwd, "..")),"configs/es_config.json"), "r", encoding="utf-8") as fp:
        esconfig = json.load(fp=fp)
    
    es = Elasticsearch(esconfig["host"], basic_auth=(esconfig["user"], esconfig["passwd"]), request_timeout=120, max_retries=10, retry_on_timeout=True)
    leader_data = get_date_from_ES(escon=es, index_name="leader-info-final")
    fallen_date = get_date_from_ES(escon=es, index_name="fallen-leader-clean")

    names = []
    positions = []
    output = []
    with open(os.path.join(cwd,"es_data/leaderinfo_inES_{}.xlsx".format('20241009')), "w", encoding="utf-8") as fp:
        fieldnames = leader_data[0].get("_source").keys()
        # writer = csv.DictWriter(fp, fieldnames=fieldnames)
        # writer.writeheader()
        for item in leader_data:
            name = item["_source"].get("cName")
            position = item["_source"].get("ePosition")
            url = item["_source"].get("url")
            if name and url and position=="":
                url = "https://baike.baidu.com" + url
                item["_source"]["url"] = url
                output.append(item.get("_source"))
            if name and not url:
                output.append(item.get("_source"))
        df = pd.DataFrame(output)
        df.to_excel("es_data/leaderinfo_inES_{}.xlsx".format('20241009'), index=True)
        # for row in output:
        #     writer.writerow(row)
            # if name and name not in names:
            #     names.append(name)
            # if position and position not in positions:
            #     positions.append(position)
        for item in fallen_date:
            name = item["_source"].get("name")
            position = item["_source"].get("position")
            if name and name not in names:
                names.append(name)
            if position and position not in positions:
                positions.append(position)
        info = {"names": names, "positions": positions}     
        # json.dump(info, fp, ensure_ascii=False, indent=4)
    '''    
    # 将三个数据保存到json文件里
    date_today = datetime.now().strftime("%Y%m%d")
    with open(os.path.join(cwd,"es_data/fallen_leader_clean_{}.json".format(date_today)), "w", encoding="utf-8") as fp:
        json.dump(fallen_date, fp, ensure_ascii=False, indent=2)
    with open(os.path.join(cwd, "es_data/leader_info_{}.json").format(date_today), "w", encoding="utf-8") as fp:     
        json.dump(obj=leader_data, fp=fp, ensure_ascii=False, indent=2)
    with open(os.path.join(cwd, "es_data/leader_info_v1_{}.json").format(date_today), "w", encoding="utf-8") as fp:
        query = {"query": {"match_all":{}}}
        leader_info_v1 = get_date_from_ES(escon=es, index_name="leader-info-v1", query=query)
        json.dump(obj=leader_info_v1, fp=fp, ensure_ascii=False, indent=2)
    '''

    """
    query={
        "query":{
            "match":{
                "update_time":3
            }
        }
    }
    es_data = get_date_from_ES(escon=es, index_name="leader-info-final", query=query)
    print(len(es_data))
    for item in es_data:
        print("")
        _id = item["_id"]
        update_time = item["_source"]["update_time"]
        if update_time in [2,3]:
            print(_id)
            new_update_time = get_time()
            script = {  
                    "source": "ctx._source.update_time = params.c1",  
                    "lang": "painless",  
                    "params": {  
                        "c1": new_update_time 
                    }  
                }  
            response = es.update(index="leader-info-final", id=_id, body={"script": script}) 
            print(response)"""
    