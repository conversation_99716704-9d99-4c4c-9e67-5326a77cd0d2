# -*- coding: utf-8 -*-
from email.quoprimime import body_check
import logging, json, os
from elasticsearch.helpers import scan
from elasticsearch import Elasticsearch
os.chdir(os.path.dirname(__file__))

def get_date_from_ES(escon="", index_name="", query =""):
    if not query:
        query = {
            "query": {
                "match_all": {}  # 查询所有文档
            },
            "sort": [
                {"create_time": {"order": "desc"}}  # 按 create 字段降序排列
            ]
        }
        # query = {
        #     "match_all":{},
        #     "sort": [ {"create_time": {"order": "desc"}} ]
        # }
    if escon=="" or index_name=="":
        raise ValueError("get_date_from_ES: ES or IndexName（{}） is empty!".format(index_name))
        logging.log(logging.ERROR, "get_date_from_ES: ES or IndexName is empty!")
        
    tt = escon.indices.exists(index=index_name)
    if not tt:
        # logging.log(logging.ERROR, "get_date_from_ES: index not exits!")
        raise ValueError("get_date_from_ES: index not exits!")
    # res = escon.search(index=index_name, query=query, timeout='10m', size=10000)
    res = escon.search(index=index_name, body=query, timeout='10m', size=10000)
    return res["hits"]["hits"]


def create_ES_index(escon="", index_name="", mappings=""):
    if escon=="" or index_name=="" or mappings=="":
        logging.log(logging.ERROR, "create_ES_index: ES or IndexName or Mappings is empty!")
    panduan_exist = escon.indices.exists(index = index_name)
    if not panduan_exist:
        escon.indices.create(index=index_name, mappings=mappings, settings={"max_result_window":10000000})
    return

if __name__=="__main__":
    with open("/home/<USER>/leader-info/UpdateLeaderInfo/configs/es_config.json", "r", encoding="utf-8") as fp:
        esconfig = json.load(fp=fp)
        # es = Elasticsearch(["http://************:9203"], basic_auth=('elastic', 'gz123@people!@#'), request_timeout=120, max_retries=10, retry_on_timeout=True)
        es = Elasticsearch(hosts=esconfig["host"], basic_auth=(esconfig["user"], esconfig["passwd"]), request_timeout=120, max_retries=10, retry_on_timeout=True)
    res = get_date_from_ES(es, index_name='leader-relative')
    print(len(res))

    res_tmp = []
    for item in res:
        ttt = item["_source"]
        if ttt["type"]=="2" or ttt["type"]==2:
            ttt["type"]="前任领导人"
        elif ttt["type"]=="1" or ttt["type"]==1:
            ttt["type"]="领导人家属"
        else:
            print(item)
        if ttt not in res_tmp:
            res_tmp.append(ttt)
    import pandas as pd
    df = pd.DataFrame(res_tmp)
    excel_file = "/home/<USER>/leader-info/UpdateLeaderInfo/leader_relative.xlsx"
    df.to_excel(excel_writer=excel_file, index=False, engine="openpyxl")
    print(f"数据已经写入{excel_file}")
        