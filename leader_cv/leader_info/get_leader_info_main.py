# -*- coding: utf-8 -*-
"""
调用八爪鱼获取机构内容
解析页面内容，获取
"""
import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.getcwd())
print(os.path.dirname(os.path.abspath(__file__)))
print(os.getcwd())

import requests
import pandas
import json
from lxml import etree
import re
from urllib.parse import quote_plus
from elasticsearch import Elasticsearch, helpers
from leader_cv.leader_cv_main import get_leader_pageContent_octopus




def get_zyleader_info_fromOctopus(leader_url_list=[], octopusTask="get_pageContent_from_urllist"):
    leader_info = []
    # 1、url列表调用八爪鱼,拿到网页page
    print("调用八爪鱼任务: ", octopusTask)
    base_url = "https://openapi.bazhuayu.com/"
    user_param = {
        "username": "zhangfzpeople",
        "password": "zhang!@#",
        "grant_type": "password"}
    try:
        leader_page_content = get_leader_pageContent_octopus(base_url=base_url, params=user_param, url_list=leader_url_list, taskName=octopusTask)
    except Exception as octopus_error:
        print("Ostopus Error:", octopus_error)
        return
    # 2、从八爪鱼结果里拿到cv字段，并写入leader_cv(name, url, cv, create_time)
    print("\n解析html源码:")
    url_tmp = []
    for leaderinfo in leader_page_content:
        if leaderinfo["Url"] not in url_tmp:
            url_tmp.append(leaderinfo["Url"])
        else:
            continue
        desc = leader_info["Desc"]
        tit_pat = re.compile("现任(.*?)。")
        title = "".join(tit_pat.findall(desc))
        leader_info.append({"url": leaderinfo["Url"], "title": title})

    return leader_info


def get_zyleader_info():
    zy_leader_fileName = "zy_leader.json"
    zy_leader_info = []
    headers = {
        "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36 Edg/107.0.1418.35'
    }
    # 读取文件，获取领导人姓名   
    print("zy-leader: step1 读取文件，获取领导人姓名")
    url_list = []
    with open(os.path.dirname(os.path.abspath(__file__))+"/"+zy_leader_fileName, "r", encoding="utf8") as fp:
        leader_lines = fp.readlines()
        info = {}
        for line in leader_lines:
            info = json.loads(line)
            zy_leader_info.append(info)
            url = "".join(["https://baike.baidu.com", info["url"]])
            if url not in url_list:
                url_list.append(url)
            else:
                continue
    fp.close()
    # 调用八爪鱼任务，获取领导人信息网页源码
    print("zy-leader: step2 读取文件，调用八爪鱼任务，获取领导人信息网页源码及title")
    leader_url_title = get_zyleader_info_fromOctopus(leader_url_list=url_list, octopusTask="get_leaderdesc_from_urllist")
    print(len(leader_url_title))

    # 将领导人信息写入文件
    print("zy-leader: step3 将zy领导人信息写入接送文件")
    with open(os.path.dirname(os.path.abspath(__file__))+"/"+zy_leader_fileName, "w", encoding="utf8") as fp:
        for leader in zy_leader_info:
            name = leader["name"]
            leader["present post"] = [url_title["title"] for url_title in leader_url_title if url_title["url"].find(quote_plus(name))!=-1]
            info_str = json.dumps(leader, ensure_ascii=False)+"\n"
            fp.write(info_str)
    fp.close()

    
    return zy_leader_info


# 从文件夹获取机构
def get_org_from_file():
    outputorg_list = []
    orgfile_dir = "E:\leader_baike_src\leader_baike\中央组织机构"
    with open(os.path.join(orgfile_dir, "领导机构"), 'r', encoding='utf-8') as f:
        org_list = f.readlines()
    for org in org_list:
        org_str = {"parents":"领导机构", "org":org}
        outputorg_list.append(org_str)

    with open(os.path.join(orgfile_dir, "国家级协会"), 'r', encoding='utf-8') as f:
        org_list = f.readlines()
    for org in org_list:
        org_str = {"parents":"国家级协会", "org":org}
        outputorg_list.append(org_str)
    
    with open(os.path.join(orgfile_dir, "国务院机构"), 'r', encoding='utf-8') as f:
        org_list = f.readlines()
    for org in org_list:
        org_str = {"parents":"国务院机构", "org":org}
        outputorg_list.append(org_str)

    with open(os.path.join(orgfile_dir, "央企"), 'r', encoding='utf-8') as f:
        org_list = f.readlines()
    for org in org_list:
        org_str = {"parents":"央企", "org":org}
        outputorg_list.append(org_str)
    
    with open(os.path.join(orgfile_dir, "政策性银行"), 'r', encoding='utf-8') as f:
        org_list = f.readlines()
    for org in org_list:
        org_str = {"parents":"政策性银行", "org":org}
        outputorg_list.append(org_str)
    
    with open(os.path.join(orgfile_dir, "中管高校"), 'r', encoding='utf-8') as f:
        org_list = f.readlines()
    for org in org_list:
        org_str = {"parents":"中管高校", "org":org}
        outputorg_list.append(org_str)
    
    with open(os.path.join(orgfile_dir, "中直机构"), 'r', encoding='utf-8') as f:
        org_list = f.readlines()
    for org in org_list:
        org_str = {"parents":"中直机构", "org":org}
        outputorg_list.append(org_str)

    with open(os.path.join("E:\leader_baike_src\leader_baike", 'province_and_city_list.json'), 'r', encoding='utf-8') as f:
        province_list = json.loads(f.read())
    # print("province_list:{}".format(province_list))
    # print("len province list={}\n".format(len(province_list)))
    #
    for p in province_list:
        province = p["province"]
        # 解析各级省级省委、政府、人大、政协，没有问题，2022-07-12 JIN
        org_name = "中国共产党" + province + "委员会"
        org_str = {"parents": "地方", "org": org_name}
        outputorg_list.append(org_str)

        org_name = province + "人民政府"
        org_str = {"parents": "地方", "org": org_name}
        outputorg_list.append(org_str)

        if province in ["新疆维吾尔自治区", "西藏自治区"]:
            org_name = province + "人民代表大会"    
        else:
            org_name = province + "人民代表大会常务委员会"
        org_str = {"parents": "地方", "org": org_name}
        outputorg_list.append(org_str)

        org_name = "中国人民政治协商会议" + province + "委员会"
        org_str = {"parents": "地方", "org": org_name}
        outputorg_list.append(org_str)
    return outputorg_list

# 从ES获取org_list
def get_orglist_fromES():
    org_list=[]
    return org_list


# 调用八爪鱼获取机构内容
def get_date_from_OctopusTask():
    date_list = []
    cycle_index = 0   # Task访问次数
    max_cycle = 10    # Task最大调用次数

    return date_list

# 获取部委领导人信息
def get_senior_leader_info():
    # 1、从数据库表中去取机构页面
    org_info_list = get_org_from_file()
    
    # 2、解析页面
    return

if __name__=="__main__":
    zy_leaders = get_zyleader_info()