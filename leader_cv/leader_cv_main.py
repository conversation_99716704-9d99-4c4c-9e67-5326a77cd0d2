# -*- coding: utf-8 -*-
import os
import sys
import requests

from datetime import datetime

import re, json, logging
import time
from lxml import etree
from elasticsearch import helpers, Elasticsearch
import sqlalchemy
import json
import schedule
import traceback
import sys
sys.path.append(os.path.abspath(os.path.dirname(__file__)))
# print(os.path.abspath(os.path.dirname(__file__)))
from octopus_utilize import get_leader_pageContent_octopus, get_leader_pageContent_octopus_Outdata
import pandas as pd


write_to_ES = True    # 抽取的简历是否要写入ES数据库
# Es参数
configpath = os.path.abspath(os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")), "configs"))
config_file = open(os.path.join(configpath, "configall.json"), "r", encoding="utf-8")
CONFIG = json.load(config_file)
es_config = CONFIG["es"]
__index_name__ = "leader-cv"
es = Elasticsearch(hosts=es_config["host"] , basic_auth=(es_config["user"], es_config["passwd"]), request_timeout=120, max_retries=10, retry_on_timeout=True)

def get_time():
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def create_es_index(index=__index_name__):
    mappings = {
        "properties": {
            "name": {"type": "keyword"}, # 采集姓名
            "url": {"type": "keyword"}, # url链接
            "cv" : {"type": "text"},
            "create_time": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"},
            "update_time": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"}
        }
    }
    es.indices.create(index=index, mappings=mappings, settings={"max_result_window":1000000})


def get_newleader_urlinfo(leader_info_index="", leader_cv_index=""):
    url_list = []
    name_url_list = []
    indexexists = es.indices.exists(index=leader_info_index)
    if leader_info_index=="" or leader_cv_index=="" or not indexexists:
        print("get_newleader_urlinfo Error: indexname=empty or index is not exist")
        return    
    if not es.indices.exists(index=leader_cv_index):
        create_es_index(index=__index_name__)

    # 从leader_cv_index中拿到已有url
    cvquery = {
        "bool":{
            "must":[]
        }
    }
    # 获得已有cv的领导人url
    old_leader_cv_tmp = es.search(index=leader_cv_index, query=cvquery, timeout="10m", size=10000)["hits"]["hits"]
    old_leader_cv_info = []
    old_url = []
    url_pat = re.compile(r"(/item.*)")
    for old_leader in old_leader_cv_tmp:
        url = old_leader["_source"]["url"]
        name = old_leader["_source"]["name"]
        cv = old_leader["_source"]["cv"]
        _id = old_leader["_id"]
        url = "".join(url_pat.findall(url))
        leader_cv_info = {"name":name, "url":url, "cv":cv, "_id":_id}
        if leader_cv_info not in old_leader_cv_info:
            old_leader_cv_info.append(leader_cv_info)
        # if url not in old_url:
        #     old_url.append(url)

    tobe_updated_url_list = []
    try:   # 从json文件里读取需要更新的信息 文件所在地址（/home/<USER>/bazhuayu_data/update_leader_info）        
        leaderinfoquery = {
            "bool":{
                "must":[]
            }
        }
        leaderinfo_list = es.search(index=leader_info_index, query=leaderinfoquery, timeout="10m", size=10000)["hits"]["hits"]
        for leader in leaderinfo_list:
            if len(leader["_source"]["url"])>0 and (leader["_source"]["url"] not in tobe_updated_url_list):
                url = leader["_source"]["url"]
                url = "".join(url_pat.findall(url))
                url = "https://baike.baidu.com" + url
                name_url_list.append([leader["_source"]["cName"], url])
                tobe_updated_url_list.append(url)
    except:
        json_file = "/home/<USER>/bazhuayu_data/update_leader_info/leaders_to_be_updated.json"
        with open(json_file, "r", encoding="utf8") as ff:
            leader_infos = ff.readlines()
            print(type(leader_infos), len(leader_infos))
            for leader in leader_infos:
                leader = json.loads(leader)
                url = leader["url"]
                if url.find("https:")==-1:
                    url = "https://baike.baidu.com"+url
                if url not in tobe_updated_url_list:
                    tobe_updated_url_list.append(url)        
        
    return tobe_updated_url_list, old_leader_cv_info


def join_text(text_list):
    text = ""
    for t in text_list:
        t = t.replace('\n','')
        if not t or t[0] == '[': # 剔除引用的方括号文本
            continue
        text += t
    pattern = re.compile('^\S{1}\s.*')
    if re.match(pattern, text):
        lis_text = list(text)
        lis_text.pop(1)
        text = "".join(lis_text)
    return text


def parse_pagecontent(name="", page=""):
    leader_cv = ""
    page_content = page
    if page=="":
        return leader_cv
    create_new = page_content.xpath("//div[@class='errorBox']")
    if create_new:
        print("----[ERROR] 无法找到{}".format(name))
        return

    start = False
    h2_title = ['个人履历', '人物履历', '个人简介', '人物简介','人物经历', '人物生平', '工作履历', '任免信息',
                '工作经历', '担任职务', '个人经历', '个人简历', '简历', '人物生平', '任职经历', '履历', '职务任免', '职业经历',
                '人物简历', '工作简历', '艺术履历', '从政经历', '个人任职', '主要简历']

    title_list = page_content.xpath('//div [contains(@class, "para")]')
    for title in title_list:
        if start == False:
            title_text = title.xpath("./h2/text()")#title.xpath("./h2[contains(@class, 'title-text')]/text()")
            title_text = "".join(title_text)
            if title_text:
                title_text = title_text.strip()
            if title_text in h2_title:
                start = True
        else:
            if title.xpath('./h2'):
                start = False
                break
            else:
                text_list = title.xpath(
                    "./text() | ./*[not(contains(@class, 'layout-right'))]//text()")
                text = join_text(text_list)
                sstr = json.dumps(text.replace('\"', ''), ensure_ascii=False)+'\n'
                leader_cv = leader_cv + text + "\n"
                #print(json.dumps(text.replace('\"', ''), ensure_ascii=False))

            # print("{} 履历".format(name))
    return leader_cv #print("name={}\nleader cv={}\n".format(name, leader_cv))

"""
获取领导人简历
1、从leader-info读取领导人姓名和url, leader_list[name, url]
2、leader_list: name和url组成的list
3、url列表调用八爪鱼,拿到网页page
3+、修改网页爬取程序，从runtime的百科里读取数据 2025年4月17日
4、从八爪鱼结果里拿到cv字段从八爪鱼结果里拿到cv字段，并写入leader_cv(name, url, cv, create_time)
"""
def get_leader_cv(input_leaderinfo_index="leader-info", output_leadercv_index=__index_name__):
    print("获取时政领导人简历")
    leader_cv = []
    leaderinfo_list = []
    name_url = []
    # log文件设置
    now_date = str(datetime.now().date())
    now_date = re.sub("-", "_", now_date)
    log_file = os.path.join(os.path.abspath(os.path.dirname(__file__)),'log',  "leader_cv_{}.log".format(now_date))

    logging.basicConfig(level=logging.INFO, filename=log_file, filemode="w", format='%(asctime)s - %(pathname)s[line:%(lineno)d] - %(levelname)s: %(message)s')
    logging.getLogger('elastic_transport.transport').setLevel(logging.WARNING)
    cvinfo_log = logging.getLogger("requests").setLevel(logging.WARNING)
    cvinfo_log = logging.getLogger(__name__)


    # 1、从leader-info读取领导人姓名和url, leaderinfo_list
    try:
        query = {
            "bool": {
                "must": []
            }
        }
        res = es.search(index=input_leaderinfo_index, query=query, timeout="10m", size=10000)
        leaderinfo_list = res["hits"]["hits"]
    except Exception as disc_error:
        print(disc_error)
        return
    # 2、name_url: name和url组成的list
    url_list = []
    name_url_list = []
    tobe_updated_url_list, exist_leader_cv_info = get_newleader_urlinfo(leader_info_index = input_leaderinfo_index, leader_cv_index=output_leadercv_index)
    cvinfo_log.info("len(tobe-updated-url-list):{}".format(len(tobe_updated_url_list)))
    cvinfo_log.info("len(exist-leader-cv-info):{}".format(len(exist_leader_cv_info)))
    print("len(tobe-updated-url-list):", len(tobe_updated_url_list))
    print("len(exist-leader-cv-info):", len(exist_leader_cv_info))

    # tobe_updated_url_list = tobe_updated_url_list[0:10]

    # 3、url列表调用八爪鱼,拿到网页page
    taskName = "get_pageContent_from_urllist"

    base_url = "https://openapi.bazhuayu.com/"
    user_param = {
        "username": "zhangfzpeople",
        "password": "zhang!@#",
        "grant_type": "password"}
    
    # 从数据库中读取数据
    configpath = os.path.abspath(os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")), "configs"))
    configfp = open(os.path.join(configpath, "configall.json"), "r", encoding="utf8")
    CONFIG = json.load(configfp)
    sql_config = CONFIG["mysql"]
    try:
        print(traceback.format_exc())
        print("调用八爪鱼任务（", taskName, "）获取待更新领导人信息")
        leader_page_content = get_leader_pageContent_octopus(base_url=base_url, params=user_param, url_list=tobe_updated_url_list, taskName=taskName)
    except Exception as octopus_error:
        sql_request = "mysql+pymysql://{}:{}@{}:{}/{}?charset={}".format\
            (sql_config["user"], sql_config["passwd"], sql_config["host"]["host"], sql_config["host"]["port"], "peopledata", sql_config["charset"])
        myengine = sqlalchemy.create_engine(sql_request)
        table_name = "tobe_updated_leader_info"
        insp = sqlalchemy.inspect(myengine)
        tables = insp.get_table_names("peopledata")
        print("从mysql中读取待更新领导人信息")
        if table_name in tables:
            sql_content = pd.read_sql_table(table_name=table_name, con=myengine.connect())
            if sql_content.size==0:
                print("data in leader_info_to_beupdated from sql is empty")
                return
            else:
                leader_page_content = []
                sql_dict = sql_content.to_dict("split")
                for dd in sql_dict["data"]:
                    dd_dict = dict(zip(sql_dict["columns"], dd))
                    if len(dd_dict["SearchText"])==0:
                        continue
                    item_info = {"Title": dd_dict["Title"], "Url": dd_dict["Url"], "PageContent": dd_dict["PageContent"], "SearchText":dd_dict["SearchText"]}
                    if item_info not in leader_page_content:
                        leader_page_content.append(item_info)

       
        # leader_page_content = get_leader_pageContent_octopus_Outdata(base_url=base_url, payload_src=user_param, taskName="get_pageContent_from_urllist", taskID="dea53f30-4ed4-f60d-033c-e7dd1f67f445")
        return


    # 4、从八爪鱼结果里拿到cv字段，并写入leader_cv(name, url, cv, create_time)
    print("\n解析html源码:")
    url_tmp = []
    exist_url_list = [i_item["url"] for i_item in exist_leader_cv_info]
    with open("leader_cv.json", "w", encoding="utf8") as file:
        for leaderinfo in leader_page_content:
            if leaderinfo["Url"] not in url_tmp:
                url_tmp.append(leaderinfo["Url"])
            else:
                continue
            page = etree.HTML(leaderinfo["PageContent"])
            cv = parse_pagecontent(leaderinfo["Title"], page)
            pageTitle = leaderinfo["Title"]
            name = re.sub(r"\（.*\）_百度百科", "", pageTitle)
            name = re.sub(r"_百度百科", "", name)
            name = re.sub("百度百科", "", name)
            time.sleep(1)
            info_str = json.dumps({"name": name, "cv":cv, "url":leaderinfo["Url"]},ensure_ascii=False)+"\n"
            file.write(info_str)
            if write_to_ES and cv and name.find("全球领先的中文百科全书")==-1:
                doc = {
                    "name": name,
                    "url": leaderinfo["Url"],
                    "cv": cv,
                    "create_time": get_time(),
                    "update_time": get_time()
                }
                uuu = re.sub("https://baike.baidu.com", "", leaderinfo["Url"])
                if uuu in exist_url_list:
                    # 待更新的领导人cv已经在旧表中存在，需要删除旧信息，添加新的信息
                    _id = [i_item["_id"] for i_item in exist_leader_cv_info if i_item["url"].find(uuu)!=-1] 
                    if _id:
                        _id=_id[0]   
                        query_by_id={
                            "match": {
                                "_id": _id
                            }                            
                        }
                        old_leader_cv_hits = es.search(index=output_leadercv_index, query=query_by_id, timeout="10m")
                        old_leader_cv = old_leader_cv_hits["hits"]["hits"][0]["_source"]["cv"]
                        if old_leader_cv==cv:
                            continue
                        else:
                            doc["create_time"] = old_leader_cv_hits["hits"]["hits"][0]["_source"]["create_time"] 
                            cvinfo_log.info("update:{}\t{}".format(name, leaderinfo["Url"]))  
                        es.update(index=output_leadercv_index, id=_id, doc=doc, timeout="10m")     
                        print("-------------------------------------------------------")
                        print("【Update】name:[{}]    url:[{}]".format(name, leaderinfo["Url"]))
                        print(cv)
                        time.sleep(2)
                else:
                    print("-------------------------------------------------------")
                    print("【Insert】name:[{}]    url:[{}]".format(name, leaderinfo["Url"]))
                    print(cv)
                    time.sleep(2)
                    cvinfo_log.info("insert:{}\t{}".format(name, leaderinfo["Url"]))  
                    es.index(index=output_leadercv_index, document=doc, timeout="10m")      
                cvinfo_log.info(info_str)     
    file.close()
    print("----",len(leader_page_content))
    # leader-cv中已经存在的人员信息，在leaderinfo中判断，如果不存在，则删除该条纪律，这部分暂时没想好怎么写
    return


def main():
    # 从ES leader-info读取领导人链接，调用八爪鱼接口，获取page源码，解析简历信息，并入库（white2ES=True)
    get_leader_cv(input_leaderinfo_index="leader-info-final", output_leadercv_index="leader-cv")
    print("Write_to_ES:", write_to_ES)

# 在原index中添加了update_time字段之后，重新建表leader-cv-new, 别名是leader-cv
# 并把新的数据同步到leader-cv-new中
def update_cv_mapping():
    try:
        query = {
            "bool": {
                "must": []
            }
        }
        res = es.search(index="leader-cv-old", query=query, timeout="10m", size=10000)
        leaderinfo_list = res["hits"]["hits"]
    except Exception as disc_error:
        print(disc_error)
        return
    for leader in leaderinfo_list:
        _id = leader["_id"]
        create_time = leader["_source"]["create_time"]
        # update_time = leader["_source"]["update_time"]
        doc = {
            "name":leader["_source"]["name"],
            "cv":leader["_source"]["cv"],
            "url":leader["_source"]["url"],
            "create_time": create_time,
            "update_time": create_time
        }
        es.index(index="leader-cv-new", document=doc, timeout="10m")
        # es.update("leader_cv", id=_id, doc=doc, timeout="10m")


if __name__=="__main__":
    # schedule.every().day.at("09:30:00").do(main)
    # while True:
    #     schedule.run_pending()
    main()
    # query_by_id={
    #     "match": {
    #         "_id": "0Jb8KIgBmtvwZt3W0aYs"
    #     }       
    # }
    # old_leader_cv = es.search(index="leader-cv", query=query_by_id)
    # print("iii")     
