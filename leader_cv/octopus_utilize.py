# -*- utf-8 -*-
import requests
import time, json
import pandas as pd
import sys, sqlalchemy
sys.path.append("/home/<USER>/bazhuayu_data/")
#from paperdata_Spider.sql_utilits import create_table

# 1、注册首次登陆
def log_in(base_url, params):
    print("octopus step 1: log in")
    headers = {"content-type": "application/json"}
    token_entity = requests.request("POST", base_url+"token", json=params, headers=headers).json()
    if "access_token" not in token_entity["data"]:
        print("Octopus log in Error:", token_entity["error"]["message"])
        return
    return token_entity

class Octopus_app():
    
    def __init__(self, name=None, **kwargs):
        base_url = "https://openapi.bazhuayu.com/"
        self.base_url = "https://openapi.bazhuayu.com/"
        params = {
        "username": "zhangfzpeople",
        "password": "zhang!@#",
        "grant_type": "password"
        }
        headers = {"content-type": "application/json"}
        
        self.engine = sqlalchemy.create_engine("mysql+pymysql://root:<EMAIL>:8092/media_database?charset=utf8")
        self.token_entity = log_in(base_url="https://openapi.bazhuayu.com/", params=params)
        self.headers = {
            "Authorization": "Bearer "+self.token_entity["data"]["access_token"],
            "content-type": "application/json"}  #可以一直使用，除非跨天

    # 获取taskGroup信息， taskgroupid
    def get_taskgroup_info(self):
        print(" octopus step 2: get task group")
        token = self.token_entity["data"]["access_token"]
        url = self.base_url + "taskGroup"
        headers = {
            "Authorization": "Bearer "+token,
            "content-type": "application/json"}  #可以一直使用，除非跨天
        getTaskGroups = requests.request("GET", url, headers=headers).json()
        if "error" in getTaskGroups:
            print("Get TaskGroup Error:", getTaskGroups["error"]["message"])
            return
        else:
            taskGropus = getTaskGroups["data"]
        return taskGropus

    # 根据groupID获取任务信息
    def get_taskinfo(self, groupId):
        print("octopus step 3: get task ID")
        taskID = ""
        task_info = []
        requests_url = "task/search"
        tasks = requests.request(method="GET", url = "{}{}?taskGroupId={}".format(self.base_url, requests_url, groupId), headers=self.headers).json()
        if tasks:
            for task in tasks["data"]:
                task_info.append({"taskName": task["taskName"], "taskId": task["taskId"]})
        return task_info


    # 根据任务id修改urllist
    def update_task_urllist(self, taskId, url_list):
        print("octopus step: get action in task")
        payload = {"taskIds":[taskId], "actionTypes": ["LoopAction"]}
        getActions = requests.request("POST", self.base_url+"task/getActions", headers=self.headers, json=payload).json()
        if "error" in getActions:
            print("Get Actions from taskId Error:", getActions["error"]["message"])
            return False
        actions = getActions["data"]
        actionId = ""
        for action in actions:
            action = action["actions"]
            for sub_act in action:
                if sub_act["actionType"]=="LoopType" and sub_act["name"]=="循环网址":
                    actionId = sub_act["actionId"]
        print("\tactionID=", actionId)
        # 更新任务循环步骤的内容 （taskName= get_pageContent_from_urllist)d
        print("\toctopus step: update task action")
        loopItems = url_list.copy()

        payload = {"taskId": taskId, "actionId": actionId, "loopType": "UrlList", "loopItems": loopItems, "isAppend": False}
        updataLoopItems = requests.request("POST", self.base_url+"task/updateLoopItems", headers=self.headers, json=payload).json()
        if "error" in updataLoopItems:
            print("UpdateLoopItems Error:", updataLoopItems["error"]["message"])
            return False
        return True

    # 根据任务id情况任务数据
    def remove_taskdata(self, taskId):
        print("\toctopus step 6: remove taskdate")
        payload = {"taskId": taskId}
        removedate = requests.request("POST", self.base_url+"data/remove", json=payload, headers=self.headers).json()
        if "error" in removedate:
            print(("Removedate Error:", removedate["error"]["message"]))
            return
        
    # 启动云服务
    def start_task(self, taskId):
        payload = {"taskId": taskId}
        startTask = requests.request("POST", self.base_url+"cloudextraction/start", json=payload, headers=self.headers).json()
        if "error" in startTask:
            print(startTask["error"]["message"])
            return False
        else:
            return True
    # 获取任务数据    
    def get_taskdata(self, taskId, offset=0, size=1, waittime=60):
        print("output data:")
        pageContent_ls=[]
        taskstatus = True
        num = 1
        while taskstatus:
            time.sleep(waittime)
            # print(".....", num)
            # num = num+1
            status_string = requests.request("POST", self.base_url+"cloudextraction/statuses", json={"taskIds":[taskId]}, headers=self.headers).json()
            if status_string:
                for status in status_string["data"]:
                    if status["taskId"]==taskId and (status["status"].find("Finished")!=-1 or status["status"].find("Stopped")!=-1):
                        taskstatus=False
                        taskdata  = []
                        data_tmp = requests.request("GET", self.base_url+"data/notexported?taskId={}&size={}".format(taskId, 1), headers=self.headers).json()
                        if "error" in data_tmp:
                            print("Export data Error:", data_tmp["error"]["message"])
                            return
                        else:
                            total_item = data_tmp["data"]["total"]
                            print("bazhuayu total date:", total_item)
                            restTotal = total_item
                            # 从任务中导出数据
                            while restTotal:
                                payload = "?taskId={}&offset={}&size={}".format(taskId, offset, 1)
                                taskdata = requests.request("GET", self.base_url+"data/all"+payload, headers=self.headers).json()
                                if "error" in taskdata:
                                    print("指定位置获取数据Error: offset=", offset)
                                    continue
                                else:
                                    offset = taskdata["data"]["offset"]
                                    restTotal = taskdata["data"]["restTotal"]
                                    if restTotal % 10==0:
                                        print("         ", restTotal)
                                    if taskdata["data"]["data"]:
                                        for tdata in taskdata["data"]["data"]:
                                            if tdata not in pageContent_ls:
                                                pageContent_ls.append(tdata)
                                    else:
                                        time.sleep(600)
                                        taskdata = requests.request("GET", self.base_url+"data/all"+payload, headers=self.headers).json()
                                        offset = taskdata["data"]["offset"]
                                        restTotal = taskdata["data"]["restTotal"]
                                        if "data" in taskdata["data"].keys():
                                            print(taskdata["data"])
                                            for tdata in taskdata["data"]["data"]:
                                                if tdata not in pageContent_ls:
                                                    pageContent_ls.append(tdata)

        return pageContent_ls
    
    # 根据urllist修改url列表，启动，并得到运行结果
    def run_app(self, taskId, urllist, tableName, output=False):   # output用来在测试时不输出到数据表
        update_status = self.update_task_urllist(taskId=taskId, url_list=urllist)
        print("\tupdate finished")
        if update_status==True:
            start_status = self.start_task(taskId=taskId)
            if start_status:
                task_data = self.get_taskdata(taskId=taskId, size=50, waittime=30)
                print(task_data)
        if task_data and output:        
            self.output_data2sql(self.engine, task_data, tablename=tableName)
    
    # 导出任务数据到指定的数据表
    def output_data2sql(self, engine="", data_ls=[], tablename=""):
        data = []
        for dd in data_ls:
            news_dict = [dd["board"], dd["date"], dd["title"], dd["subtitle"], dd["content"], dd["url"]]
            print(dd["board"], dd["date"], dd["title"], dd["subtitle"], dd["url"])
            ddd = pd.DataFrame([news_dict], columns=["board", "date", "title", "subtitle", "content", "url"])
            ddd.to_sql(name=tablename, con=engine, if_exists="append", index=False)
            data.append(news_dict)


def get_leader_pageContent_octopus(base_url, params, url_list=[], taskName=""):
    pageContent_ls = []
    if taskName=="":
        print("TaskName is empty!")
        return

    # 1、注册首次登陆
    print(" octopus step 1: log in")
    headers = {"content-type": "application/json"}
    token_entity = requests.request("POST", base_url+"token", json=params, headers=headers).json()
    if "access_token" not in token_entity["data"]:
        print("Octopus log in Error:", token_entity["error"]["message"])
        return
    
    # # 2、获取TaskGroup信息， taskID……
    # print(" octopus step 2: get task group")
    token = token_entity["data"]["access_token"]
    url = base_url + "taskGroup"
    headers = {
        "Authorization": "Bearer "+token,
        "content-type": "application/json"}  #可以一直使用，除非跨天
    """
    获取任务id，默认情况调用的是get_pageContent_from_urllist
    # getTaskGroups = requests.request("GET", url, headers=headers).json()
    # if "error" in getTaskGroups:
    #     print("Get TaskGroup Error:", getTaskGroups["error"]["message"])
    #     return
    # else:
    #     taskGropus = getTaskGroups["data"]
    
    # # # 3、获取任务id taskId
    # print(" octopus step 3: get task ID")
    # taskID = ""
    # task_info = []
    # for group in taskGropus:
    #     time.sleep(1)
    #     groupId = group["taskGroupId"]
    #     payload = {"taskGroupId": groupId}
    #     requests_url = "task/search"
    #     tasks = requests.request(method="GET", url = "{}{}?taskGroupId={}".format(base_url, requests_url, groupId), headers=headers).json()
    #     if tasks:
    #         for task in tasks["data"]:
    #             if task["taskName"].find(taskName)!=-1:
    #                 taskID = task["taskId"]
    #     else:
    #         continue
    """
    time.sleep(3)

    taskID = "dea53f30-4ed4-f60d-033c-e7dd1f67f445"
    loopItems = url_list.copy()

    # 4、获取任务步骤信息
    print(" octopus step 4: get action in task")
    payload = {"taskIds":[taskID], "actionTypes": ["LoopAction"]}
    getActions = requests.request("POST", base_url+"task/getActions", headers=headers, json=payload).json()
    if "error" in getActions:
        print("Get Actions from taskId Error:", getActions["error"]["message"])
        return
    actions = getActions["data"]
    actionId = ""
    for action in actions:
        action = action["actions"]
        for sub_act in action:
            if sub_act["actionType"]=="LoopType" and sub_act["name"]=="循环网址":
                actionId = sub_act["actionId"]

    # 5、更新任务循环步骤的内容 （taskName= get_pageContent_from_urllist)
    print(" octopus step 5: update task action")
    loopItems = url_list.copy()

    payload = {"taskId": taskID, "actionId": actionId, "loopType": "UrlList", "loopItems": loopItems, "isAppend": False}
    updataLoopItems = requests.request("POST", base_url+"task/updateLoopItems", headers=headers, json=payload).json()
    if "error" in updataLoopItems:
        print("UpdateLoopItems Error:", updataLoopItems["error"]["message"])
        return
    
    # 6、启动云服务，循环检测云服务状态，并获取数据
    print(" octopus step: remove taskdate -> start task->get task status->get task data")
    payload = {"taskId": taskID}
    removedate = requests.request("POST", base_url+"data/remove", json=payload, headers=headers).json()
    if "error" in removedate:
        print(("Removedate Error:", removedate["error"]["message"]))
        return

    startTask = requests.request("POST", base_url+"cloudextraction/start", json=payload, headers=headers).json()
    taskstatus = True
    waittime = 1*len(loopItems)
    num = 1
    while taskstatus:
        time.sleep(waittime)
        print(".....", num)
        num = num+1
        status_string = requests.request("POST", base_url+"cloudextraction/statuses", json={"taskIds":[taskID]}, headers=headers).json()
        if status_string:
            for status in status_string["data"]:
                if status["taskId"]==taskID and (status["status"].find("Finished")!=-1 or status["status"].find("Stopped")!=-1):
                    taskstatus=False
                    taskdata  = []
                    data_tmp = requests.request("GET", base_url+"data/notexported?taskId={}&size={}".format(taskID, 1), headers=headers).json()
                    if "error" in data_tmp:
                        print("Export data Error:", data_tmp["error"]["message"])
                        return
                    else:
                        total_item = data_tmp["data"]["total"]
                        print("bazhuayu total date:", total_item)
                        restTotal = total_item
                        offset = 0
                        size= 10   # 根据八爪鱼要求，最多导出不超过8M
                        # 从任务中导出数据
                        print("    output data:")
                        while restTotal:
                            payload = "?taskId={}&offset={}&size={}".format(taskID, offset, 10)
                            querystring = {"taskId":taskID,"offset":str(offset),"size":"1"}
                            taskdata = requests.request("GET", base_url+"data/all", headers=headers, params=querystring)
                            taskdata = json.loads(taskdata.text)
                            if "error" in taskdata:
                                print("指定位置获取数据Error: offset=", offset)
                                continue
                            else:
                                offset = taskdata["data"]["offset"]
                                restTotal = taskdata["data"]["restTotal"]
                                if restTotal % 100==0:
                                    print("\t restTotal:", restTotal)
                                if taskdata["data"]["data"] is not None:
                                    print("data length:", len(taskdata["data"]["data"]), "剩余条数：", restTotal)
                                    for tdata in taskdata["data"]["data"]:
                                        if tdata not in pageContent_ls:
                                            pageContent_ls.append(tdata)
                        # 标记数据为已导出状态
                        # payload = {"taskId": taskID}
                        # markexported = requests.request("POST", base_url+"data/markexported", headers=headers, json=payload)
    return pageContent_ls



def get_leader_pageContent_octopus_Outdata(base_url, payload_src, taskName="", taskID=""):
    pageContent_ls = []
    if taskName=="":
        print("TaskName is empty!")
        return

    # 1、注册首次登陆
    print(" octopus step 1: log in")
    headers = {"content-type": "application/json"}
    token_entity = requests.post(base_url+"token", json=payload_src, headers=headers).json()
    # token_entity = requests.request("POST", base_url+"token", json=payload_src, headers=headers).json()
    if "access_token" not in token_entity["data"]:
        print("Octopus log in Error:", token_entity["error"]["message"])
        return
    token = token_entity["data"]["access_token"]
    headers = {
        "Authorization": "Bearer "+token,
        "content-type": "application/json"}  #可以一直使用，除非跨天
    
    if taskID=="":
        # 2、获取TaskGroup信息， taskID……
        print(" octopus step 2: get task group")
        url = base_url + "taskGroup"
        getTaskGroups = requests.request("GET", url, headers=headers).json()
        if "error" in getTaskGroups:
            print("Get TaskGroup Error:", getTaskGroups["error"]["message"])
            return
        else:
            taskGropus = getTaskGroups["data"]
        
        # 3、获取任务id taskId
        print(" octopus step 3: get task ID")
        taskID = ""
        task_info = []
        for group in taskGropus:
            time.sleep(1)
            groupId = group["taskGroupId"]
            payload = {"taskGroupId": groupId}
            requests_url = "task/search"
            tasks = requests.request(method="GET", url = "{}{}?taskGroupId={}".format(base_url, requests_url, groupId), headers=headers).json()
            if tasks:
                for task in tasks["data"]:
                    if task["taskName"].find(taskName)!=-1:
                        taskID = task["taskId"]
            else:
                continue
    time.sleep(3)
    # 从任务中直接导出数据
    print("     output data:")
    restTotal = 9388888
    offset = 0
    while restTotal:
        time.sleep(1)
        payload = "?taskId={}&offset={}&size={}".format(taskID, offset, 10)  # size = [1, 10]
        taskdata = requests.request("GET", base_url+"data/all"+payload, headers=headers).json()
        if "error" in taskdata:
            print("指定位置获取数据Error: offset=", offset)
            continue
        else:
            offset = taskdata["data"]["offset"]
            restTotal = taskdata["data"]["restTotal"]
            print("         ", restTotal)
            for tdata in taskdata["data"]["data"]:
                if tdata not in pageContent_ls:
                    pageContent_ls.append(tdata)
    print("pageContent—ls:\n", len(pageContent_ls))
    return pageContent_ls



if __name__=="__main__":
    octopus_app = Octopus_app()
    urllist = ["http://szb.gzrbs.com.cn/pc/layout/{}{}/{}/node_01.html".format(2023,"05",12)]
    octopus_app.remove_taskdata("ee046a1b-ae84-659c-f72a-603979a1c0d3")
    octopus_app.run_app(taskId="ee046a1b-ae84-659c-f72a-603979a1c0d3", urllist=urllist, tableName="GuiZhouDaily", output=True)