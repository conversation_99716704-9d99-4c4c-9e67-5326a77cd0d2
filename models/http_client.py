import json, os
from typing import Any
import requests


class HttpClient(object):
    def __init__(self, api) -> None:
        self.api = api

    def post(self, **kwargs):
        headers = {"Content-Type": "application/json"}
        r = requests.post(self.api, headers=headers, data=json.dumps(kwargs))
        return r

    def __call__(self, **kwargs):
        return self.post(**kwargs)


class RotNetClient(HttpClient):
    def __init__(self, api=''):
        if api == '':
            api = 'http://g12.guodata.com:9208/detect'
        super().__init__(api)

    def __call__(self, img_url):
        body = {
            'url': img_url
        }
        resp = self.post(**body)
        print(resp)
        print(resp.text)
        if resp.status_code == 200:
            res = resp.json()
            return res['resp']
        else:
            return -1


if __name__ == '__main__':
    import requests
    import json

    url = 'http://baike.baidu.com/item/张三里'
    headers = {"Content-Type": "application/json"}
    body = {'url': url}
    resp = requests.post('http://g12.guodata.com:9208/detect', headers=headers, data=json.dumps(body))
    print(resp.status_code)
    data = resp.json()
    print(data['url'])
    workdir = os.path.dirname(__file__)
    page_source = data["html"]
    # print(page_source.find("黑龙江省勃利县医疗保障局局长"))
    with open(os.path.join(workdir,'res1.html'), 'w') as f:
        f.write(data['html'])  # 这里的data["html"]才是以前的resp.text
