PARAMS:
  retry: 5
  timeout: 90
  num_workers: 20
  random_sleep_min: 1
  random_sleep_max: 2
  anti_crawl_func: baidu_anti
  log_level: info

REQUESTOR:
  # class_name: HttpRequestor
  # kwargs:
  #   api: http://*************:9208/detect
  class_name: Requestor
  kwargs:
    ~

# MODEL:
#   # class_name: RotNet
#   # kwargs: 
#   #   model_path: ckpt/rotnet_street_view_resnet50_keras2.hdf5
#   class_name: RotNetClient
#   kwargs: 
#     api: http://g12.guodata.com:9208/detect

# PROXY:
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0616-20.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0616-200.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0616-200-2.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0616-200-3.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0616-200-4.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0616-200-5.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0617-20.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0619-20.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0620-20.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0621-20.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0625-20.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0626-20.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0630-20.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0703-20.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0705-20.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0706-20.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0707-20.txt
#   - /home/<USER>/work/UpdateLeaderInfo/configs/proxy/getip0710-20.txt

# AGENT:
#   - "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/22.0.1207.1 Safari/537.1"
#   - "Mozilla/5.0 (X11; CrOS i686 2268.111.0) AppleWebKit/536.11 (KHTML, like Gecko) Chrome/20.0.1132.57 Safari/536.11"
#   - "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.6 (KHTML, like Gecko) Chrome/20.0.1092.0 Safari/536.6"
#   - "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.6 (KHTML, like Gecko) Chrome/20.0.1090.0 Safari/536.6"
#   - "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/********** Safari/537.1"
#   - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.9 Safari/536.5"
#   - "Mozilla/5.0 (Windows NT 6.0) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.36 Safari/536.5"
#   - "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3"
#   - "Mozilla/5.0 (Windows NT 5.1) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3"
#   - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_0) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3"
#   - "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1062.0 Safari/536.3"
#   - "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1062.0 Safari/536.3"
#   - "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3"
#   - "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3"
#   - "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3"
#   - "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.0 Safari/536.3"
#   - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.24 (KHTML, like Gecko) Chrome/19.0.1055.1 Safari/535.24"
#   - "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/535.24 (KHTML, like Gecko) Chrome/19.0.1055.1 Safari/535.24"
#   - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36"
