[loggers]
keys=root,my_logger

[handlers]
keys=console<PERSON><PERSON><PERSON>,fileHandler

[formatters]
keys=simpleFormatter

[logger_root]
level=DEBUG
handlers=consoleHandler

[logger_my_logger]
level=DEBUG
handlers=consoleHandler,fileHandler
qualname=my_logger
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=DEBUG
formatter=simpleFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=logging.handlers.TimedRotatingFileHandler
level=DEBUG
formatter=simpleFormatter
args=('%(LOG_FILE_PATH)s', 'midnight', 1, 7)  # 使用环境变量作为路径

[formatter_simpleFormatter]
format="%(asctime)s | %(levelname)s | %(funcName)s | %(lineno)s | %(message)s"
datefmt="%Y%m%d %H:%M:%S"

