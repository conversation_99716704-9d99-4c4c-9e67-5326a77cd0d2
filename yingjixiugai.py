# -*-utf-8
from elasticsearch import helpers, Elasticsearch
import os, json
# ----------------------------------------------------------
# leader-info 先确认检查是否有错，没错再写
write_to_ES = True   # False不写入ES，True写入ES

config_file = os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")), "configs/es_config.json")
with open(config_file, "r", encoding='utf-8') as fp:
    CONFIG_ES = json.load(fp)
__index_name__ = CONFIG_ES["leader_info_index"]    #'leader-info-final'  # index新表，增加了leader_type字段                           

es = Elasticsearch(CONFIG_ES['host'], basic_auth=(CONFIG_ES['user'], CONFIG_ES['passwd']), request_timeout=120, max_retries=10, retry_on_timeout=True)

