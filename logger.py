import os
import time
import logging


def get_logger(name='', filename=None, log_dir=None, level='info'):
    if name == '':
        name = 'logger'
        
    levels = {
        'info': logging.INFO,
        'debug': logging.DEBUG,
        'warning': logging.WARNING,
        'error': logging.ERROR,
    }
    level = levels[level]

    if log_dir is None:
        log_dir = os.path.join(os.path.dirname(__file__), 'logs')
    os.system(f'mkdir -p {log_dir}')
    # print(log_dir)

    if filename is None:
        t = time.strftime('%Y%m%d',time.localtime(time.time()))
        filename = os.path.join(log_dir, f'{t}.log')

    onlyname = os.path.split(os.path.splitext(filename)[0])[1]
    filename_err = os.path.join(log_dir, f'{onlyname}_warn.log')

    logger = logging.getLogger(name)
    logger.setLevel(level)

    if not logger.handlers:
        formatter = logging.Formatter('%(levelname)s|%(asctime)s|%(threadName)s|%(filename)s:%(lineno)d】%(message)s')
        fh = logging.FileHandler(filename)
        fh.setLevel(level)
        fh.setFormatter(formatter)
        logger.addHandler(fh)

        fh_err = logging.FileHandler(filename_err)
        fh_err.setLevel(logging.WARNING)
        fh_err.setFormatter(formatter)
        logger.addHandler(fh_err)

        # ch = logging.StreamHandler()
        # ch.setLevel(logging.WARNING)
        # ch.setFormatter(formatter)
        # logger.addHandler(ch)

    logger.info(f"log file: {filename}")
    logger.info(f"warn log file: {filename_err}")

    return logger

