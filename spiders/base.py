from email.utils import unquote
import os
import re
import time
import json
import random
from uu import decode
import requests
import yaml
import hashlib
import traceback
from urllib.parse import urlparse
from scrapy.http import HtmlResponse

from logger import get_logger
from anti_crawls import AntiCrawlError
import urllib.parse

AGENTS = [
    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/22.0.1207.1 Safari/537.1",
    "Mozilla/5.0 (X11; CrOS i686 2268.111.0) AppleWebKit/536.11 (KHTML, like Gecko) Chrome/20.0.1132.57 Safari/536.11",
    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.6 (KHTML, like Gecko) Chrome/20.0.1092.0 Safari/536.6",
    "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.6 (KHTML, like Gecko) Chrome/20.0.1090.0 Safari/536.6",
    "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/19.77.34.5 Safari/537.1",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.9 Safari/536.5",
    "Mozilla/5.0 (Windows NT 6.0) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.36 Safari/536.5",
    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3",
    "Mozilla/5.0 (Windows NT 5.1) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_0) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3",
    "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1062.0 Safari/536.3",
    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1062.0 Safari/536.3",
    "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3",
    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3",
    "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3",
    "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.0 Safari/536.3",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.24 (KHTML, like Gecko) Chrome/19.0.1055.1 Safari/535.24",
    "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/535.24 (KHTML, like Gecko) Chrome/19.0.1055.1 Safari/535.24",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
]


class Requestor(object):
    def __init__(self, ip_port=None, logger=None, snapshot_dir=''):
        if logger is None:
            logger = get_logger()
        self.logger = logger
        if isinstance(ip_port, str):
            self.ip_port = [ip_port]
        elif isinstance(ip_port, list):
            self.ip_port = ip_port
        else:
            self.ip_port = []
        self.snapshot_dir = snapshot_dir
        if self.snapshot_dir != '':
            os.system(f'mkdir -p {self.snapshot_dir}')
        # self.logger.info(f'Requestor init: {ip_port}')

    def save_snapshot(self, url, response):
        
        info = self.parse_url(url)
        if "baike.baidu.com" in url:
            url = re.sub("%2F", "/", url)
            pattern = r"(?:item/|word=)([^&]*)"
            matches = re.findall(pattern=pattern, string=url)
            if matches:
                word_value = urllib.parse.unquote(matches[0])
                word_value = re.sub(" ", "", word_value)
                word_value = re.sub("/", "_", word_value)
                info_id = "".join(re.compile(r'[\u4e00-\u9fff]+').findall(word_value))
            else:
                info_id = hashlib.md5(url.encode("utf-8")).hexdigest()
        elif "g12.guodata.com" in url:
            info_id = "organizations"
        else:
            info_id = hashlib.md5(url.encode("utf-8")).hexdigest()

        save_dir = info['netloc']+info['path']
        save_dir = os.path.join(self.snapshot_dir, save_dir)
        os.system(f'mkdir -p {save_dir}')
        save_name = os.path.join(save_dir, info_id)
        with open(os.path.join(self.snapshot_dir, 'file_list.txt'), 'a') as f:
            f.write(url+': '+save_name+'\n')
        with open(save_name, 'w') as f:
            f.write(response.text)
        with open(os.path.join(save_dir, f'{info_id}_info.json'), 'w') as f:
            json.dump(info, f, ensure_ascii=False, indent=2)

    def load_snapshot(self, url):
        # print("load_snapshot:", unquote(url))
        import urllib.parse
        info = self.parse_url(url)
        if "baike.baidu.com" in url:
            pattern = r"(?:item/|word=)([^&]*)"
            matches = re.findall(pattern=pattern, string=url)
            if matches:
                word_value = urllib.parse.unquote(matches[0])
                word_value = re.sub(" ", "", word_value)
                word_value = re.sub("/", "_", word_value)
                info_id = "".join(re.compile(r'[\u4e00-\u9fff]+').findall(word_value))
            else:
                info_id = hashlib.md5(url.encode("utf-8")).hexdigest()
        elif "g12.guodata.com" in url:
            info_id = "organizations"
        else:
            info_id = hashlib.md5(url.encode("utf-8")).hexdigest()
        # print("load_snapshot : ", info_id)
        
        snapshot_dir = info['netloc']+info['path']
        snapshot_dir = os.path.join(self.snapshot_dir, snapshot_dir)
        snapshot_name = os.path.join(snapshot_dir, info_id)
        with open(snapshot_name) as f:
            html = f.read()
            
        response = HtmlResponse(url=url, body=html, encoding='utf8')
        if len(html.strip()) == 0:
            response.status_code = 400
            # print("empty")
        else:
            response.status_code = response.status
        return response
    
    def get_title(self, html_data, strict=False):
        p = re.compile("<title>([\s\S]*?)</title>")
        m = re.findall(p, html_data)
        if len(m) > 0:
            return re.sub('[\s]+', '', m[0])
        else:
            if strict:
                return ''
            else:
                return html_data
        
    def _get(self, url, timeout): ####
        headers = {
            "User-Agent": random.choice(AGENTS),
            "connection": "close"
        }
        if len(self.ip_port) > 0:
            ip_port = random.choice(self.ip_port)
            proxy_meta = "http://{}".format(ip_port)# "http://sgkgre:2bc4rtcw@{}".format(ip_port) #sgkgre:2bc4rtcw@
            proxy = {
                'http': proxy_meta, #_proxy
                'https': proxy_meta
            }
        else:
            proxy = None
        response = requests.get(url=url.strip(), headers=headers, proxies=proxy, timeout=timeout)
        return response

    def get(self, url, timeout=8):
        self.logger.info(f"[GET] {url}")
        try:
            
            response = self.load_snapshot(url)
            if response.status_code!=200:
                response = self._get(url, timeout)
                from_snapshot = True   
            else:     
                from_snapshot = True
            # print("load_snapshot:", url)
        except FileNotFoundError:
            response = self._get(url, timeout)
            from_snapshot = False

        self.logger.info(f'[RSP] {response.status_code} {self.get_title(response.text, strict=True)}')
        log_str = {
            'proxy': self.ip_port,
            'url': url,
            'resp_code': response.status_code
        }
        # print(log_str)
        log_str = json.dumps(log_str, ensure_ascii=False, indent=2)
        if response.status_code != 200:
            self.logger.warning('\n'+log_str)

        if self.snapshot_dir != '' and not from_snapshot:
            self.save_snapshot(url, response)
        return response
    
    def parse_url(self, url):
        parsed_url = urlparse(url)
        res = {
            'scheme': parsed_url.scheme,
            'netloc': parsed_url.netloc,
            'path': parsed_url.path,
            'params': parsed_url.params,
            'query': parsed_url.query,
            'fragment': parsed_url.fragment,
        }
        return res
    
    def run(self, urls: list, callbacks=[], timeout=None):
        start_time = time.time()
        data = [{'index': i, 'url': url} for i, url in enumerate(urls)]
        random.shuffle(data)
        self.logger.info(f"开始爬取url列表，共{len(urls)}个")
        print(f"开始爬取url列表，共{len(urls)}个")

        res = list()
        for item in data:
            resp = self.get(item['url'], timeout=timeout)
            item['resp'] = resp
            item['code'] = 0 if resp.status_code == 200 else -1
            # print(item)
            res.append(item)
        res = sorted(res, key=lambda x: x['index'])

        tmp_res = list()
        for item in res:
            for func in callbacks:
                if item['code'] == 0:
                    try:
                        item['resp'] = func(item['resp'])
                    except:
                        self.logger.error(traceback.format_exc())
                        item['code'] = -1
            tmp_res.append(item)
        res = tmp_res

        end_time = time.time()
        self.logger.debug(start_time)
        self.logger.debug(end_time)
        self.logger.info(f'爬取任务结束，耗时: {end_time-start_time:.3f}s')
        print(f'爬取任务结束，耗时: {end_time-start_time:.3f}s')

        return res
    

class Spider(object):
    def __init__(self, config_file) -> None:
        with open(config_file) as f:
            self.cfg = yaml.safe_load(f)
        self.logger = get_logger()

    def _get(self, url):
        proxy = random.choice(self.cfg['PROXY'])
        req = Requestor(proxy, self.cfg['AGENT'])
        for i in range(self.cfg['PARAMS']['retry']):
            try:
                resp = req(url, timeout=self.cfg['PARAMS']['timeout'])
                return resp
            except:
                time.sleep(self.cfg['PARAMS']['sleep'])
                self.logger.warning(f'retry - {i+1}')
        self.logger.error('request failed')
        raise Exception('request error')
    
    def get(self, url):
        return self._get(url)


