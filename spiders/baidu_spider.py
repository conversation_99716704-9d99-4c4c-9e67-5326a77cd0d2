import time
import random
from scrapy.http import HtmlResponse

from pyppeteer import launch
import asyncio

from models import RotNet, RotNetClient, HttpClient
from .base import Requestor, AGENTS


async def try_validation(page, distance=308):
    # 将距离拆分成两段，模拟正常人的行为
    distance1 = distance - 10
    distance2 = 10
    btn_position = await page.evaluate('''
       () =>{
        return {
         x: document.querySelector('.vcode-spin-button').getBoundingClientRect().x,
         y: document.querySelector('.vcode-spin-button').getBoundingClientRect().y,
         width: document.querySelector('.vcode-spin-button').getBoundingClientRect().width,
         height: document.querySelector('.vcode-spin-button').getBoundingClientRect().height
         }}
        ''')
    x = btn_position['x'] + btn_position['width'] / 2
    y = btn_position['y'] + btn_position['height'] / 2
    # print(btn_position)
    await page.mouse.move(x, y)
    await page.mouse.down()
    await page.mouse.move(x + distance1, y, {'steps': 30})
    await page.waitFor(800)
    await page.mouse.move(x + distance1 + distance2, y, {'steps': 20})
    await page.waitFor(800)
    await page.mouse.up()


async def page_evaluate(page):
    await page.evaluate(
        '''() =>{ Object.defineProperties(navigator,{ webdriver:{ get: () => false } });window.screen.width=1366; }''')
    await page.evaluate('''() =>{ window.navigator.chrome = { runtime: {}, };}''')
    await page.evaluate('''() =>{ Object.defineProperty(navigator, 'languages', { get: () => ['en-US', 'en'] }); }''')
    await page.evaluate('''() =>{ Object.defineProperty(navigator, 'plugins', { get: () => [1, 2, 3, 4, 5,6], }); }''')


class BaiduRequestor(Requestor):
    def __init__(self, ip_port, class_name, logger=None, snapshot_dir='', **model_kwargs):
        super().__init__(ip_port, logger, snapshot_dir)
        self.model = eval(class_name)(**model_kwargs)

    def _get(self, url, timeout):
        # event = asyncio.new_event_loop()
        # asyncio.set_event_loop(event)
        # loop = asyncio.get_event_loop()
        # response = loop.run_until_complete(self.async_get(url, timeout))
        # loop.close()
        response = asyncio.get_event_loop().run_until_complete(self.async_get(url, timeout))
        return response

    async def async_get(self, url, timeout=None):
        width=1366
        height=768

        ip_port = random.choice(self.ip_port)

        browser = await launch({'headless': True,#可以无头
                            'slowMo':1.3,
                            'userDataDir': './userdata',
                            # "executablePath" : "/home/<USER>/software/chromedirver",
                            'args': [
                                f'--window-size={width},{height}'
                                '--disable-extensions',
                                '--hide-scrollbars',
                                '--disable-bundled-ppapi-flash',
                                '--mute-audio',
                                '--no-sandbox',
                                '--disable-setuid-sandbox',
                                '--disable-gpu',
                                '--disable-infobars',
                                f'--proxy_server={"http://"+ip_port}'
                            ],
                            'dumpio': False
                            })
        browser.close()
        page = await browser.newPage()
        # 设置浏览器头部
        # await page.setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.97 Safari/537.36")
        await page.setUserAgent(random.choice(AGENTS))

        # 设置浏览器大小
        await page.setViewport({'width': width, 'height': height})
        # 注入js，防反爬
        await page_evaluate(page)
        res=await page.goto(url=url)

        await page.waitFor(2)

        st = time.time()
        # 判断是否验证
        rotImg = await page.querySelector('.vcode-spin-img')
        # print(f"rotImg: {rotImg}")
        # 如果有验证码就去旋转
        times = 1
        while rotImg:
            if timeout is not None and time.time()-st > timeout:
                raise TimeoutError
            img_url=await (await(rotImg).getProperty("src")).jsonValue()
            # save_name = "".join(re.compile("id=(\d.*)-").findall(img_url))+".jpg"
            # response = requests.get(img_url, proxies=proxy)
            # image_content = response.content
            # image = Image.open(io.BytesIO(image_content))
            # image.save(os.path.join("data/baike/src", save_name))
            print(img_url)
            angle = self.model(img_url)
            # await asyncio.sleep(1)
            spin_bottom = await page.querySelector(".vcode-spin-bottom")
            spin_button = await page.querySelector(".vcode-spin-button")
            # print(spin_bottom)
            # print(spin_button)
            if spin_bottom and spin_button:
                bottom_line=await (await(spin_bottom).getProperty("offsetWidth")).jsonValue()
                button_line = await (await(spin_button).getProperty("offsetWidth")).jsonValue()
            else:
                continue
            b=bottom_line-button_line
            move_line = angle/360*b
            await try_validation(page,move_line)
            print(f"尝试验证第{times}次")
            self.logger.info(f"尝试验证第{times}次")
            times += 1
            # 停个3秒
            await asyncio.sleep(1)
            rotImg = await page.querySelector('.vcode-spin-img')
            if not rotImg:
                html = await page.content()
                # tree = etree.HTML(html)
                # title_pat = re.compile("<title>.*?</title>")
                # title = "".join(title_pat.findall(html))
                print(page.url, "验证成功")
                self.logger.info(f"{page.url} 验证成功")

        html = await page.content()
        # print("title={}".format(self.get_title(html)))
        self.logger.info("title={}".format(self.get_title(html)))

        response = HtmlResponse(url=url, body=html, encoding='utf8')
        response.status_code = response.status

        return response


class HttpRequestor(Requestor):
    def __init__(self, api, ip_port, logger=None, snapshot_dir=''):
        super().__init__(ip_port, logger, snapshot_dir)
        self.req = HttpClient(api)

    def _get(self, url, *args, **kwargs):
        post_data = {'url': url}
        resp = self.req(**post_data)
        code = resp.status_code
        resp = resp.json()
        response = HtmlResponse(url=resp['url'], body=resp['html'], encoding='utf8')
        response.status_code = resp['code']
        return response

