from genericpath import isfile
import os
import json
import yaml
import time
import random
import requests
from threading import Thread
from queue import Queue, Empty
import traceback
import urllib
import re

from .base import Requestor
from .baidu_spider import BaiduRequestor, HttpRequestor
from logger import get_logger
from utils import get_balance, get_ips
from models import *
from anti_crawls import *

def extract_zhongjian_content(url):
    # 检查第一种模式
    match1 = re.search(r'item/(.*?)/\d+', url)
    if match1:
        return match1.group(1)  # 提取/item/与/之间的字符
    
    # 检查第二种模式
    match2 = re.search(r'item/(.*)', url)
    if match2:
        return match2.group(1)  # 提取/item/之后的字符
    
    return None  # 如果不匹配，返回None


class MPSpider(object):
    def __init__(self, config_file, logger=None, snapshot_dir='') -> None:
        if logger is None:
            logger = get_logger()
        self.logger = logger
        self.logger.info(f'building MPSpider: {config_file}')
        self.snapshot_dir = snapshot_dir
        
        self.input_queue = Queue()
        self.output_queue = Queue()
        self.working = False
        self.workers = list()
        self.monitor = None
        self.history = list()
        self.task_start_time = -1
        self.task_timeout = -1

        with open(config_file) as f:
            self.cfg = yaml.safe_load(f)
        # print(json.dumps(self.cfg, ensure_ascii=False, indent=2))
        self.num_workers = self.cfg['PARAMS']['num_workers']
        assert self.num_workers > 0

        self.is_anti_crawled = None
        if 'anti_crawl_func' in self.cfg['PARAMS'] and self.cfg['PARAMS']['anti_crawl_func'] is not None and self.cfg['PARAMS']['anti_crawl_func'] != '':
            self.is_anti_crawled = eval(self.cfg['PARAMS']['anti_crawl_func'])
        self.last_print = []

    def __del__(self):
        try:
            self.logger.info('destruct MPSpider')
            self.show_history()
            self.logger.info('*'*40)
            for line in self.last_print:
                self.logger.info(line)
            self.logger.info('*'*40)
            # self.logger.info(f"当前账号余额: {get_balance():.2f}")
        except:
            pass

    def monitor_task(self, num_workers):
        self.logger.info(f"start monitor task\n num workers: {num_workers}")
        while self.working:
            self.logger.info("-")
            # 检查整个任务是不是死了
            curr_time = time.time()
            if self.task_timeout != -1 and (curr_time-self.task_start_time) > self.task_timeout:
                self.logger.error(f"任务超时：from [{self.to_timestamp(self.task_start_time)}] to [{self.to_timestamp(curr_time)}], timeout: {self.task_timeout}s")
                self.working = False
                break

            self.clean_workers()
            if len(self.workers) < num_workers:
                n = num_workers-len(self.workers)
                # self.add_workers(n)
                self.add_workers_with_proxy(n)
            time.sleep(1)

    def add_workers_with_proxy(self, num_workers):
        self.logger.info(f"add_worders: {num_workers}")
        # print("add_workers_with_proxy")
        ips = get_ips(num_workers)
        self.logger.info(f"got ips: {ips}")
        for ip in ips:
            worker = Thread(target=self.task, name=ip, kwargs={'ip_port': ip})
            worker.daemon = True
            worker.start()
            self.workers.append(worker)
            time.sleep(0.1)
            self.logger.info(f"worker [{ip}] started.")

    def add_workers(self, num_workers):
        self.logger.info(f"add_workers: {num_workers}")
        for _ in range(num_workers):
            worker = Thread(target=self.task, kwargs={'ip_port': None})
            worker.daemon = True
            worker.start()
            self.workers.append(worker)
            time.sleep(0.1)
            self.logger.info('worker started')

    def clean_workers(self):
        self.logger.info("clean worders")
        new_workers = list()
        for w in self.workers:
            if w.is_alive():
                new_workers.append(w)
            else:
                w.join(timeout=0.1)
        self.workers = new_workers
        self.logger.info(f"last worders: {len(self.workers)}")

    def start(self, num_queries, num_workers=0):
        self.stop()

        self.working = True
        self.logger.info('start task')

        self.task_start_time = time.time()
        self.task_timeout = min(self.cfg['PARAMS']['timeout']*self.cfg['PARAMS']['retry']*num_queries, 10800)
        self.logger.info(f'设置任务超时时间: {self.task_timeout}s')
        self.logger.info(f'任务最晚将于[{self.to_timestamp(self.task_start_time+self.task_timeout)}]结束')

        if num_workers == 0:
            num_workers = self.num_workers
        num_workers = min(num_workers, self.num_workers)

        self.monitor = Thread(target=self.monitor_task, name='Monitor', kwargs={'num_workers': num_workers})
        self.monitor.daemon = True
        self.monitor.start()

        # self.add_workers(num_workers)

    def stop(self):
        self.working = False
        self.logger.info('stop task')
        while len(self.workers) > 0:
            worker = self.workers.pop()
            worker.join(timeout=5)
        if self.monitor is not None:
            self.monitor.join(timeout=5)
        self.monitor = None
        self.task_timeout = -1

    def _load_proxy(self):
        ips = list()
        for item in self.cfg['PROXY']:
            if os.path.isfile(item):
                with open(item) as f:
                    lines = f.read().splitlines()
                    lines = [line.strip() for line in lines if line.strip() != '' or line.strip()[0] != '#']
                self.logger.debug(item)
                self.logger.debug('\n'.join(lines))
                ips += lines
            else:
                ips.append(item)
        return ips
    
    def sleep(self):
        sleep_time = random.randint(self.cfg['PARAMS']['random_sleep_min'], self.cfg['PARAMS']['random_sleep_max'])
        self.logger.debug(f'sleep {sleep_time}s')
        time.sleep(sleep_time)
        self.logger.debug('awake')
    
    def task(self, ip_port):
        self.logger.debug(f'start agent: {ip_port}')
        kwargs = self.cfg['REQUESTOR']['kwargs']
        if kwargs is None:
            kwargs = {}
        # agent = Requestor(ip_port=ip_port, logger=self.logger, snapshot_dir=self.snapshot_dir)
        # agent = BaiduRequestor(ip_port=ip_port, class_name=self.cfg['MODEL']['class_name'], logger=self.logger, snapshot_dir=self.snapshot_dir, **self.cfg['MODEL']['kwargs'])
        agent = eval(self.cfg['REQUESTOR']['class_name'])(ip_port=ip_port, logger=self.logger, snapshot_dir=self.snapshot_dir, **kwargs)
        try_times = 0
        url = ""
        while self.working:
            try:
                assert try_times < self.cfg['PARAMS']['retry'], f"从[{url}]获取数据失败，已重试{try_times}次"
                self.sleep()
                url = self.input_queue.get(timeout=1)
                url = urllib.parse.unquote(url)
                self.logger.debug(f'获取到url: {url}')
                # print('spider:', url, agent)
                resp = agent.get(url)
                # print('spider:', resp.status_code, resp.text)
                if self.is_anti_crawled is not None and self.is_anti_crawled(resp.text):
                    raise AntiCrawlError
                if resp.status_code != 200:
                    raise ValueError(resp.text)

                self.output_queue.put({
                    'proxy': ip_port,
                    'url': url,
                    'code': 0,
                    'resp': resp
                })
                try_times = 0
            except TimeoutError:
                try_times += 1
                self.logger.warning(f"尝试获取[{url}失败！第{try_times}次]")
                if not self.working:
                    self.logger.warning(f"终止任务[{url}]")
                    self.output_queue.put({
                        'proxy': ip_port,
                        'url': url,
                        'code': 9,
                        'resp': "terminated task"
                    })
            except AntiCrawlError:
                self.logger.warning(f"被反爬[{ip_port}]: {url}")
                self.output_queue.put({
                    'proxy': ip_port,
                    'url': url,
                    'code': 8,
                    'resp': resp
                })
                try_times = 0
                self.last_print.append(f"[{ip_port}][{url}]: 被反爬")
                break
            except Empty:
                pass
            except ValueError as e:
                self.output_queue.put({
                    'proxy': ip_port,
                    'url': url,
                    'code': -1,
                    'resp': str(e)
                })
                try_times = 0
                self.last_print.append(f"[{ip_port}][{url}]: {str(e)}")
                break
            except:
                self.logger.warning(traceback.format_exc())
                self.output_queue.put({
                    'proxy': ip_port,
                    'url': url,
                    'code': -1,
                    'resp': traceback.format_exc()
                })
                try_times = 0
                self.last_print.append(f"[{ip_port}][{url}]: {traceback.format_exc()}")
                break
        self.logger.debug(f'stop agent: {ip_port}')

    def run(self, urls: list, callbacks=[]):
        from urllib.parse import unquote
        from scrapy.http import HtmlResponse
        from urllib.parse import urlparse
        load_snapshot_results = []
        caiji_urls = []
        for url in urls:
            uncode_url = unquote(url)
            parsed_url_res = urlparse(uncode_url)
            urlres = {
                'scheme': parsed_url_res.scheme,
                'netloc': parsed_url_res.netloc,
                'path': parsed_url_res.path,
                'params': parsed_url_res.params,
                'query': parsed_url_res.query,
                'fragment': parsed_url_res.fragment
            }
            if url.endswith("/"):
                continue
            if urlres["query"]:
                hanzi_list = re.findall(r'[\u4e00-\u9fa5]+', urlres["query"])  #满足这个条件的是机构
                info_id = ''.join(hanzi_list)  # 将提取的汉字合并成一个字符串
            else:
                # hanzi_list = re.findall(r'[\u4e00-\u9fa5]+', urlres["path"])
                # info_id = ''.join(hanzi_list)  # 将提取的汉字合并成一个字符串
                try:
                    info_id = unquote(extract_zhongjian_content(url))
                except Exception as e:
                    print(e)
                    print(url)
                info_id = re.sub(" ", "", info_id)
                
            if "g12" in url and not info_id:
                info_id = "organizations"
            snapshot_dir = urlres['netloc']+urlres['path']
            snapshot_dir = os.path.join(self.snapshot_dir, snapshot_dir)
            snapshot_name = os.path.join(snapshot_dir, info_id)

            if os.path.exists(snapshot_name) and not snapshot_name.endswith("baidu.com/"):
                with open(snapshot_name, "r", encoding = "utf-8") as fp:
                    html = fp.read()
                    if len(html)==0:
                        caiji_urls.append(url)
                        continue
                response = HtmlResponse(url=url, body=html, encoding='utf8')
                response.status_code = response.status
                if callbacks:
                    tmp_res = []
                    for func in callbacks:
                        func_result= dict()
                        if response.status_code==200:
                            try:
                                func_result["resp"] = func(response)
                                func_result["url"] = response.url
                                func_result["proxy"] = ""
                                func_result["code"] = response.status_code
                            except Exception as e:
                                self.logger.error("load_snopshot:{}".format(traceback.format_exc()))
                                print(e)
                                func_result["resp"] = func(response)
                                func_result["url"] = response.url
                                func_result["proxy"] = ""
                                func_result["code"] = -1
                            tmp_res.append(func_result)
                        else:
                            self.logger.error("load_snopshot status_code= {}".format(response.status_code))
                    if len(tmp_res)==1:
                        tmp_res = tmp_res[0]
                    load_snapshot_results.append(tmp_res)
                else:
                    snapshot_info = {"proxy": "", "url": url, "code": 0, "resp": response}
                    load_snapshot_results.append(snapshot_info)
            else:
                if url not in caiji_urls:
                    caiji_urls.append(url)
        
        print("caiji_urls:{}".format(len(caiji_urls)))
        print("load_snapshot_results:{}".format(len(load_snapshot_results)))
        if not caiji_urls:      # 需要采集的url为空，全部load      
            if not callbacks:
                return load_snapshot_results
            else:
                tmp_res = list()
                for item in load_snapshot_results:
                    self.logger.info(str(item))
                    for func in callbacks:
                        self.logger.info(str(func.__name__))
                        # print(item)
                        if item["code"]==0:
                            try:
                                item['resp'] = func(item['resp'])
                            except:
                                self.logger.error(traceback.format_exc())
                                item['code'] = -1
                        tmp_res.append(item)              
                return tmp_res
        
        random.shuffle(caiji_urls)
        self.logger.info(f"开始爬取url列表，共{len(caiji_urls)}个")
        print(f"开始爬取url列表，共{len(caiji_urls)}个")
        self.start(len(urls), num_workers=len(urls))
        for url in caiji_urls:
            self.input_queue.put(url)
            time.sleep(random.randint(2,4))

        res = list()
        while True:
            if len(res) >= len(caiji_urls):
                break
            try:
                resp = self.output_queue.get(timeout=5)
                # print(resp)
                res.append(resp)
            except Empty:
                if not self.working:
                    raise Exception("任务提前结束")
        self.stop()

        tmp_res = list()
        for item in res:
            self.logger.info(str(item))
            for func in callbacks:
                self.logger.info(str(func))
                if item['code'] == 0:
                    try:
                        item['resp'] = func(item['resp'])
                    except:
                        self.logger.error(traceback.format_exc())
                        item['code'] = -1
            tmp_res.append(item)
        res = tmp_res

        end_time = time.time()
        self.logger.debug(self.task_start_time)
        self.logger.debug(end_time)
        self.logger.info(f'爬取任务结束，耗时: {end_time-self.task_start_time:.3f}s')
        print(f'爬取任务结束，耗时: {end_time-self.task_start_time:.3f}s')

        his = {
            'start_time': self.task_start_time,
            'end_time': end_time,
            'content': []
        }
        for item in res:
            his['content'].append({'url': item['url'], 'code': item['code']})
        self.history.append(his)

        self.logger.info("load_snapshot_results:{}".format(len(load_snapshot_results)))
        self.logger.info("输入的urls:{}".format(len(urls)))
        self.logger.info("采集urls:{}".format(len(caiji_urls)))
        self.logger.info("采集成功:{}".format(len(res)))
        if load_snapshot_results:
            res.extend(load_snapshot_results)
        return res
    
    def show_history(self):
        for i_his, his_data in enumerate(self.history):
            self.logger.warning(f"{i_his+1}. {self.to_timestamp(his_data['start_time'])} - {self.to_timestamp(his_data['end_time'])}")
            succ = list()
            fail = list()
            for item in his_data['content']:
                if item['code'] == 0:
                    succ.append(item['url'])
                else:
                    fail.append(item['url'])
            self.logger.warning(f"\t共：{len(his_data['content'])}  成功：{len(succ)}  失败：{len(fail)}  成功率：{100.*len(succ)/len(his_data['content']):.2f}%  耗时：{his_data['end_time']-his_data['start_time']:.3f}s")
            self.logger.info('\t成功列表')
            for i, x in enumerate(succ):
                self.logger.info(f'\t {i+1}. {x}')
            self.logger.warning('\t失败列表')
            for i, x in enumerate(fail):
                self.logger.warning(f'\t {i+1}. {x}')
            self.logger.warning('-'*40)
    
    @staticmethod
    def to_timestamp(t):
        return time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(t))
    

class MPSpiderGlobalAgent(object):
    def __init__(self, config_file, logger=None, snapshot_dir='') -> None:
        if logger is None:
            logger = get_logger()
        self.logger = logger
        self.logger.info(f'building MPSpider: {config_file}')
        self.snapshot_dir = snapshot_dir
        
        self.input_queue = Queue()
        self.output_queue = Queue()
        self.working = False
        self.workers = list()
        self.monitor = None
        self.history = list()
        self.task_start_time = -1
        self.task_timeout = -1

        with open(config_file) as f:
            self.cfg = yaml.safe_load(f)
        self.num_workers = self.cfg['PARAMS']['num_workers']
        assert self.num_workers > 0

        self.agents = []
        for _ in range(self.num_workers):
            self.agents.append(self.build_agent())

        self.is_anti_crawled = None
        if 'anti_crawl_func' in self.cfg['PARAMS'] and self.cfg['PARAMS']['anti_crawl_func'] is not None and self.cfg['PARAMS']['anti_crawl_func'] != '':
            self.is_anti_crawled = eval(self.cfg['PARAMS']['anti_crawl_func'])
        self.last_print = []

    def build_agent(self):
        model = eval(self.cfg['MODEL']['class_name'])(**self.cfg['MODEL']['kwargs'])
        return model

    def __del__(self):
        self.logger.info('destruct MPSpider')
        self.show_history()
        self.logger.info('*'*40)
        for line in self.last_print:
            self.logger.info(line)
        self.logger.info('*'*40)
        # self.logger.info(f"当前账号余额: {get_balance():.2f}")

    def monitor_task(self, num_workers):
        self.logger.info("start monitor task")
        while self.working:
            # 检查整个任务是不是死了
            curr_time = time.time()
            if self.task_timeout != -1 and (curr_time-self.task_start_time) > self.task_timeout:
                self.logger.error(f"任务超时：from [{self.to_timestamp(self.task_start_time)}] to [{self.to_timestamp(curr_time)}], timeout: {self.task_timeout}s")
                self.working = False
                break

            self.clean_workers()
            if len(self.workers) < num_workers:
                n = num_workers-len(self.workers)
                self.add_workers(n)

            time.sleep(5)

    def get_ips_legacy(self, n):
        return random.sample(self.ips, n)

    def add_workers(self, num_workers):
        ips = get_ips(num_workers)
        for i, ip in enumerate(ips):
            worker = Thread(target=self.task, name=ip, kwargs={'ip_port': ip, 'worker_index': i})
            worker.daemon = True
            worker.start()
            self.workers.append(worker)
            time.sleep(0.1)

    def clean_workers(self):
        new_workers = list()
        for w in self.workers:
            if w.is_alive():
                new_workers.append(w)
            else:
                w.join(timeout=0.1)
        self.workers = new_workers

    def start(self, num_queries, num_workers=0):
        self.stop()

        self.working = True
        self.logger.info('start task')

        self.task_start_time = time.time()
        self.task_timeout = min(self.cfg['PARAMS']['timeout']*self.cfg['PARAMS']['retry']*num_queries, 10800)
        self.logger.info(f'设置任务超时时间: {self.task_timeout}s')
        self.logger.info(f'任务最晚将于[{self.to_timestamp(self.task_start_time+self.task_timeout)}]结束')

        if num_workers == 0:
            num_workers = self.num_workers
        num_workers = min(num_workers, self.num_workers)

        self.monitor = Thread(target=self.monitor_task, name='Monitor', kwargs={'num_workers': num_workers})
        self.monitor.daemon = True
        self.monitor.start()

        # self.add_workers(num_workers)

    def stop(self):
        self.working = False
        self.logger.info('stop task')
        while len(self.workers) > 0:
            worker = self.workers.pop()
            worker.join(timeout=5)
        if self.monitor is not None:
            self.monitor.join(timeout=5)
        self.monitor = None
        self.task_timeout = -1

    def _load_proxy(self):
        ips = list()
        for item in self.cfg['PROXY']:
            if os.path.isfile(item):
                with open(item) as f:
                    lines = f.read().splitlines()
                    lines = [line.strip() for line in lines if line.strip() != '' or line.strip()[0] != '#']
                self.logger.debug(item)
                self.logger.debug('\n'.join(lines))
                ips += lines
            else:
                ips.append(item)
        return ips
    
    def sleep(self):
        sleep_time = random.randint(self.cfg['PARAMS']['random_sleep_min'], self.cfg['PARAMS']['random_sleep_max'])
        self.logger.debug(f'sleep {sleep_time}s')
        time.sleep(sleep_time)
        self.logger.debug('awake')
    
    def task(self, ip_port, worker_index):
        self.logger.debug(f'start agent: {ip_port}')
        # agent = Requestor(ip_port=ip_port, logger=self.logger, snapshot_dir=self.snapshot_dir)
        # agent = BaiduRequestor(ip_port=ip_port, model=self.agents[worker_index], logger=self.logger, snapshot_dir=self.snapshot_dir)
        agent = eval(self.cfg['REQUESTOR']['class_name'])(ip_port=ip_port, logger=self.logger, snapshot_dir=self.snapshot_dir, **self.cfg['REQUESTOR']['kwargs'])
        try_times = 0
        url = ""
        while self.working:
            try:
                assert try_times < self.cfg['PARAMS']['retry'], f"从[{url}]获取数据失败，已重试{try_times}次"
                self.sleep()
                url = self.input_queue.get(timeout=1)
                self.logger.debug(f'获取到url: {url}')
                resp = agent.get(url)
                self.logger.info(resp)
                if self.is_anti_crawled is not None and self.is_anti_crawled(resp.text):
                    raise AntiCrawlError

                self.output_queue.put({
                    'proxy': ip_port,
                    'url': url,
                    'code': 0,
                    'resp': resp
                })
                try_times = 0
            except TimeoutError:
                try_times += 1
                self.logger.warning(f"尝试获取[{url}失败！第{try_times}次]")
                if not self.working:
                    self.logger.warning(f"终止任务[{url}]")
                    self.output_queue.put({
                        'proxy': ip_port,
                        'url': url,
                        'code': 9,
                        'resp': "terminated task"
                    })
            except AntiCrawlError:
                self.logger.warning(f"被反爬[{ip_port}]: {url}")
                self.output_queue.put({
                    'proxy': ip_port,
                    'url': url,
                    'code': 8,
                    'resp': resp
                })
                try_times = 0
                self.last_print.append(f"[{ip_port}][{url}]: 被反爬")
                break
            except Empty:
                pass
            except:
                self.logger.warning(traceback.format_exc())
                self.output_queue.put({
                    'proxy': ip_port,
                    'url': url,
                    'code': -1,
                    'resp': traceback.format_exc()
                })
                try_times = 0
                self.last_print.append(f"[{ip_port}][{url}]: {traceback.format_exc()}")
                break
        self.logger.debug(f'stop agent: {ip_port}')

    def run(self, urls: list, callbacks=[]):
        random.shuffle(urls)
        self.logger.info(f"开始爬取url列表，共{len(urls)}个")
        print(f"开始爬取url列表，共{len(urls)}个")
        self.start(len(urls), num_workers=len(urls))
        for url in urls:
            self.input_queue.put(url)
            time.sleep(0.5)

        res = list()
        while True:
            if len(res) >= len(urls):
                break
            try:
                resp = self.output_queue.get(timeout=5)
                res.append(resp)
            except Empty:
                if not self.working:
                    raise Exception("任务提前结束")
        self.stop()

        tmp_res = list()
        for item in res:
            for func in callbacks:
                if item['code'] == 0:
                    try:
                        item['resp'] = func(item['resp'])
                    except:
                        self.logger.error(traceback.format_exc())
                        item['code'] = -1
            tmp_res.append(item)
        res = tmp_res

        end_time = time.time()
        self.logger.debug(self.task_start_time)
        self.logger.debug(end_time)
        self.logger.info(f'爬取任务结束，耗时: {end_time-self.task_start_time:.3f}s')
        print(f'爬取任务结束，耗时: {end_time-self.task_start_time:.3f}s')

        his = {
            'start_time': self.task_start_time,
            'end_time': end_time,
            'content': []
        }
        for item in res:
            his['content'].append({'url': item['url'], 'code': item['code']})
        self.history.append(his)

        return res
    
    def show_history(self):
        for i_his, his_data in enumerate(self.history):
            self.logger.warning(f"{i_his+1}. {self.to_timestamp(his_data['start_time'])} - {self.to_timestamp(his_data['end_time'])}")
            succ = list()
            fail = list()
            for item in his_data['content']:
                if item['code'] == 0:
                    succ.append(item['url'])
                else:
                    fail.append(item['url'])
            self.logger.warning(f"\t共：{len(his_data['content'])}  成功：{len(succ)}  失败：{len(fail)}  成功率：{100.*len(succ)/len(his_data['content']):.2f}%  耗时：{his_data['end_time']-his_data['start_time']:.3f}s")
            self.logger.info('\t成功列表')
            for i, x in enumerate(succ):
                self.logger.info(f'\t {i+1}. {x}')
            self.logger.warning('\t失败列表')
            for i, x in enumerate(fail):
                self.logger.warning(f'\t {i+1}. {x}')
            self.logger.warning('-'*40)
    
    @staticmethod
    def to_timestamp(t):
        return time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(t))


if __name__ == '__main__':
    spider = MPSpider('/home/<USER>/work/spider/configs/config.yaml')
    urls = [
        'https://baike.baidu.com/item/%E6%9D%8E%E5%85%8B%E5%BC%BA',
        'https://baike.baidu.com/item/%E6%9D%8E%E7%AB%8B%E4%B8%89',
        'https://baike.baidu.com/item/%E6%B1%9F%E6%B3%BD%E6%B0%91',
        'https://baike.baidu.com/item/%E9%82%93%E5%B0%8F%E5%B9%B3',
    ]
    spider_res = spider.run(urls)
    print(spider_res)
