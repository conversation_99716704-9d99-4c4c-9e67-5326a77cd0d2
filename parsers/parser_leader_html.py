import dis
from email import header
import html
from logging.config import fileConfig
from shutil import ExecError
from networkx import fiedler_vector
import re, requests, os, sys, json
from lxml import etree
import concurrent.futures
import logging, time
import datetime
from urllib.parse import unquote_plus
import requests
import traceback

today_str = datetime.date.today().strftime("%Y%m%d")
today_str = "20241118"

def join_text(text_list):
    text = ""
    for t in text_list:
        t = t.replace('\n','')
        if not t or t[0] == '[': # 剔除引用的方括号文本
            continue
        text += t
    pattern = re.compile('^\S{1}\s.*')
    if re.match(pattern, text):
        lis_text = list(text)
        lis_text.pop(1)
        text = "".join(lis_text)
    return text

class ParseLeaderPage(object):
    @staticmethod
    def get_summary(page_content):
        '''获取个人主页的个人综述'''
        summary = ""
        if page_content:
            page_tree = etree.HTML(page_content)
            xpath_str = ".//div[contains(@class, 'lemmaSummary')]//text()"
            summary = "".join(page_tree.xpath(xpath_str))
            if not summary:
                # print("use pattern")
                summarypattern = '<meta name="description" content=\"(.*?)\">'
                summary = re.findall(summarypattern, page_content)
                summary = "".join(summary)
        else:
            return ""
        summary = re.sub("\[.*?\]|\xa0|\n", "", summary)
        return summary
    @staticmethod
    def get_description(page_content):
        '''获取个人主页的个人描述'''
        description = ""
        if page_content:
            page_tree = etree.HTML(page_content)
            xpath_str = ".//div[contains(@class, 'lemmaDesc')]/text()"
            description = "".join(page_tree.xpath(xpath_str))
            if not description:
                # print("urs json data")
                descriptionpattern = 'PAGE_DATA= {.*?\"lemmaDesc\":\"(.*?)\",\"lemmaType\".*?'
                description = re.findall(descriptionpattern, page_content)
                description = "".join(description)
        else:
            return ""
        return description
    
    @staticmethod
    def get_cv(page_content):
        '''获取个人简历'''
        leader_cv = ""
        if page_content=="":
            return leader_cv
        page_tree = etree.HTML(page_content)
        create_new = page_tree.xpath("//div[@class='errorBox']")
        if create_new:
            return leader_cv

        start = False
        h2_title = ['个人履历', '人物履历', '个人简介', '人物简介','人物经历', '人物生平', '工作履历', '任免信息',
                    '工作经历', '担任职务', '个人经历', '个人简历', '简历', '人物生平', '任职经历', '履历', '职务任免', '职业经历',
                    '人物简历', '工作简历', '艺术履历', '从政经历', '个人任职', '主要简历']

        title_list = page_tree.xpath('//div [contains(@class, "para")]')
        for title in title_list:
            if start == False:
                title_text = title.xpath("./h2/text()")#title.xpath("./h2[contains(@class, 'title-text')]/text()")
                title_text = "".join(title_text)
                if title_text:
                    title_text = title_text.strip()
                if title_text in h2_title:
                    start = True
            else:
                if title.xpath('./h2'):
                    start = False
                    break
                else:
                    text_list = title.xpath(
                        "./text() | ./*[not(contains(@class, 'layout-right'))]//text()")
                    text = join_text(text_list)
                    text = re.sub("\[.*?\]", "", text)
                    # sstr = json.dumps(text.replace('\"', ''), ensure_ascii=False)+'\n'
                    leader_cv = leader_cv + text + "\n"
        return leader_cv
    
    @staticmethod
    def get_xianren_title(page_content):
        xianren_title = ""
        summary = ParseLeaderPage.get_summary(page_content=page_content) if page_content else ""
        description = ParseLeaderPage.get_description(page_content=page_content)
        if summary:
            title_pat = re.compile("现任(.*?)。")
            xianren_title = "".join(title_pat.findall(summary)) if "".join(title_pat.findall(summary)) else description
            prepos_pattern = re.compile("原主席|原省长|原书记|原主任|原调研员|原委员|原会长|原社长|原行长|原首席|原监事|原党组|原长官|原局长|原部长|原司长|原司令|原政委|原参谋长|原专员|原市长|原市　长|原区长|原州长|原盟长|原县长|原干部|原巡视|原督学|原总理|原常委|原编辑|原编委|原秘书|原会计|原组长|原牵头人|原外长|原成员|原领导|原领袖|原经理|原董事|原总监|原校长|原副校长|原院长|原检察长|原经济师|原工程师|原畜牧师|原农艺师|原兽医师|原署长|原副署长|原总设计师|原总会计师|原总审计师|原上将|原教育长|原总审计师|空缺|届次|任期|职务|姓名|原秘书长")
            pre_title_match = "".join(prepos_pattern.findall(xianren_title))
            if pre_title_match:
                xianren_title = ""
        url = ParseLeaderPage.get_page_url(page_content=page_content)
        if url=="https://baike.baidu.com/item/%E4%B9%A0%E8%BF%91%E5%B9%B3/515617":
            xianren_title = '中国共产党中央委员会总书记，中华人民共和国主席，中共中央军事委员会主席，中华人民共和国中央军事委员会主席，中央国家安全委员会主席'
        return xianren_title


    @staticmethod
    def check_death(page_content):
        '''判断是否去世，返回True or False'''
        is_death = False
        pattern = re.compile(r"\d+年\d+月.*?因病.*?逝世.*?享年\d+岁|\d+年\d+月.*?因车祸.*?逝世.*?享年\d+岁|\d+年\d+月.*?因病.*?去世.*?享年\d+岁|\d+年\d+月.*?因.*?逝世.*?享年\d+岁|\d+年\d+月.*?因.*?离世.*?享年\d+岁|\d+年\d+月.*?在.*?逝世.*?享年\d+岁")
        results = pattern.findall(page_content)
        if results:
            is_death=True
        return is_death
    
    @staticmethod
    def check_retirement(page_content)->bool:
        '''判断是否退休，返回True or False'''
        is_retirement = False
        pattern = re.compile("\d+年\d+月.*?免去{0,1}.*，退休|\d+年\d+月.*?因年龄原因.*?辞去.*?|\d+年\d+月.*?已到年{0,1}龄退休")
        results = pattern.findall(page_content)
        if results:
            is_retirement=True
        return is_retirement
    
    @staticmethod
    def get_page_url(page_content):
        url = ""
        url_pat = re.compile('<link rel="alternate" hreflang="x-default" href="(.*?)">')
        url_list = url_pat.findall(page_content) if page_content else []
        if len(url_list)==0:
            return ""
        else:
            url = url_list[0]
            url = re.sub(" |\u3000|%20|%E3%80%80|https://baike.baidu.com|http://baike.baidu.com", "", url)
            return url
    
    @staticmethod
    def check_fallen(page_content)->str:
        '''判断个人是否落马'''
        shencha_pat = re.compile("涉嫌.*?违纪违法|严重.*?违纪违法|开除.*?公职|开除.*?党籍|涉嫌.*?罪|依法.*?逮捕")
        result = ""
        if page_content:
            summary = ParseLeaderPage.get_summary(page_content)
            shencha_result = shencha_pat.findall(summary)
            if shencha_result:
                result = "审查"
        return result
                    

    @staticmethod
    def get_page_title(page_content):
        '''获取个人主页的title，如:李强_百度百科'''
        title = ""
        if not page_content:
            return title
        else:
            pattern = re.compile("<title>(.*?)</title>")
            title = "".join(pattern.findall(page_content))
            return title
        
    @staticmethod
    def get_leader_name(page_content):
        url = ParseLeaderPage.get_page_url(page_content=page_content)
        name = ""
        if url:
            match = re.search(r"(/item/[^/]+)", url)
            result = match.group(1) if match else None
            name = unquote_plus(re.sub("/item/", "", result))
        return name
        
        
    @staticmethod
    def get_polysemantlist(page_content, max_li_num=50):
        '''获取同名人polysemantantlist信息'''
        from lxml import quote_plus
        results = []
        pat = re.compile("<script>window.PAGE_DATA=(.*?)</script>")
        page_data_script = "".join(pat.findall(page_content))
        if page_data_script:
            page_data = json.loads(page_data_script)
            lemmacnt = int(page_data.get("lemmaCnt")) if page_data.get("lemmaCnt") else 0
            if lemmacnt>1:
                lemmas = page_data.get("navigation").get('lemmas')
                for idx in range(min(max_li_num, len(lemmas))):
                    lemma = lemmas[idx]
                    if 'uid' in lemma:
                        continue
                    _id = lemma.get("lemmaId")#"".join(re.compile('"lemmaId":(.*?),').findall(lemma))
                    _desc = lemma.get("lemmaDesc")#"".join(re.compile('"lemmaDesc":\"(.*?)\",').findall(lemma))
                    _title = lemma.get("lemmaTitle")#"".join(re.compile('"lemmaTitle":\"(.*?)\"').findall(lemma))
                    _url = "/item/{}/{}".format(quote_plus(_title), _id) if _id else "/item/{}".format(quote_plus(_title))
                    # print(f"{idx}: {_desc}----{_id}")
                    tmp = {"href": _url, "title": _desc}
                    if tmp not in results:
                        results.append(tmp)
            elif lemmacnt==1:
                _id = page_data.get("lemmaId")
                _desc = page_data.get("lemmaDesc")
                _title = page_data.get("lemmaTitle")
                _url = "/item/{}/{}".format(quote_plus(_title), _id) if _id else "/item/{}".format(quote_plus(_title))
                tmp = {"href": _url, "title": _desc}
                if tmp not in results:
                    results.append(tmp)
            else:
                return results      
        else:
            root = etree.HTML(page_content)
            li_list = root.xpath("//div[contains(@class, 'lemmaListTips')]//ul/li[contains(@class, lemmaItem)]")
            for li in li_list[:min(max_li_num, len(li_list))]:
                tag_a = li.xpath("./a")
                _title = "".join(tag_a.xpath("./text()")).strip()
                _url = "".join(tag_a.xpath("./@href")).strip()
                _url = re.sub("?fromModule=disambiguation", "", _url)
                tmp = {"href": _url, "title": _title}
                # print(f"{idx}: {_desc}----{_id}")
                if tmp not in results:
                    results.append(tmp)
        return results
    
    @staticmethod
    def get_pre_title(page_content):
        '''获取曾任职务信息'''
        pre_title = ""
        summary = ParseLeaderPage.get_summary(page_content=page_content) if page_content else ""
        description = ParseLeaderPage.get_description(page_content=page_content) if page_content else ""
        if summary: # 先在summary里找是否有曾任
            title_pat = re.compile("曾任(.*?)。")
            pre_title = "".join(title_pat.findall(summary)) if "".join(title_pat.findall(summary)) else ""
        if not pre_title and description:
            prepos_pattern = re.compile("原主席|原省长|原书记|原主任|原调研员|原委员|原会长|原社长|原行长|原首席|原监事|原党组|原长官|原局长|原部长|原司长|原司令|原政委|原参谋长|原专员|原市长|原市　长|原区长|原州长|原盟长|原县长|原干部|原巡视|原督学|原总理|原常委|原编辑|原编委|原秘书|原会计|原组长|原牵头人|原外长|原成员|原领导|原领袖|原经理|原董事|原总监|原校长|原副校长|原院长|原检察长|原经济师|原工程师|原畜牧师|原农艺师|原兽医师|原署长|原副署长|原总设计师|原总会计师|原总审计师|原上将|原教育长|原总审计师|空缺|届次|任期|职务|姓名|原秘书长")
            pre_title_match = "".join(prepos_pattern.findall(description))
            if pre_title_match:
                pre_title=description
        return pre_title




def parser_html_result(file_path:str, logging):
    global summary_count, yanzheng_count, empty_count
    # logging.info("=="*40)
    # logging.info(file_path)
    with open(file_path, "r", encoding="utf-8") as fp:
        html = fp.read()
        if not html or len(html)==0:
            empty_count+=1
        page_title = ParseLeaderPage.get_page_title(html)
        if "验证" in page_title:
            yanzheng_count+=1 
        discription = ParseLeaderPage.get_description(html)
        summary = ParseLeaderPage.get_summary(html)
        retirement = ParseLeaderPage.check_retirement(html)
        xianren = ParseLeaderPage.get_xianren_title(html)
        pre_title = ParseLeaderPage.get_pre_title(html)
        shencha = ParseLeaderPage.check_fallen(html)
        pageurl = ParseLeaderPage.get_page_url(html)
        leader_name = ParseLeaderPage.get_leader_name(html)
        cv = ParseLeaderPage.get_cv(html)
        if summary:
            summary_count+=1
        '''
        # logging.info("discription:{}".format(discription))
        # logging.info("summary:{}".format(summary))
        # logging.info("retirement:{}".format(retirement))
        # logging.info("xianren:{}".format(xianren))
        # logging.info("pre_title:{}".format(pre_title))
        # logging.info("shencha:{}".format(shencha))
        # logging.info("pageurl:{}".format(pageurl))
        '''
        parse_result = {
            'discription':discription,
            'summary': summary,
            'retirement':retirement,
            'xianren': xianren,
            'pre_title': pre_title,
            'shencha':shencha,
            'pageurl':pageurl,
            "name": leader_name
        }
        logging.info(json.dumps(parse_result,ensure_ascii=False))
    return parse_result

def process_file(file_path):
    if file_path.endswith(".json"):
        return  # 跳过 JSON 文件
    print(file_path)
    parser_html_result(file_path=file_path, logging=logging)

def glm_parse_results(file_path:str, logging):
    parse_result = dict()
    get_titles_url = "http://*************:9219/api/get_titles"
    headers = {"Content-Type":"application/json"}
    # logging.info("==="*50)
    if os.path.exists(file_path):
        with open(file=file_path, mode="r", encoding="utf-8") as fp:
            html_content = fp.read()
            if html_content:
                url = ParseLeaderPage.get_page_url(page_content=html_content)
                name = ParseLeaderPage.get_leader_name(page_content=html_content)
                summary = ParseLeaderPage.get_summary(page_content=html_content)
                if name and summary:
                    json_data={"action":"inference", "name": name, "summary": summary}
                    r = requests.post(get_titles_url, headers=headers, data=json.dumps(json_data))
                    logging.info(json.dumps(json_data, ensure_ascii=False))
                    logging.info(json.dumps(r.json(), ensure_ascii=False))
                    parse_result = r.json()
                else:
                    logging.warning(f"【{name}】or summary 为空")
                    logging.warning(f"{url}")
            else:
                logging.warning(f"{file_path}页面信息为空")
    return parse_result

def extract_chinese_characters(input_string):
    # 匹配中文字符的正则表达式
    pattern = r'[\u4e00-\u9fff]+'   
    # 找到所有匹配的中文字符
    chinese_characters = re.findall(pattern, input_string)
    # 将所有找到的中文字符连接成一个字符串
    # print(''.join(chinese_characters))
    return ''.join(chinese_characters)

summary_count = 0
yanzheng_count = 0
empty_count = 0      
yizhi_count=0
file_count=0

def main(html_path):
    # 设置 logging
    global yanzheng_count, empty_count, yizhi_count, file_count
    current_path = os.path.dirname(__file__)
    logfile_path = f"/home/<USER>/leader-info/UpdateLeaderInfo/runtime/{today_str}/logs/"
    logging.basicConfig(filename=os.path.join(logfile_path, "leader_discription_results.log"), filemode="w", level=logging.INFO, format="%(lineno)d | %(message)s")

    url = "http://*************:9219/api/get_titles"
    headers = {"Content-Type":"application/json"}
    data = {"action": "load_model"}
    r = requests.post(url, headers=headers, data=json.dumps(data))
    print(data["action"], r.json())
    count=0
    try:
        '''
        # with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
        #     futures = []
        #     for root, dirs, files in os.walk(html_path):
        #         for file in files:
        #             # 构建完整的文件路径
        #             file_path = os.path.join(root, file)
        #             # futures.append(executor.submit(process_file, file_path))
        #             futures.append(executor.submit(glm_parse_results, file_path, logging))

        #     # 等待所有线程完成
        #     for future in concurrent.futures.as_completed(futures):
        #         try:
        #             future.result()  # 获取结果（如果需要的话）
        #         except Exception as e:
        #             logging.error(f"Error processing file: {e}")
        '''
        for root, dirs, files in os.walk(html_path):
            count+=1
            # if count>10:
            #     break
            for file in files:
                
                # 构建完整的文件路径
                if file.endswith(".json"):
                    continue
                logging.info("=="*50)
                file_count+=1
                file_path = os.path.join(root, file)
                #if "胡家福" not in file_path:
                #    continue
                glm_parser_result = glm_parse_results(file_path, logging)
                print(count, glm_parser_result)
                re_parser_result = parser_html_result(file_path, logging)
       
                if glm_parser_result.get("msg", "")=="ok" and re_parser_result:
                    glm_result = "".join(glm_parser_result["result"].get("current_positions", []))
                    glm_result = extract_chinese_characters(glm_result)
                    re_result = re_parser_result.get("xianren", "")
                    re_result = extract_chinese_characters(re_result)
                    try:
                        logging.info("glm:{}".format(glm_result))
                        logging.info("re:{}".format(re_result))
                    except Exception as e:
                        print(e)

                    if ((glm_result==re_result) or (glm_result in re_result) or (re_result in glm_result)) and (len(glm_result)>0 and len(re_result)>0):
                        yizhi_count+=1

        data = {"action": "unload_model"}
        r = requests.post(url, headers=headers, data=json.dumps(data))
        print(data["action"], r.json())
    except:
        print(traceback.format_exc())
        data = {"action": "unload_model"}
        r = requests.post(url, headers=headers, data=json.dumps(data))
        print(data["action"], r.json())

    logging.info("filecount={}\tsummarycount={}".format(file_count, summary_count))
    logging.info("yizhicount={}".format(yizhi_count))
    logging.info("yanzhengcount={}\temptycount={}".format(yanzheng_count, empty_count))
    

if __name__=="__main__":
    html_path = f"/home/<USER>/leader-info/UpdateLeaderInfo/runtime/{today_str}/snapshot/baike.baidu.com/item"  # 替换为你的 HTML 目录
    main(html_path)
    '''
    # import logging
    # current_path = os.path.dirname(__file__)
    # logging.basicConfig(filename=os.path.join(current_path, "desc_detail_1025.log"), filemode="w", level=logging.INFO)
    # html_path= "/home/<USER>/leader-info/UpdateLeaderInfo/runtime/20241025/snapshot/baike.baidu.com/item"
    # global summary_count, yanzheng_count, empty_count

    # for root, dirs, files in os.walk(html_path):
    #     for file in files:
    #         # 构建完整的文件路径
    #         file_path = os.path.join(root, file)
    #         if file_path.endswith(".json"):
    #             continue
    #         print(file_path)
    #         parser_html_result(file_path=file_path, logging=logging)
            
    #     logging.info("filecount={}\tsummarycount={}".format(len(files), summary_count))
    #     logging.info("yanzhengcount={}\temptycount={}".format(yanzheng_count, empty_count))
    '''

                
