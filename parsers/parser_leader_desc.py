'''
对机构按parent进行分类，并获取每个org所属领导人信息
'''
import re, os, sys, requests
import json
from lxml import etree
import datetime
from urllib.parse import quote, quote_plus
current_dir = os.path.abspath(os.path.dirname(__file__))
sys.path.append(current_dir)
from parser_html import *
from parser_leader_html import ParseLeaderPage
os.system("clear")
from spiders import MP<PERSON><PERSON>er


def leader_desc(response):
    if isinstance(response, str):
        content = response
    else:
        content = response.text
    pagetitle = ParseLeaderPage.get_page_title(content)
    xianren = ParseLeaderPage.get_xianren_title(content)
    assert '验证' not in pagetitle
    summary = ParseLeaderPage.get_summary(content)
    discription = ParseLeaderPage.get_description(content)
    shencha = ParseLeaderPage.check_fallen(content)
    pre_title = ParseLeaderPage.get_pre_title(content)
    url = ParseLeaderPage.get_page_url(content)
    name = re.sub("\(.*?\)|（.*?）|百度百科|_| ", "", pagetitle) if pagetitle else ""
    return {
        "PageTitle": pagetitle, # title, 
        "Summary": summary, #Desc, 
        "Description": discription, #desc2,
        "XianRen": xianren,
        "shencha": shencha,
        "pre_title": pre_title,
        "name": name, 
        "url": url  
    }

def request_url(url):
    headers = {"Content-Type": "application/json", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
    body = {'url': url}
    resp = requests.post('http://g12.guodata.com:9208/detect', headers=headers, data=json.dumps(body))
    print(resp)
    data = resp.json()
    return data["html"]
    # print(data['url'])
    

# 获取百科页面
def get_page_source(org_name, read_cache=True, dirname=os.path.dirname(__file__)):
    page_source = ""
    html_file = os.path.join(dirname, "{}".format(org_name))
    # html_file = dirname
    if read_cache and os.path.exists(html_file):
        with open(html_file, "r", encoding="utf-8") as fp:
            page_source = fp.read()
    else:
        workdir = os.path.join(os.path.dirname(__file__), "html" )  # 这里已经根据日期创建了runtime文件夹
        # self.workdir = "/home/<USER>/leader-info/UpdateLeaderInfo/runtime/20240915"
        url = "https://baike.baidu.com/search/word?fromModule=lemma_search-box&lemmaId=&word=中国共产党中央委员会对外联络部"
        spider = MPSpider("/home/<USER>/leader-info/UpdateLeaderInfo/configs/config.yaml")
        html_spider = spider.run(urls=[url])
        page_source = html_spider
        with open(html_file, "w", encoding="utf-8") as fp:
            fp.write(page_source)
    return page_source


def _get_org_list():
    org_final_res = []
    config_file = os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")), "configs/configall.json")
    with open(config_file, "r", encoding="utf-8") as fp:
        json_data = json.load(fp=fp)
        http_organizations = json_data.get("http_organizations").get("value")
        org_list = requests.get(http_organizations)
        org_list = org_list.json()
    for org_type in org_list:
        if org_type!='地方':
            province = ""
            for org in org_list.get(org_type):
                org_item = {"org_type":org_type, "province": province, "org_name": org}
                if org_item not in org_final_res:
                    org_final_res.append(org_item) 
        else:
            pro_ls = list(org_list.get(org_type).keys())
            for province in pro_ls:
                org_item = {"org_type":org_type, "province": province, "org_name": ""}
                if org_item not in org_final_res:
                    org_final_res.append(org_item)    
    return org_final_res

if __name__=="__main__":  
    date_today = datetime.datetime.today().strftime("%Y%m%d")
    # print(date_today)
    
    runtime_dir1 = os.path.join(os.path.dirname(__file__), "html/{}".format(date_today))
    runtime_dir = "/home/<USER>/leader-info/UpdateLeaderInfo/runtime/20250614/snapshot/baike.baidu.com/search/word"
    if not os.path.exists(runtime_dir1):
        os.mkdir(runtime_dir1)
    org_list = _get_org_list()

    df_org_categories =["中共", "政府","人大", "政协"]
    save_file = os.path.join(os.path.dirname(__file__), "html/{}/leaders20250513.txt".format(date_today))
    all_leaders = []

    with open(save_file, 'w', encoding='utf-8') as fp:
        for org_item in org_list:
            all_leaders = []
            org_type = org_item["org_type"]
            province = org_item["province"]
            if org_type not in ["中管高校"] :
                continue
            # if province not in ['新疆生产建设兵团']:
            #     continue

            if org_type not in ['地方']:#'地方':
                org_name = org_item["org_name"]
                if org_name not in ["大连理工大学"]:
                    continue
                print(org_type, ":", runtime_dir, "----", org_name)
                fp.write("\n")
                fp.write(r"##{}:{}----{}".format(org_type, runtime_dir, org_name))
                fp.write("\n")
                not_df_runtime_dir = runtime_dir  #os.path.join(runtime_dir, org_name)
                if not os.path.exists(not_df_runtime_dir):
                    os.mkdir(not_df_runtime_dir)
                html_content = get_page_source(org_name=org_name, dirname=not_df_runtime_dir, read_cache=True)
                if org_type=="央企": # 央企
                    leaders = parse_yq(org=org_name, page_content=html_content)
                elif org_type=="国家级协会": #国家级协会
                    leaders = parse_xh(org=org_name, page_content=html_content)
                elif org_type=="国务院机构": #国务院机构
                    leaders = parse_gov(org=org_name, page_content=html_content)
                elif org_type=="军事机构":# 军事机构
                    leaders = parse_pla(org=org_name, page_content=html_content)
                elif org_type=="中直机构":# 中直机构
                    leaders = parse_zz(org=org_name, page_content=html_content)
                elif org_type=="领导机构":# 领导机构
                    leaders = parse_cpc(org=org_name, page_content=html_content)
                elif org_type=="中管高校":# 高校
                    leaders = parse_school( org=org_name, page_content=html_content)
                elif org_type=="政策性银行":
                    leaders = parse_yh(org=org_name, page_content=html_content)
                item = {"org":org_name, "lens": len(leaders), "leaders":leaders}
                if item not in all_leaders:
                    all_leaders.append(item)
            else:
                for category in df_org_categories:
                    if category=="政府":
                        org_name = "{}人民政府".format(province)
                    if category=="中共":
                        org_name="中国共产党{}委员会".format(province)
                    if category=="人大":
                        org_name="{}人民代表大会常务委员会".format(province)
                    if category=="政协":
                        org_name="中国人民政治协商会议{}委员会".format(province)
                    print(org_type,"----", org_name)
                    html_content = get_page_source(org_name=org_name, read_cache=True, dirname=runtime_dir)
                    leaders = parse_cppcc(org_info={"province":province, "org": category, "org_name":org_name}, page_content=html_content)
                    '''
                    # with open(os.path.join(prov_runtime_dir, "{}.json".format(org_name)), "w", encoding="utf-8") as fp:
                    #     json_leaders = json.dumps(leaders, ensure_ascii=False, indent=2)
                    #     fp.write(json_leaders)
                    '''
                    item = {"org":org_name, "lens": len(leaders), "leaders":leaders}
                    if item not in all_leaders:
                        all_leaders.append(item)
                    if 'leaders' not in locals():
                        print("没有 Leaders 这个变量")
                    if len(leaders)==0:
                        print("leaders = 0")
            if all_leaders:
                for iidx in range(0,len(all_leaders)):
                    print("===="*15)
                    print("{}:{}".format(province, df_org_categories[iidx]))
                    print("all_leaders={}".format(len(all_leaders[iidx]["leaders"])))
                    fp.write("{}:{}".format(province, df_org_categories[iidx]))
                    fp.write("all_leaders={}".format(len(all_leaders[iidx]["leaders"])))
                    fp.write("\n")
                    for item in all_leaders[iidx]["leaders"]:
                        print(item)
                        json_str=json.dumps(item, ensure_ascii=False)
                        fp.write(json_str+"\n")                
    # print("需要验证的org=", len(org_name_list))