import json
from bs4 import BeautifulSoup


def org_list(response):
    soup = BeautifulSoup(response.text, 'lxml')
    # print(soup.pre.text)
    pdetect_org_list = json.loads(soup.pre.text)
    _org_list = []
    for parent in pdetect_org_list.keys():
        if parent in ["null", "地方机构","国外"]:
            continue
        if parent not in ["地方"]:
            for org_temp in pdetect_org_list[parent]:
                org_info = {"parent": parent, "name": org_temp}
                _org_list.append(org_info)
        else:
            for province in pdetect_org_list["地方"].keys():
                org_name = "中国共产党" + province + "委员会"
                org_info = {"parent": "地方", "name": org_name}
                _org_list.append(org_info)

                org_name = province + "人民政府"
                org_info = {"parent": "地方", "name": org_name}
                _org_list.append(org_info)

                if province in ["新疆维吾尔自治区", "西藏自治区"]:
                    org_name = province + "人民代表大会"
                else:
                    org_name = province + "人民代表大会常务委员会"
                org_info = {"parent": "地方", "name": org_name}
                _org_list.append(org_info)

                org_name = "中国人民政治协商会议" + province + "委员会"
                org_info = {"parent": "地方", "name": org_name}
                _org_list.append(org_info)             
    return _org_list


def get_org_list_from_text(pagetext):
    _org_list = []
    if not pagetext:
        raise "从机构页面获取机构列表失败"
    jsondata = json.loads(pagetext)
    for parent in jsondata.keys():
        if parent in ["null", "地方机构","国外"]:
            continue
        if parent not in ["地方"]:
            for org_temp in jsondata[parent]:
                org_info = {"parent": parent, "name": org_temp}
                _org_list.append(org_info)
        else:
            for province in jsondata["地方"].keys():
                org_name = "中国共产党" + province + "委员会"
                org_info = {"parent": "地方", "name": org_name}
                _org_list.append(org_info)

                org_name = province + "人民政府"
                org_info = {"parent": "地方", "name": org_name}
                _org_list.append(org_info)

                if province in ["新疆维吾尔自治区", "西藏自治区"]:
                    org_name = province + "人民代表大会"
                else:
                    org_name = province + "人民代表大会常务委员会"
                org_info = {"parent": "地方", "name": org_name}
                _org_list.append(org_info)

                org_name = "中国人民政治协商会议" + province + "委员会"
                org_info = {"parent": "地方", "name": org_name}
                _org_list.append(org_info)             
    return _org_list

