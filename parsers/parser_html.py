# -*- coding: utf-8 -*-
from email.utils import unquote
import enum
from operator import truediv
import re, requests, pymysql, time, os, sys, csv, json, sqlalchemy, shutil, random
from uu import encode
from lxml import etree
from xml.etree import ElementTree
#import pandas as pd
from urllib.parse import quote_plus, quote, unquote
from requests_html import HTMLSession
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__),"..")))
# print(os.path.abspath(os.path.join(os.path.dirname(__file__),"..")))
from utils import join_text, remove_parentheses, remove_non_chinese_end



user_agent_list = [
        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/22.0.1207.1 Safari/537.1",
        "Mozilla/5.0 (X11; CrOS i686 2268.111.0) AppleWebKit/536.11 (KHT<PERSON>, like Gecko) Chrome/20.0.1132.57 Safari/536.11",
        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.6 (KHTML, like Gecko) Chrome/20.0.1092.0 Safari/536.6",
        "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.6 (KHTML, like Gecko) Chrome/20.0.1090.0 Safari/536.6",
        "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/19.77.34.5 Safari/537.1",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.9 Safari/536.5",
        "Mozilla/5.0 (Windows NT 6.0) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.36 Safari/536.5",
        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3",
        "Mozilla/5.0 (Windows NT 5.1) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_0) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3",
        "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1062.0 Safari/536.3",
        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1062.0 Safari/536.3",
        "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3",
        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3",
        "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3",
        "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.0 Safari/536.3",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.24 (KHTML, like Gecko) Chrome/19.0.1055.1 Safari/535.24",
        "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/535.24 (KHTML, like Gecko) Chrome/19.0.1055.1 Safari/535.24"
    ]

org_title_pattern = dict()
person_name_pattern = r'[\u4E00-\u9FA5]{2,6}(·[\u4E00-\u9FA5]{2,6}){0,2}'
person_name_list_pattern = person_name_pattern+'(、'+person_name_pattern+'){0,15}'
person_name_list_2_pattern = person_name_pattern+'(、'+person_name_pattern+'){1,15}'
person_name_list_pattern_well = person_name_pattern+'(#'+person_name_pattern+'){0,15}'
person_name_list_2_pattern_well = person_name_pattern+'(#'+person_name_pattern+'){1,15}'
person_name_no_title_pattern = r'(#|(书记)|(常委)|(省长)|(市长)|(区长)|(县长)|(州长)|(主任)|(主席)|(秘书长)|(党组)|(领导)|(委员))+'
person_name_pattern_no_title = r'[^#|(书记)|(常委)|(省长)|(市长)|(区长)|(县长)|(州长)|(主任)|(主席)|(秘书长)|(党组)|(领导)|(委员)]{2,6}(·[^#|(书记)|(常委)|(省长)|(市长)|(区长)|(县长)|(州长)|(主任)|(主席)|(秘书长)|(党组)|(领导)|(委员)]{2,6}){0,2}'
# person_name_list_pattern_no_title = person_name_pattern_no_title+'(、'+person_name_pattern_no_title+'){0,15}'
# person_name_list_2_pattern_no_title = person_name_pattern_no_title+'(、'+person_name_pattern_no_title+'){1,15}'
person_name_list_pattern_no_title_well = person_name_pattern_no_title+'(#'+person_name_pattern_no_title+'){0,15}'
# person_name_list_2_pattern_no_title_well = person_name_pattern_no_title+'(#'+person_name_pattern_no_title+'){1,15}'

org_title_pattern['协会'] = r'((党组)?(副)?((书记)|(成员))、)?(常务)?副?((主席)|(会长)|(理事长)|(秘书长))(、(党组)?副?((书记)|(成员)))?'
org_title_pattern['中共'] = r'((其它)|([省市县区州]+委))?(常务)?副?((书记)|(常委)|(州委委员)|(常务委员会委员))'
org_title_pattern['政府'] = r'((党组)?(副)?((书记)|(成员))、)?(常务)?副?((省长)|(市长)|(主席)|(秘书长))(、(党组)?副?((书记)|(成员)))?'
org_title_pattern['人大'] = r'((党组)?(副)?((书记)|(成员))、)?(常务)?副?((主任)|(秘书长))(、(党组)?副?((书记)|(成员)))?'
org_title_pattern['政协'] = r'((政协)?(党组)?(副)?((书记)|(成员))、)?(政协)?(常务)?副?((主席)|(秘书长))(、(政协)?(党组)?副?((书记)|(成员)))?'


pos_pattern = re.compile("董事长|主席|省长|市长|区长|书记|主任|调研员|委员|会长|社长|行长|首席|监事|党组|长官|局长|部长|司长|司令|政委|参谋长|专员|市长|市　长|区长|州长|盟长|县长|干部|巡视|督学|总理|常委|编辑|编委|秘书|会计|组长|牵头人|外长|成员|领导|领袖|经理|董事|总监|校长|副校|院长|检察长|经济师|工程师|畜牧师|农艺师|兽医师|署长|副署长|总设计师|总会计师|总审计师|上将|教育长|总审计师|总飞行师|理事|常委")


def print_temp(org, name, position, position_order, url):
    if url:
        # if org=="中国社会科学院":
        pattern = re.compile("(/item.+)\?fromModule")
        # print(url)
        url = pattern.findall(url)
        url = "".join(url)
        # print("[{} {} {} {} {}]".format(org, name, position, position_order, url))
    else:
        # print("[{} {} {} {}]".format(org, name, position, position_order))
        return

# 对url进行处理,全部编码为安全的格式
def url_encode(lst):
    pattern = re.compile(r"/item[\w-]+(\\?from){0,1}")#("(/item.+)\?from")
    pattern = re.compile(r'/item/[^?from]+')
    encoded_lst = []
    for item in lst:
        encoded_item = quote(item, safe='?:/')
        encoded_item = "".join(pattern.findall(encoded_item))
        encoded_item2 = re.sub("%25", "%", encoded_item)
        # print("encode_url:", encoded_item2)
        encoded_lst.append(encoded_item2)
    return encoded_lst

# 中央领导人特殊处理
def parse_zy(org=""):
    zy_leader_ls = []
    filepath = os.path.join("/home/<USER>/bazhuayu_data/", "leaderinfo_files/zy_leader.json")
    with open(filepath, "r", encoding="utf8") as fp:
        zy_leader = fp.readlines()

    for zy_ll in zy_leader:
        kk = type(zy_ll)
        if type(zy_ll) is str:
            zy_ll = json.loads(zy_ll)
        url = zy_ll["url"]
        url = re.sub("https://baike.baidu.com", "", url)
        leader_tmp = {"parent": zy_ll["parent"], "province": "", "city": "", "org": zy_ll["org"], "position": zy_ll["position"], "position_order": zy_ll["order"], "name": zy_ll["name"], "url": url}
        if org=="":
            zy_leader_ls.append(leader_tmp)
        else:
            if leader_tmp["org"]==org:
                zy_leader_ls.append(leader_tmp)
    return zy_leader_ls



def parse_gov_chongqing(org_info={}, page_content=""):
    senior_leader = []
    province = '重庆市'
    position_order = 0
    # 2023年7月18日修改解析方法
    # page_tree = etree.HTML(page_content)
    table_ls = page_content.xpath('.//table[contains(@class,"tableBox")]')
    position_order = 0
    for table in table_ls:
        caption = "".join(table.xpath("./caption/text()"))
        if caption not in ["重庆市人民政府领导"]:
            continue
        tr_list = page_content.xpath("//table//tr/td[2]")
        # print(f"len(tr_list)={len(tr_list)}")
        for tr in tr_list:
            tr_txt = ":".join(tr.xpath(".//span//text()")).strip()
            url_list = url_encode(tr.xpath(".//a/@href"))
            tmp = tr_txt.split(":")
            if len(tmp)>1:
                name_list = tmp[0].split("、")
                position = "".join(tmp[1:])
                position = re.sub("\[.*?\]", "", position).strip()
                for name in name_list:
                    name = re.sub(" ", "", name)
                    url = [s for s in url_list  if quote_plus(name) in s]
                    url = url[0] if url else ""
                    if name and position:
                        position_order=position_order+1
                        info_str = {"parent":"地方", "province":province, "city":"", "org":org_info["org"], "position":position, "position_order":position_order, "name":name, "url":url}
                        senior_leader.append(info_str)
    return senior_leader     

# 省级四套班子
def parse_cppcc(org_info={}, page_content=""):
    senior_leader = []
    province = org_info["province"]
    org = org_info["org"]
    org_name = org_info["org_name"]
    # print("*********{}--{}--{}**********".format(province, org, org_name))
    page_content = etree.HTML(page_content)
    create_new = page_content.xpath("//div[@class='errorBox']")
    if create_new:
        print("[ERROR] 无法找到 {}--{}--{}".format(province, org, org_name))
        return []
    
    if province in ['重庆市'] and org in ['政府']:
        chongqing_gov_leader = parse_gov_chongqing(org_info, page_content)
        return chongqing_gov_leader

    start = False
    h_title = ""
    position = ""
    position_order = 0
    # 领导成员

    div_titles = ["现任领导", "主要领导", "领导信息", "领导班子", "领导成员", "领导分工", "组成人员", "常务委员会", "政协领导", "政府领导","州委领导","县委领导","区委领导","市委领导","市委成员","社团领导","省委领导"]
    if org_name in ["山西省人民政府"]:
        div_titles=["政府领导"]
    elif org_name in ["上海市人民政府"]:
        div_titles = ["机构领导"]
    elif org_name in ["江苏省人民代表大会常务委员会"]:
        div_titles.append("人大常委会领导")
    
    if org_name in ["河北省人民政府"]:
        title_list = page_content.xpath("//div")
    else:
        title_list = page_content.xpath("//div[contains(@class, 'para')]")

    sidx = 0
    eidx = len(title_list)
    for idx, title in enumerate(title_list):
        if org_name in ['山西省人民政府','广东省人民代表大会常务委员会', '湖北省人民政府','中国共产党宁夏回族自治区委员会','宁夏回族自治区人民代表大会常务委员会', '贵州省人民代表大会常务委员会','北京市人民代表大会常务委员会','中国人民政治协商会议北京市委员会','中国人民政治协商会议吉林省委员会','中国共产党四川省委员会','四川省人民政府','上海市人民政府','上海市人民代表大会常务委员会','中国人民政治协商会议上海市委员会','云南省人民政府','云南省人民代表大会常务委员会','中国人民政治协商会议云南省委员会','中国共产党内蒙古自治区委员会','内蒙古自治区人民政府','内蒙古自治区人民代表大会常务委员会','中国人民政治协商会议内蒙古自治区委员会','吉林省人民代表大会常务委员会','四川省人民代表大会常务委员会','中国人民政治协商会议四川省委员会','天津市人民代表大会常务委员会','中国人民政治协商会议天津市委员会','宁夏回族自治区人民政府','中国人民政治协商会议宁夏回族自治区委员会','中国共产党安徽省委员会','安徽省人民政府','安徽省人民代表大会常务委员会','中国人民政治协商会议安徽省委员会','中国共产党山东省委员会','山东省人民政府','中国人民政治协商会议山东省委员会','中国共产党山西省委员会','山西省人民政府','山西省人民代表大会常务委员会','中国人民政治协商会议山西省委员会','中国共产党广东省委员会','中国人民政治协商会议广东省委员会','中国共产党广西壮族自治区委员会','广西壮族自治区人民政府','广西壮族自治区人民代表大会常务委员会','中国人民政治协商会议广西壮族自治区委员会','中国共产党新疆维吾尔自治区委员会','新疆维吾尔自治区人民政府','新疆维吾尔自治区人民代表大会常务委员会','中国人民政治协商会议新疆维吾尔自治区委员会','中国共产党江苏省委员会','江苏省人民代表大会常务委员会','中国共产党江西省委员会','江西省人民政府','江西省人民代表大会常务委员会','中国人民政治协商会议江西省委员会','中国共产党河北省委员会','河北省人民政府','河北省人民代表大会常务委员会','中国人民政治协商会议河北省委员会','中国共产党河南省委员会','河南省人民政府','河南省人民代表大会常务委员会','中国人民政治协商会议河南省委员会','中国共产党浙江省委员会','浙江省人民政府','浙江省人民代表大会常务委员会','中国人民政治协商会议浙江省委员会','中国共产党海南省委员会','海南省人民政府','海南省人民代表大会常务委员会','中国人民政治协商会议海南省委员会','中国共产党湖北省委员会','湖北省人民政府','湖北省人民代表大会常务委员会','中国人民政治协商会议湖北省委员会','中国共产党湖南省委员会','湖南省人民政府','湖南省人民代表大会常务委员会','中国人民政治协商会议湖南省委员会','中国共产党甘肃省委员会','甘肃省人民政府','甘肃省人民代表大会常务委员会','中国人民政治协商会议甘肃省委员会','中国共产党福建省委员会','福建省人民政府','福建省人民代表大会常务委员会','中国人民政治协商会议福建省委员会','中国共产党西藏自治区委员会','西藏自治区人民政府','西藏自治区人民代表大会常务委员会','中国人民政治协商会议西藏自治区委员会',
                    '中国共产党贵州省委员会','贵州省人民政府','贵州省人民代表大会常务委员会','中国人民政治协商会议贵州省委员会',
                    '中国共产党辽宁省委员会','辽宁省人民政府','辽宁省人民代表大会常务委员会','中国人民政治协商会议辽宁省委员会',
                    '中国共产党重庆市委员会','重庆市人民政府','重庆市人民代表大会常务委员会','中国人民政治协商会议重庆市委员会',
                    '中国共产党陕西省委员会','陕西省人民政府','陕西省人民代表大会常务委员会','中国人民政治协商会议陕西省委员会',
                    '中国共产党青海省委员会','青海省人民政府','青海省人民代表大会常务委员会','中国人民政治协商会议青海省委员会',
                    '中国共产党黑龙江省委员会','黑龙江省人民政府','黑龙江省人民代表大会常务委员会','中国人民政治协商会议黑龙江省委员会',
                    '中国共产党新疆生产建设兵团委员会','新疆生产建设兵团人民政府','新疆生产建设兵团人民代表大会常务委员会','中国人民政治协商会议新疆生产建设兵团委员会']:
            leader_text = title.xpath("./h2/text()")
        else:
            leader_text = title.xpath("./h2[@name]/text()")
        
        leader_text = "".join(leader_text)   


        if org_name in ["海南省人民政府"]:
            title_text = "".join(title.xpath("string(.)"))
            if leader_text:
                if start==False and leader_text in div_titles:
                    start = True
                    sidx = idx+1
                    continue
                if start==True and leader_text.find("历届省长")!=-1:
                    eidx = idx
                    break
            '''if (leader_text==None or leader_text=="") and (start==True):
                if title.xpath("./h3"):
                    h3_title = "".join(title.xpath("./h3/text()"))
                    if h3_title.find("历届省长")!=-1:
                        eidx = idx
                        break'''
        elif org_name in ["江苏省人民政府",'中国人民政治协商会议江苏省委员会']:
            leader_text = title.xpath("./h3/text()")
            leader_text = "".join(leader_text).strip()
            if leader_text:
                if start==False and leader_text in div_titles:
                    start=True
                    sidx=idx+1
                    continue
                if leader_text and start==True and leader_text=="历任领导":
                    eidx = idx
                    break  
        elif org_name in ["山西省人民政府"]:
            if leader_text:
                if start==False and leader_text in div_titles:
                    start=True
                    sidx = idx
                    continue
                elif start==True:
                    eidx = idx
                    break
                    
        else:
            if leader_text:
                if start==False and leader_text in div_titles:
                    start = True
                    sidx = idx+1 
                    continue
                elif start==True:
                    eidx = idx
                    break
            else:
                leader_text = title.xpath("./h3[@class='title-text']/text()")
                leader_text = "".join(leader_text)
                if leader_text and '现任领导' in leader_text:
                    start = True
                    sidx = idx + 1
                    continue
    div_list = []
    if org_name in ["中国共产党广东省委员会","中国共产党云南省委员会", "中国共产党上海市委员会", "中国共产党浙江省委员会"]:
        table_ls = page_content.xpath(".//table[contains(@class,'tableBox')]")
        for table in table_ls:
            caption = "".join(table.xpath("./caption/text()")).strip()
            if caption not in ["领导成员", "现任领导", '现任领导成员']:
                continue
            tr_list = table.xpath(".//tr")
            if tr_list and len(tr_list)>1:
                div_list = tr_list[1:]
    else:   
        div_list = title_list[sidx:eidx+1] 

    if org_name in ["中国人民政治协商会议河南省委员会"]:
        text = ""
        for div in div_list:
            tt = "".join(div.xpath(".//text()"))
            tt = re.sub("\[.*?\]", "", tt).strip()
            if pos_pattern.findall(tt):
                text = text+"#{}".format(tt)
            else:
                text = "{}：{}".format(text, tt)
            text = re.sub("^#", "", text)
        #     text.append(tt)
        # text = "#".join(text)
    elif org_name in ["中国共产党广东省委员会"]:      
        text = []
        for tr in div_list:
            td_list = tr.xpath(".//td[position() > last() - 2]//span/text()|.//td[position() > last() - 2]//span//a/text()")
            td_text = []
            for td in td_list:
                temp = re.sub("\[.*?\]", "、", td).strip()
                if temp and re.sub("（女）", "", temp):
                    td_text.append(temp)
            td_text = "：".join(td_text)
            td_text = re.sub("：、&", "", td_text)
            text.append(td_text)
        text = "#".join(text)
    elif org_name in ["中国共产党云南省委员会", "中国共产党浙江省委员会"]:
        text = []
        for tr in div_list:
            td_list = tr.xpath("./td//text()")
            if len(td_list)>1:
                td_text = td_list[0]+":"+"".join(td_list[1:])
                td_text = re.sub("\[.*?\]", "", td_text).strip()
                text.append(td_text)
            else:
                continue
        text = "#".join(text)
    elif org_name in ['中国共产党上海市委员会']:
        text = []
        for tr in div_list:
            td_list = tr.xpath("./td")
            if td_list and len(td_list)>1:
                div_templist = td_list[-1].xpath("./div")
                for div in div_templist:
                    div_text = "".join(div.xpath(".//text()"))
                    if div_text not in text:
                        text.append(div_text)
        text = "#".join(text)
    elif org_name in ["天津市人民政府"]:
        text = []
        start = False
        div_list = page_content.xpath(".//div[@class='J-lemma-content']/div")
        for div in div_list:
            h2_text = "".join(div.xpath(".//h2/text()"))
            if start==False:
                if h2_text in ["现任领导", "市委领导"]:
                    start=True
            else:
                if h2_text:
                    break
                table_ls = div.xpath(".//table[contains(@class, 'tableBox')]")
                for table in table_ls:
                    tr_list = table.xpath("./tbody/tr")
                    if tr_list and len(tr_list)>1:
                        for tr in tr_list[1:]:
                            tr_text = []
                            td_list = tr.xpath("./td")
                            for td in td_list[:-1]:
                                td_text = "".join(td.xpath(".//text()")).strip()
                                
                                if td_text not in text:
                                    tr_text.append(td_text)
                            tr_text = "：".join(tr_text)
                            if tr_text not in text:
                                text.append(tr_text)
        text = "#".join(text)       
    elif org_name in ["北京市人民政府", "中国共产党北京市委员会", "中国共产党天津市委员会"]:
        text = []
        start = False
        div_list = page_content.xpath(".//div[@class='J-lemma-content']/div")
        for div in div_list:
            h2_text = "".join(div.xpath(".//h2/text()"))
            if start==False:
                if h2_text in ["现任领导", "市委领导"]:
                    start=True
            else:
                if h2_text:
                    break
                table_ls = div.xpath(".//table[contains(@class, 'tableBox')]") #div.xpath(".//table[@class='tableBox_ezPLl']|.//table[contains(@class, 'tableBox_hIjb7')]")
                for tbl_idx, table in enumerate(table_ls):
                    # tab_caption = "".join(table.xpath("./caption/text()"))
                    tr_list = table.xpath("./tbody/tr")          
                    for tr in tr_list:
                        td_list = tr.xpath("./td")
                        if len(td_list)>=2:
                            temp_list = td_list[1].xpath(".//text()")
                            print("temp_list={}".format(temp_list))
                            try:
                                if "工作分工：" in temp_list:
                                    index = temp_list.index("工作分工：")
                                    text.append("{}：{}".format(temp_list[0],"".join(temp_list[1:index])))
                                else:
                                    index=-1
                                    text.append("{}：{}".format(temp_list[0],"".join(temp_list[1::])))
                            except ValueError:
                                continue
                        else:
                            pass

        text = "#".join(text)
        text = re.sub("校、长|校（院）长（兼）", "校长", text)
    elif org_name in ["广东省人民政府"]:
        text = []
        start = False
        div_list = page_content.xpath(".//div")#('//div[contains(@class, "para")]')#(".//div[@class='J-lemma-content']/div")
        for div in div_list:
            h2_text = "".join(div.xpath(".//h2/text()"))
            if start==False:
                if h2_text in ["现任领导", "市委领导","政府领导"]:
                    start=True
            else:
                if h2_text:
                    break
                table_ls = div.xpath(".//table[contains(@class, 'tableBox')]") #div.xpath(".//table[@class='tableBox_ezPLl']|.//table[contains(@class, 'tableBox_hIjb7')]")
                # print("table_ls={}".format(len(table_ls)))
                for tbl_idx, table in enumerate(table_ls):
                    tr_list = table.xpath("./tbody/tr")          
                    for tr in tr_list:
                        td_list = tr.xpath("./td")
                        if len(td_list)>=2:
                            temp_list = td_list[1].xpath(".//text()")
                            try:
                                if "工作分工：" in temp_list:
                                    index = temp_list.index("工作分工：")
                                    text_str = "{}：{}".format("".join(temp_list[1:index]), temp_list[0])
                                else:
                                    index=-1
                                    text_str = "{}：{}".format("".join(temp_list[1:index]), temp_list[0])
                                if text_str not in text:
                                    text.append(text_str)
                            except ValueError:
                                continue
                        else:
                            pass
                        # print(text[-1])
        text = "#".join(text)
    elif org_name in ["山西省人民政府",'河北省人民政府']:
        div_txt_list = []
        text = []
        followtable = div_list[0].xpath("./following::table[1]")
        if followtable:
            tr_list = followtable[0].xpath("./tbody/tr")
            for tr in tr_list[1:]:
                td_list = tr.xpath("./td")
                tr_text = []
                for td in td_list:
                    td_text = "".join(td.xpath(".//text()"))
                    tr_text.append(td_text)
                tr_text = ":".join(tr_text)
                text.append(tr_text)
        text = "#".join(text)
    else:
        div_txt_list = []
        yijingjisuan = False
        for div_idx, div in enumerate(div_list):
            if org_name=="中国共产党河北省委员会" and div_idx==2:
                continue
            if org_name in ["新疆维吾尔自治区人民政府"]:
                div_text = div.xpath(".//text()[not(ancestor::h3)]") #捕获div下除h3标签的所有的text
            if org_name in ["北京市人民代表大会常务委员会"]:
                div_text = []
                ancestor_tr = div.xpath("ancestor::tr[1]")
                if ancestor_tr:
                    ancetor_tr = ancestor_tr[0]
                    td_texts = ancetor_tr.xpath('./td//span/text()')
                    if td_texts and len(td_texts)>2:
                        if td_texts[0] not in div_text:
                            div_text.append(td_texts[0])
                        if td_texts[-1] not in div_text:
                            div_text.append("#"+td_texts[-1])
            elif org_name in ["中国共产党四川省委员会"] and not yijingjisuan:
                yijingjisuan=True
                tr_texts = []
                ancestor_table = div.xpath("ancestor::table[1]")
                if ancestor_table:
                    tr_list = ancestor_table[0].xpath(".//tr")
                    if tr_list:
                        for tr in tr_list[1:]:
                            td_list = tr.xpath("./td")
                            td_texts = []
                            for td in td_list:
                                td_text = "".join(td.xpath(".//text()"))
                                if td_text not in td_texts:
                                    td_texts.append(td_text)
                            if td_texts and len(td_texts)>=2:
                                tr_texts.extend(td_texts[-2:])
                div_txt_list = tr_texts
                break

            elif org_name in ["河南省人民政府"]:
                div_text = div.xpath(".//text()")
            else:
                div_text = div.xpath("string(.)")
            div_text = "".join(div_text)
            div_text = div_text.strip()
            if div_text not in div_txt_list:
                div_txt_list.append(div_text)

        text = "#".join(div_txt_list)
    # text = '#'.join(div_list.xpath("string(.)").getall())
    text = text.replace('\n', '') # 去掉换行
    text = re.sub("\[.*?\]", "、", text).replace('：：','：')  # 去掉[1][2]
    text = re.sub("[\(（].*?[）\)]", "、", text).replace(' ', '').replace(' ', '').replace('\u3000', '')
    text = re.sub("、+", "、", text).replace(':', '：')
    text = text.replace('：：', '：').replace('、：', '：').replace('、，', '，').replace('、。', '。').replace('、#', '#')  # 去掉[1][2]
    info_dict = dict()
    print("text=\n\t{}\n-------------------".format(text))

    #职务：姓名列表
    #pattern = re.compile(r'(^|#)[^#]*?((政协)?(党组)?(副)?((书记)|(成员))、)?(政协)?(常务)?副?((书记)|(常委)|(省长)|(市长)|(主席)|(主任)|(秘书长))(、(政协)?(党组)?副?((书记)|(成员)))?([^政协：#。；]+?)?：[\u4E00-\u9FA5]{2,6}(·[\u4E00-\u9FA5]{2,6}){0,2}(、[\u4E00-\u9FA5]{2,6}(·[\u4E00-\u9FA5]{2,6}){0,2}){0,15}')
    if org_name in ["山西省人民政府"]:
        org_title_pattern['政府'] = r'((党组)|(省委))?(副)?((书记)|(成员)、?)?((副)？((书记)|(成员)|(省长)))?'
        pattern = re.compile(r'(^|#)[^#]*?'+org_title_pattern[org]+'([^政协：#。；]+?)?：'+person_name_list_pattern)
    elif org_name in ["新疆维吾尔自治区人民政府","江苏省人民政府","河南省人民政府","浙江省人民政府", "海南省人民政府", "湖北省人民政府", "贵州省人民政府", "陕西省人民政府", "黑龙江省人民政府",'广东省人民政府']:
        pattern = re.compile(r'([^#]+(?:党组书记|党组副书记|党组成员|副主席|总经理|主任|书记|厅长|主委|主席|督察长)[^#]*)(?=#|$)')
    else:
        pattern = re.compile(r'(^|#)[^#]*?'+org_title_pattern[org]+'([^政协：#。；]+?)?：'+person_name_list_pattern)
    res_iter = pattern.finditer(text)
    item_num=0
    # print(text)
    for res in res_iter:
        seg = re.split("：",text[res.span()[0]:res.span()[1]].strip('#@'))
        if len(seg)<2:
            continue
        if re.findall(pos_pattern, seg[0]):
            pos_idx=0
            name_idx=1      
        else:
            pos_idx = 1
            name_idx = 0
        position = seg[pos_idx]
        position= re.sub(r"[。，,!、；]*$","", position)
        name_list = seg[name_idx].split("、")
        # position = seg[0] #if org_name not in ["广东省人民政府"] else seg[1]
        # name_list = seg[1].split("、")   #if org_name not in ["广东省人民政府"] else seg[0].split("、")
        #校验名字的准确性
        # if re.search(person_name_no_title_pattern, seg[1]):
        #     continue
        if re.search(person_name_no_title_pattern, seg[name_idx]):
            continue
        
        for name in name_list:
            # print(position, name)
            item_num=item_num+1
            info_dict[item_num+res.span()[0]] = [position, name]
        text = text[:res.span()[0]]+'@'*len(text[res.span()[0]:res.span()[1]])+text[res.span()[1]:]
    # if item_num>5:
    #     return

    #姓名；职务
    #pattern = re.compile(r'(^|#)[^#]*?' + person_name_pattern + r'：[^#：\n]*?' + org_title_pattern[org] + r'[^#：。；\n]*')
    pattern = re.compile(r'(^|#)'+person_name_pattern+r'：[^#：\n]*?'+org_title_pattern[org]+r'[^#：。；\n]*')
    res_iter = pattern.finditer(text)
    item_num = 0
    for res in res_iter:
        seg = text[res.span()[0]:res.span()[1]].strip('#@').split("：")
        name = seg[0]
        # 校验名字的准确性
        if re.search(person_name_no_title_pattern, name) or re.search('常设机构', name):
            continue
        position = seg[1].strip('、')#.split("、")
        item_num = item_num + 1
        info_dict[item_num + res.span()[0]] = [position, name]
        # print(position, name)
        text = text[:res.span()[0]] + '@' * len(text[res.span()[0]:res.span()[1]]) + text[res.span()[1]:]
    # if item_num > 5:
    #     return

    #判断是否包含姓名列表， #慕德贵、何力、李飞跃、桑维亮、王忠、杨永英、蓝绍敏#
    pattern = re.compile(r'(^|#)' + org_title_pattern[org] + r'#' + person_name_list_2_pattern)
    res_iter = pattern.finditer(text)
    item_num = 0
    for res in res_iter:
        #print('########',res)
        # 职务#姓名列表，以、分割
        seg = re.split("#", text[res.span()[0]:res.span()[1]].strip('#@'))
        # 校验名字的准确性
        if re.search(person_name_no_title_pattern, seg[1]):
            continue
        position = seg[0]
        name_list = seg[1].split("、")
        for name in name_list:
            if len(name)>4:  #去掉名字重叠的情况 政协主席 董云虎董云虎
                sub = int(len(name)/2)
                if name[0:sub]==name[sub:]:
                    name = name[0:sub]
            item_num = item_num + 1
            info_dict[item_num + res.span()[0]] = [position, name]
            # print(position, name)
        text = text[:res.span()[0]] + '@' * len(text[res.span()[0]:res.span()[1]]) + text[res.span()[1]:]

    # 判断是否包含非职务连续列表，且为姓名列表， #慕德贵#何力#李飞跃#桑维亮#王忠#杨永英#蓝绍敏#
    pattern = re.compile(r'(^|#)' + org_title_pattern[org] + r'#' + person_name_list_pattern_well)
    res_iter = pattern.finditer(text)
    item_num = 0
    for res in res_iter:
        sub_text = text[res.span()[0]:res.span()[1]].strip('#@')
        sub_pattern = re.compile(r'(^|#)' + org_title_pattern[org] + r'#' + person_name_list_pattern_no_title_well)
        sub_iter = sub_pattern.finditer(sub_text)
        for res1 in sub_iter:
            # 职务#姓名列表，以#号分割(#慕德贵#何力#李飞跃#桑维亮#王忠#杨永英#蓝绍敏#)
            seg = re.split("#", sub_text[res1.span()[0]:res1.span()[1]].strip('#'))
            position = seg[0]
            name_list = seg[1:]
            for name in name_list:
                if len(name) > 4:  # 去掉名字重叠的情况 政协主席 董云虎董云虎
                    sub = int(len(name) / 2)
                    if name[0:sub] == name[sub:]:
                        name = name[0:sub]
                # print(position, name)
                item_num = item_num + 1
                info_dict[item_num + res.span()[0] + res1.span()[0]] = [position, name]
                if '秘书长' in position:
                    break
            sub_text = sub_text[:res1.span()[0]] + '@' * len(sub_text[res1.span()[0]:res1.span()[1]]) + sub_text[res1.span()[1]:]
        text = text[:res.span()[0]] + sub_text + text[res.span()[1]:]
        # if item_num > 3:
        #     return

    # 姓名#职务
    #pattern = re.compile(r'(^|#)[^#|(书记)|(常委)|(省长)|(市长)|(主席)|(主任)|(秘书长)]*?[\u4E00-\u9FA5]{2,6}(·[\u4E00-\u9FA5]{2,6}){0,2}#[^#：\n]*?((书记)|(常委)|(省长)|(市长)|(主席)|(主任)|(秘书长))[^#：。；\n]*')
    pattern = re.compile(r'(^|#)'+person_name_pattern+r'#[^#：\n]*?'+org_title_pattern[org]+r'[^#：。；\n]*')
    res_iter = pattern.finditer(text)
    # print(text)
    item_num = 0
    for res in res_iter:
        seg = text[res.span()[0]:res.span()[1]].strip('#@').split("#")
        name = seg[0]
        # 校验名字的准确性
        if re.search(person_name_no_title_pattern, name):
            continue
        position = seg[1].strip('、')  # .split("、")
        item_num = item_num + 1
        # print(position, name)
        info_dict[item_num + res.span()[0]] = [position, name]
        text = text[:res.span()[0]] + '@' * len(text[res.span()[0]:res.span()[1]]) + text[res.span()[1]:]

    if item_num < 5:
        pattern = re.compile(r'(.*?)([\u4E00-\u9FA5]{2,6}(?:·[\u4E00-\u9FA5]{2,6}){0,2}(?:、[\u4E00-\u9FA5]{2,6}(?:·[\u4E00-\u9FA5]{2,6}){0,2}){0,15})([^，。#]*?当选[^，。#纪委]*?)' + r'(副?(?:(书记)|(常委)|(常务委员会委员)))')#|(常务委员会委员)])')
        text = re.sub(pattern, r'@\2：\4@', text)
        pattern = re.compile(r'@'+person_name_list_pattern+'：'+r'(副?(?:(书记)|(常委)|(常务委员会委员)))@')
        res_iter = pattern.finditer(text)
        item_num = 0
        for res in res_iter:
            seg = text[res.span()[0]:res.span()[1]].strip('@').split("：")
            position = seg[1]
            # 校验名字的准确性
            if re.search(person_name_no_title_pattern, seg[0]):
                continue
            name_list = seg[0].split('、')
            for name in name_list:
                item_num = item_num + 1
                info_dict[item_num + res.span()[0]] = [position, name]

    # html = '\n'.join(div_list)
    url_list = []
    for div in div_list:
        url = div.xpath(".//a/@href")
        for uu in url:
            url_list.append(uu)
    url_list = url_encode(url_list)
    position_order = 0  
    import unicodedata     
    for k in sorted(info_dict):
        position = info_dict[k][0]
        name = info_dict[k][1]
        if name:
            last_chr = name[-1]
            if not ('\u4e00'<=last_chr<='\u9fff'):
                name = name[:-2]
            if name in ["姓名", "职务"]:
                continue
        # print(quote_plus(name))
        if len(name)>2:
            url = [s for s in url_list if quote_plus(name) in s or quote(name) in s or name in s]
        elif len(name)==2:
            url = [s for s in url_list if quote_plus(name) in s or (quote(name[0]) in s and quote(name[1]) in s)]
        url = url[0] if url else ""
        url = re.sub("\?fromModule=lemma_inlink", "", url)
        if name in url:
            url = re.sub(name, quote_plus(name), url)
        if re.findall('常设机构', name) or re.findall('参考资料', name):
            continue
        position_order = position_order + 1
        position = re.sub("\xa0|\[.*?\]", "", position)
        if re.compile("第.*?届").match(name):
            continue
        info_str = {"parent":"地方", "province":province, "city":"", "org":org_info["org"], "position":position, "position_order":position_order, "name":name, "url":url}
        senior_leader.append(info_str) 
    return senior_leader


# 领导机构
def parse_cpc(org="", page_content=""):
    senior_leader = []
    # print("*********领导机构/{}**********".format(org))
    # cpc_org_list = ["中共中央", "中华人民共和国国家主席", "中华人民共和国国务院", "中华人民共和国中央军事委员会", "中央书记处书记", "中国共产党中央委员会政治局委员", "中国共产党中央委员会政治局常务委员会委员"]
    cpc_org_list = ["中共中央", "中华人民共和国国家主席", "中华人民共和国国务院", "中华人民共和国中央军事委员会"]
    page_content = etree.HTML(page_content)
    create_new = page_content.xpath("//div[@class='errorBox']")
    if create_new and org not in cpc_org_list:
        print("----[ERROR] 无法找到{}".format(org))
        return senior_leader
    
    if org in ["中央委员会总书记"]:
        info_str = {"parent": "领导机构", "province":"", "city":"", "org":org, "position":"中国共产党中央委员会总书记", "position_order":1, "name":"习近平", "url":f"/item/%E4%B9%A0%E8%BF%91%E5%B9%B3"}
        senior_leader.append(info_str) 
        return senior_leader
    start = False
    h_title = ""
    position = ""
    position_order=0
    leader_part = False   # 为中国共产党中央军事委员会设置的参数

   
    if org in cpc_org_list: 
        senior_leader = parse_zy(org=org)
        return senior_leader
    else:  # 除了cpc_org_list里的其他领导机构
        # 领导成员
        position_order = 0
        title_list = page_content.xpath("//div[contains(@class, 'para')]")
        for title in title_list:
            if start == False:
                if org in ["中华人民共和国最高人民检察院", "中华人民共和国全国人民代表大会常务委员会", "中国人民政治协商会议全国委员会"]: #["中华人民共和国全国人民代表大会常务委员会", "中华人民共和国最高人民检察院"]:
                    leader_text = title.xpath("./h3/text()")
                    leader_text = "".join(leader_text)
                    if leader_text and leader_text.strip() in ["现任领导"]: # 找到文章段落，开始解析
                        start = True
                        if org in ["中华人民共和国最高人民检察院"]:
                            tr_list = title.xpath("./following::table[1]//tr")
                            for tr in tr_list[1:]:
                                text_list = tr.xpath("./td[2]//text()")
                                name = join_text(text_list).strip()
                                if name == "空缺":
                                    continue
                                name_list = re.split('、', name)
                                if name == "":
                                    name = join_text(tr.xpath("./td[1]//text()")).strip()
                                else:
                                    position = join_text(tr.xpath("./td[1]//text()")).strip()
                                url_list = url_encode(tr.xpath('.//a/@href'))
                                for name in name_list:
                                    name = remove_parentheses(name)
                                    if re.compile("第.*?届").match(name):
                                        continue
                                    if name.strip():
                                        if len(name)>2:
                                            tmp_url = [s for s in url_list if quote_plus(name) in s]
                                        elif len(name)==2:
                                            tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                                        url = tmp_url[0] if tmp_url else ""
                                        position_order+=1
                                        position = re.sub("\[.*?\]", "", position)
                                        info_str = {"parent": "领导机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                        senior_leader.append(info_str)  
                            return senior_leader
                else:
                    leader_text = title.xpath("./h2[@name]/text()") if org not in ["中央社会工作部"] else title.xpath("./h2/text()")
                    leader_text = "".join(leader_text)
                    # print(f"leader_text={leader_text}")
                    if org in ["中央书记处书记", "中国共产党中央委员会政治局委员", "中国共产党中央委员会政治局常务委员会委员"]:
                        position = org
                        if leader_text in ["历届名单"]:
                            tr_list = title.xpath("./following::table[1]//tr")
                            for tr in tr_list[-1:]:
                                if org in ["中央书记处书记", "中国共产党中央委员会政治局常务委员会委员"]:
                                    text_list = tr.xpath("./td[3]//text()")                              
                                    url_list = tr.xpath('./td[3]//a/@href')
                                elif org in ["中国共产党中央委员会政治局委员"]:
                                    text_list = tr.xpath("./td[2]//text()")
                                    url_list = tr.xpath("./td[2]/a/@href")
                                cat_text = join_text(text_list).strip()
                                name_list = re.split("、", cat_text)
                                for name in name_list:
                                    name = remove_parentheses(name)
                                    if re.compile("第.*?届").match(name):
                                        continue
                                    if name.strip():
                                        if len(name)>2:
                                            tmp_url = [s for s in url_list if quote_plus(name) in s]
                                        elif len(name)==2:
                                            tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                                        url = tmp_url[0] if tmp_url else ""
                                        position_order+=1
                                        position = re.sub("\[.*?\]","", position)
                                        info_str = {"parent": "领导机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                        senior_leader.append(info_str)  
                        return senior_leader
                    elif org in ["中国共产党中央军事委员会"]:
                        if leader_text in ["历任领导"]:
                            leader_part = True
                        if leader_part:
                            tag_b = title.xpath("./b")
                            if tag_b:
                                start=True
                    elif org in ["中央社会工作部"]:
                        if leader_text in ["领导分工"]:
                            following_table = title.xpath("./following::table[1]")
                            if following_table:
                                caption = following_table[0].xpath("./caption/text()")
                                # print(f"org={org}\t caption={caption}")
                                tr_list = following_table[0].xpath(".//tr")
                                for tr in tr_list[1:]:
                                    td_list=tr.xpath("./td")
                                    if td_list and len(td_list)>1:
                                        position = "".join(td_list[0].xpath(".//text()"))
                                        name_text = "".join(td_list[1].xpath(".//text()"))
                                        position = re.sub("\[.*?\]", "", position)
                                        position = remove_non_chinese_end(position)
                                        url_list = tr.xpath("./td//a/@href")
                                        if name_text and position:
                                            name_list = name_text.split(r"、")
                                            for name in name_list:
                                                if name.strip():
                                                    position_order+=1
                                                    position = re.sub("\xa0|\[.*?\]", "", position).strip()
                                                    name = remove_parentheses(name)
                                                    if len(name)>2:
                                                        tmp_url = [s for s in url_list if quote_plus(name) in s]
                                                    elif len(name)==2:
                                                        tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                                                    url = tmp_url[0] if tmp_url else ""  
                                                    url = re.sub("\?fromModule=lemma_inlink", "", url)                         
                                                    info_str = {"parent": "领导机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                                    senior_leader.append(info_str) 
                            return senior_leader
                    else:
                        if leader_text in ["领导成员", "现任领导", "第十三届全国政协主席、副主席、秘书长"]: # 找到文章段落，开始解析
                            start = True
            else:
                if org in ["中国共产党中央军事委员会"]:
                    tag_b = title.xpath("./b")
                    if tag_b:
                        start = False
                        break
                if title.xpath("./h3"):
                    h_title = title.xpath("./h3/text()")
                    if isinstance(h_title, list):
                        h_title = "".join(h_title)
                    if h_title in ["历任领导"]:
                        start = False
                        break
                elif title.xpath("./h2"):
                    start = False # 找到下一个文章段落，停止解析
                    break
                else:
                    text_list = title.xpath("./text() | ./*[not(contains(@class, 'layout-right'))]//text()")  # 获得每一条文本
                    text = join_text(text_list)
                    # print(text)
                    if re.search("参考资料", text):
                        break
                    if re.search("资料|更新", text):
                        continue
                    else:
                        if org in ["中国农业发展集团有限公司"]:
                            text = text.rstrip()
                            text = text.replace(" ", "_")
                            findspace = text.find("_")
                            if findspace!=-1:
                                if findspace<2:
                                    list_text = list(text)
                                    list_text.pop(findspace)
                                    list_text = "".join(list_text)
                                    text = []
                                    text = str(list_text)
                            else:
                                name_list = []
                                position = ""
                            my_temp_list = re.split('_', text)
                            if len(my_temp_list)>1:
                                name_list = [my_temp_list[0]]
                                position = my_temp_list[1]
                            else:
                                name_list = []
                                position = ""
                        else:
                            if text.find("全国人民代表大会常务委员会")!=-1:
                                continue
                            if org in ["中国共产党中央军事委员会"]:
                                temp = re.split("\u3000", text)
                            else:
                                temp = re.split('[：:]', text) # 文本分割，默认短的文本为姓名                               
                            if len(temp) > 1:
                                if pos_pattern.search(temp[0]):
                                    position = temp[0]
                                    name_list = re.split('[、；]', temp[1])
                                else:
                                    position = temp[1]
                                    name_list = re.split('[、；]', temp[0])
                            else:
                                name_list = re.split('[、 ]', text)
                                position = h_title
                    url_list = url_encode(title.xpath(".//a/@href"))
                    for name in name_list:
                        name = remove_parentheses(name)
                        if re.compile("第.*?届").match(name):
                            continue
                        if name.strip():
                            name = name.strip()
                            if len(name)>2:
                                tmp_url = [s for s in url_list if quote_plus(name) in s]
                            elif len(name)==2:
                                tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                            url = tmp_url[0] if tmp_url else ""
                            url = re.sub(r"\?from.*", "", url)
                            if name=="杨晓渡":
                                url = url.strip('"')
                            position_order+=1
                            position = re.sub("\[.*?\]", "", position)
                            info_str = {"parent": "领导机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                            senior_leader.append(info_str)  
        return senior_leader
    return senior_leader
                        
# 政策性银行
def parse_yh(org="", page_content=""):
    senior_leader = []
    # print("*********{}**********".format(org))
    page_content = etree.HTML(page_content)
    create_new = page_content.xpath("//div[@class='errorBox']")
    if create_new:
        print("[ERROR] 无法找到{}".format(org))
        return
    start = False
    h_title = ""
    position = ""
    position_order=0
    # 领导成员
    title_list = page_content.xpath("//div[contains(@class, 'para')]")
    for title in title_list:
        if start == False:
            if org in  ["中国农业银行"]: 
                leader_text = title.xpath("./h3/text()")  # leader_text = title.xpath("./h3[@class='title-text']/text()")
                leader_text = "".join(leader_text)
                if leader_text and leader_text.strip() in ["现任管理层","管理层", '高级管理层','现任领导']:#["管理人员", "现任领导", "高级管理层", "管理层","董事会", "现任管理层"]: # 找到文章段落，开始解析
                    tr_list = title.xpath("./following-sibling::div[1]//table//tr")
                    for tr in tr_list[1:]:
                        text_list = tr.xpath("./td[2]//text()")
                        name_list = join_text(text_list).strip()
                        name_list = name_list.split("、")
                        url_list = tr.xpath("./td[2]//a/@href")
                        url_list = url_encode(url_list)
                        for name in name_list:
                            name = re.sub("\[.*?\]| |\(.*?\)|（.*?）", "", name)
                            if name == "空缺":
                                continue                           
                            if len(name)>2:
                                url = [s for s in url_list if (quote_plus(name) in s)]
                            elif len(name)==2:
                                url = [s for s in url_list if quote_plus(name) in s or (quote(name[1]) in s and quote(name[0]) in s)]
                            if 'url' not in locals():
                                url = []
                            url = url[0] if url else ""
                            if name == "":
                                name = join_text(tr.xpath("./td[2]//text()")).strip()
                            else:
                                position = join_text(tr.xpath("./td[1]//text()")).strip()
                            name = remove_parentheses(name)
                            if name.strip():
                                if re.compile("第.*?届").match(name):
                                    continue
                                position_order += 1
                                position = re.sub("\xa0 | \[.*?\]", "", position).strip()
                                info_str = {"parent": "政策性银行", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                senior_leader.append(info_str)                                   
                    return senior_leader

            else:
                if org in ["中国银行", "亚洲基础设施投资银行","中国农业发展银行","国家开发银行", "中国工商银行", '中国建设银行']:
                    leader_text = title.xpath("./h2/text()")
                else:
                    leader_text = title.xpath("./h2[@class='title-text']/text()")
                leader_text = join_text(leader_text)
                
                all_h2_text = ["领导成员", "现任领导", "管理人员", "管理团队", "领导信息", "企业领导","集团领导","公司领导","领导班子","机构领导","主要领导","领导团队"]
                if leader_text in all_h2_text: # 找到文章段落，开始解析
                    start = True
                    if org in ["亚洲基础设施投资银行"]:
                        tr_list = title.xpath("./following-sibling::div[1]//table//tr")
                        for tr in tr_list[1:]:
                            text_list = tr.xpath("./td[2]//text()")
                            name_list = join_text(text_list).strip()
                            name_list = name_list.split("、")
                            url_list = tr.xpath("./td[2]//a/@href")
                            url_list = url_encode(url_list)
                            for name in name_list:
                                name = re.sub("\[.*?\]| |\(.*?\)|（.*?）", "", name)
                                if name == "空缺":
                                    continue                           
                                if len(name)>2:
                                    # if url_list:
                                    #     print(unquote(url_list[0]))
                                    url = [s for s in url_list if (quote_plus(name) in s)]
                                elif len(name)==2:
                                    url = [s for s in url_list if quote_plus(name) in s or (quote(name[1]) in s and quote(name[0]) in s)]
                                if 'url' not in locals():
                                    url = []
                                    print("no url=>", org)
                                url = url[0] if url else ""
                                if name == "":
                                    name = join_text(tr.xpath("./td[2]//text()")).strip()
                                else:
                                    position = join_text(tr.xpath("./td[1]//text()")).strip()
                                name = remove_parentheses(name)
                                if name.strip():
                                    if re.compile("第.*?届").match(name):
                                        continue
                                    position_order += 1
                                    position = re.sub("\xa0 | \[.*?\]", "", position).strip()
                                    info_str = {"parent": "政策性银行", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str)                                   
                        return senior_leader
                    if org in ['中国银行']:
                        tr_list = title.xpath("./following-sibling::div[1]//table//tr")
                        position_order = 0
                        for idx, tr in enumerate(tr_list[1:]):
                            td_text = "###".join(tr.xpath(".//text()"))
                            td_text = re.sub(r"###.*", "", td_text)
                            split_td_text = td_text.split(" ")
                            url_list = tr.xpath(".//a/@href")
                            if len(split_td_text)>1:
                                if re.search(pos_pattern, split_td_text[0]):
                                    position = split_td_text[0]
                                    name_list = split_td_text[1]
                                else:
                                    position = split_td_text[1]
                                    name_list = split_td_text[0]
                                split_name_list = name_list.split("、")
                                
                                for name in split_name_list:
                                    name = remove_parentheses(name)
                                    if not name.strip():
                                        continue
                                    position_order+=1
                                    url = [s for s in url_list if (quote_plus(name) in s)]
                                    url = url[0] if url else ""
                                    info_str = {"parent": "政策性银行", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str) 
                        return senior_leader
        else:
            if title.xpath("./h3"):
                # print(title.xpath("./h3/text()").get()) # 获得职务名称：书记、副书记
                h_title = title.xpath("./h3/text()")
                if h_title in ["历任领导"]:
                    start = False
                    break
            elif title.xpath("./h2"):
                start = False # 找到下一个文章段落，停止解析
                break
            else:
                # text_list = title.xpath(".//text()").getall() # 获得每一条文本
                text_list = title.xpath("./text() | ./*[not(contains(@class, 'layout-right'))]//text()") # 获得每一条文本
                text = join_text(text_list)
                if re.search("资料|更新", text):
                    continue
                temp = re.split('[：:]', text) # 文本分割，默认短的文本为姓名
                if org in ["中国农业银行"] and len(temp)<2:
                    continue
                if len(temp) > 1:
                    if pos_pattern.search(temp[0].replace(" ", "")):
                        position = temp[0].replace(" ", "")
                        name_list = re.split('[、；，]', temp[1].replace(" ", ""))
                    else:
                        position = temp[1].replace(" ", "")
                        name_list = re.split('[、；，]', temp[0].replace(" ", ""))
                    if not position.strip():
                        position = h_title
                else:
                    name_list = re.split('[、 ]', text)
                    position = h_title
                url_list = title.xpath(".//a/@href")
                url_list = url_encode(url_list)
                for name in name_list:
                    name = remove_parentheses(name)
                    if re.compile("第.*?届").match(name):
                        continue
                    if name.strip():
                        name = re.sub("\xa0", "", name)
                        position_order += 1
                        if len(name)>2:
                            tmp_url = [s for s in url_list if quote_plus(name) in s]
                        elif len(name)==2:
                            tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                        url = tmp_url[0] if tmp_url else ""
                        position = re.sub("\xa0|\[.*?\]", "", position).strip()
                        info_str = {"parent": "政策性银行", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                        senior_leader.append(info_str)                  
    return senior_leader

# 中管高校
def parse_school( org="", page_content=""):
    senior_leader = []
    # print("*********{}**********".format(org))
    if page_content == "":
        print("------{}.page is empty!------".format(org))
        return senior_leader
    page_content = etree.HTML(page_content)
    create_new = page_content.xpath("//div[@class='errorBox']")
    if create_new:
        print('[ERROR] 无法找到{}'.format(org))
        return senior_leader

    start = False
    h_title = ""
    position = ""
    position_order = 0
    # 领导成员
    title_list = page_content.xpath("//div[contains(@class, 'para')]")
    for title in title_list:
        if start == False:
            if org in ["兰州大学", "四川大学", "西北工业大学", "华中科技大学", "重庆大学", "山东大学", "西安交通大学", "武汉大学", "中国科学技术大学", "浙江大学", "南京大学", \
                        "同济大学", "哈尔滨工业大学", "复旦大学", "大连理工大学", "天津大学", "吉林大学", "北京师范大学", "北京理工大学", "中国人民大学", "北京大学","北京航空航天大学", \
                        "东南大学", "厦门大学", "上海交通大学", "清华大学", "南开大学",
                        "西北农林科技大学", "中国农业大学", "中山大学"]:
                # leader_text = "".join(title.xpath("./h3[@class='title-text']/text()"))
                leader_text = "".join(title.xpath("./h3/text()"))
                if leader_text and leader_text.strip() in ["现任领导"]:  # 找到文章段落，开始解析
                    start = True
                    if org in ["兰州大学", "四川大学", "西北工业大学", "华中科技大学", "重庆大学", "山东大学", "西安交通大学", "武汉大学", "中国科学技术大学","浙江大学", \
                                "南京大学", "同济大学", "哈尔滨工业大学", "复旦大学", "大连理工大学", "天津大学", "吉林大学", "北京师范大学", "北京理工大学","中国人民大学", \
                                "北京大学", "东南大学", "厦门大学", "上海交通大学", "清华大学", "南开大学",
                                "西北农林科技大学", "中国农业大学", "中山大学"]:
                        # tr_list = title.xpath("./following-sibling::table[1]//tr")
                        tr_list = title.xpath(".//following::table[1]//tr")
                        print("tr_list={}".format(len(tr_list)))
                        if tr_list:
                            tr_end_text = "".join(tr_list[-1].xpath("//td//text()"))
                            # print(re.search("截止|截至|参考|资料", tr_end_text))
                            tr_end_index = len(tr_list) if re.search("截止|截至|参考|资料", tr_end_text) else len(tr_list)+1 #确定是否要table的最后一行信息
                            # print("tr_end_index=", tr_end_index, "len_tr_list=", len(tr_list))
                            for tr_idx, tr in enumerate(tr_list[1:tr_end_index]):
                                td_1_text = join_text(tr.xpath("./td[1]//text()")).strip()
                                if re.search(pos_pattern, td_1_text):
                                    position = join_text(tr.xpath("./td[1]//text()")).strip()
                                    text_list = tr.xpath("./td[2]//text()")
                                    url_list = tr.xpath('./td[2]//a/@href')
                                else:
                                    position = join_text(tr.xpath("./td[2]//text()")).strip()
                                    text_list = tr.xpath("./td[1]//text()")
                                    url_list = tr.xpath('./td[1]//a/@href')
                                # print(text_list)
                                # print(url_list)
                                if org in ['西安交通大学']:
                                    name_list = re.split('[ 、]', join_text(text_list))
                                else:
                                    name_list = re.split("、", join_text(text_list).strip())

                                for name in name_list:
                                    name = remove_parentheses(name)
                                    if name.strip():
                                        if re.compile("第.*?届").match(name):
                                            continue
                                        url_list = url_encode(url_list)
                                        if len(name)>2:
                                            # print(quote_plus(name))
                                            tmp_url = [s for s in url_list if quote_plus(name) in s]
                                        elif len(name)==2:
                                            tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                                        url = tmp_url[0] if tmp_url else ""
                                        url = re.sub(r"\?from.*", "", url)
                                        position_order += 1
                                        position = re.sub("\xa0|\[.*?\]", "", position).strip()
                                        if not position:
                                            continue
                                        info_str = {"parent": "中管高校", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                        senior_leader.append(info_str)                                      
                            return senior_leader
                    if org in ["北京航空航天大学"]:
                        tr_list = title.xpath("./following::table[1]//tr")
                        for tr in tr_list[1:]:
                            text_list = tr.xpath("./td[2]//text()")
                            text = join_text(text_list).strip()
                            if re.search("参考|资料", text):
                                continue
                            name_list = re.split("、", text)
                            position = join_text(tr.xpath("./td[1]//text()")).strip()
                            for name in name_list:
                                name = remove_parentheses(name)
                                if name.strip():
                                    if re.compile("第.*?届").match(name):
                                        continue
                                    url_list = tr.xpath('.//a/@href') 
                                    url_list = url_encode(url_list)  
                                    if len(name)>2:
                                        tmp_url = [s for s in url_list if quote_plus(name) in s]
                                    elif len(name)==2:
                                        tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]                                   
                                    url= tmp_url[0] if tmp_url else ""                                          
                                    position_order += 1
                                    position = re.sub("\xa0|\[.*?\]", "", position).strip()
                                    info_str = {"parent": "中管高校", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str)                                      
                        return senior_leader
                    
            else:
                leader_text = "".join(title.xpath("./h2[@class='title-text']/text()"))
                leader_text = "".join(title.xpath("./h2[@name]/text()")) if org not in ['中南大学'] else "".join(title.xpath("./h2/text()")) 
                if leader_text in ["行政管理"]:  # 找到文章段落，开始解析
                    # print(leader_text)
                    start = True
                    if org in ["中南大学"]:
                        # tr_list = title.xpath("./following-sibling::table[1]//tr")
                        tr_list = title.xpath("./following::table[1]//tr")
                        for tr in tr_list[1:]:
                            position = join_text(tr.xpath("./td[1]//text()")).strip()
                            position = re.sub("\xa0", "", position).strip()
                            if re.search("参考|资料", position):
                                continue

                            text_list = tr.xpath("./td[2]//text()")
                            name_list = re.split("、", join_text(text_list).strip())

                            for name in name_list:
                                name = remove_parentheses(name)
                                if re.compile("第.*?届").match(name):
                                        continue
                                if name.strip():
                                    url_list = tr.xpath('.//a/@href')
                                    url_list = url_encode(url_list)
                                    if len(name)>2:
                                        tmp_url = [s for s in url_list if quote_plus(name) in s]
                                    elif len(name)==2:
                                        tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                                    url = tmp_url[0] if tmp_url else ""
                                    position_order += 1
                                    position = re.sub("\[.*?\]", "", position)
                                    info_str = {"parent": "中管高校", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str)               
                        return senior_leader
        # 
        else:
            if title.xpath("./h3"):
                # print(title.xpath("./h3/text()").get()) # 获得职务名称：书记、副书记
                h_title = title.xpath("./h3/text()")
                if h_title in ["历任领导", "历任校长"]:
                    start = False
                    break
            elif title.xpath("./h2"):
                start = False  # 找到下一个文章段落，停止解析
                break
            else:
                # text_list = title.xpath(".//text()").getall() # 获得每一条文本
                text_list = title.xpath("./text() | ./*[not(contains(@class, 'layout-right'))]//text()")  # 获得每一条文本
                # print(title.get())
                # 拼接文本
                text = join_text(text_list)
                if re.search("资料|更新", text):
                    continue
                temp = re.split('[：:]', text)  # 文本分割，默认短的文本为姓名
                if len(temp) > 1:
                    if pos_pattern.search(temp[0].replace(" ", "")):
                        position = temp[0].replace(" ", "")
                        name_list = re.split('[、；，]', temp[1].replace(" ", ""))
                    else:
                        position = temp[1].replace(" ", "")
                        name_list = re.split('[、；，]', temp[0].replace(" ", ""))
                    if not position.strip():
                        position = h_title
                else:
                    name_list = re.split('[、 ]', text)
                    position = h_title
                for name in name_list:
                    name = remove_parentheses(name)
                    if name.strip():
                        if re.compile("第.*?届").match(name):
                            continue
                        url_list = url_encode(title.xpath('./a/@href'))
                        if len(name)>2:
                            tmp_url = [s for s in url_list if quote_plus(name) in s]
                        elif len(name)==2:
                            tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                        url = tmp_url[0] if tmp_url else ""
                       
                        position_order += 1
                        position = re.sub("\xa0|\[.*?\]", "", position).strip()
                        # print_temp(org, name, position, position_order, url)
                        info_str = {"parent": "中管高校", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                        senior_leader.append(info_str)   

    return senior_leader
                        
# 国务院机构
def parse_gov(org="", page_content=""):
    senior_leader = []
    # print("*********{}**********".format(org))

    if page_content=="":
        print("------{}.page is empty!------".format(org))
        return senior_leader
    page_content = etree.HTML(page_content)
    create_new = page_content.xpath("//div[@class='errorBox']")
    if create_new:
        print('[ERROR] 无法找到{}'.format(org))
        return senior_leader

    start = False
    h_title = ""
    position = ""
    position_order = 0

    title_list = page_content.xpath("//div[contains(@class, 'para')]")
    s_idx = 0
    e_idx = len(title_list)
    for div_idx, title in enumerate(title_list):
        if start==False:
            if org in ["中央广播电视总台", "中国工程院", "中国社会科学院", "中国科学院", "新华通讯社", "国家认证认可监督管理委员会", "国务院参事室", '科学技术部', '退役军人事务部', '国家市场监督管理总局','人民银行']:
                leader_text = title.xpath("./h3/text()") if org not in ["科学技术部", '退役军人事务部', '国家市场监督管理总局'] else title.xpath("./h2/text()")
                leader_text = "".join(leader_text)

                if leader_text and leader_text.strip() in ["现任领导", "领导信息", "领导机构"]:
                    start = True
                    if org in ["中国社会科学院", "中国工程院", "中国科学院", '国家市场监督管理总局','人民银行']:
                        tr_list = title.xpath("./following::table[1]//tr")
                        for tr in tr_list[1:]:
                            text_list = tr.xpath("./td[2]//text()")
                            text = join_text(text_list)
                            print("---{}".format(text))
                            if re.search("参考|资料|空缺", text):
                                continue
                            name_list = re.split("、", text)
                            url_list = tr.xpath("./td[2]//a/@href")
                            position = join_text(tr.xpath("./td[1]//text()")).strip()
                            url_list = url_encode(url_list)
                            for name in name_list:
                                name = remove_parentheses(name).strip()
                                if re.compile("第.*?届").match(name):
                                        continue
                                if name and position:
                                    if re.compile("第.*?届").match(name):
                                        continue
                                    
                                    url = [s for s in url_list if quote_plus(name) in s]
                                    url = url[0] if url else ""
                                    # url = re.sub(r"\?from.*", "", url)
                                    position_order += 1
                                    position = position.strip()
                                    position = re.sub("\xa0|\[.*?\]|。$", "", position)
                                    info_str = {"parent": "国务院机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str)   
                        return senior_leader    
                    if org in ["中央广播电视总台", '科学技术部', '退役军人事务部']:
                        tr_list = title.xpath("./following::table[1]//tr")
                        for tr in tr_list[1:]:
                            text_list = tr.xpath("./td[1]//text()")
                            name = join_text(text_list).strip()
                            if name == "空缺":
                                continue
                            url_list = url_encode(tr.xpath("./td[1]//a/@href"))
                            if name == "":
                                name = join_text(tr.xpath("./td[2]//text()")).strip()
                            else:
                                position = join_text(tr.xpath("./td[2]//text()")).strip()
                            name = remove_parentheses(name).strip()
                            if name and position:
                                if re.compile("第.*?届").match(name):
                                        continue
                                position_order += 1
                                position = position.strip()
                                position = re.sub("\xa0|\[.*?\]|。$", "", position)
                                url = "".join([url for url in url_list if quote_plus(name) in url])
                                info_str = {"parent": "国务院机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                senior_leader.append(info_str) 
                        return senior_leader                         
            else:
                leader_text = title.xpath("./h2/text()") if org in ["国家数据局", '国家广播电视总局','外交部','国防部', '工业和信息化部','国家民族事务委员会','公安部','国家安全部','财政部','自然资源部','生态环境部','住房和城乡建设部','商务部','国家卫生健康委员会','海关总署','国家金融监督管理总局','应急管理部','国家外国专家局','国家航天局','国家原子能机构','国家税务总局','国家体育总局','国家统计局','国家国际发展合作署','国家医疗保障局','国家机关事务管理局','国家新闻出版署','国家宗教事务局','国务院港澳事务办公室','国务院台湾事务办公室','国家互联网信息办公室','国务院新闻办公室','国家行政学院','国家粮食和物资储备局','国家烟草专卖局','国家移民管理局','国家林业和草原局','国家铁路局','国家乡村振兴局','国家文物局','国家外汇管理局','国家药品监督管理局','国家知识产权局','中华人民共和国出入境管理局','国家公园管理局','国家公务员局','国家档案局','国家密码管理局','文化和旅游部','中央全面依法治国委员会','教育部','民政部','司法部','人力资源和社会保障部','交通运输部','水利部','农业农村部','审计署','国家语言文字工作委员会','国家核安全局','国务院国有资产监督管理委员会','海关总署','国家电影局','国务院研究室','国务院侨务办公室','国务院发展研究中心','中国气象局','中国证券监督管理委员会','国家信访局','国家能源局','国家国防科技工业局','中国民用航空局','国家邮政局','国家中医药管理局','国家矿山安全监察局','国家保密局','国家金融监督管理总局','国家标准化管理委员会','国家发展和改革委员会'] else title.xpath("./h2[@name]/text()")
                # leader_text = title.xpath("./h2/text()")
                leader_text = "".join(leader_text)
                # print("h_title:", leader_text)
                if org in ["国家宗教事务局"]:
                    h2_title = ["历任领导"]
                else:
                    h2_title = ["领导成员", "现任领导", "领导干部", "领导分工", "机构领导", "侨办领导", "主要领导","机关领导", "军委领导"]
                if leader_text in h2_title:  # 找到文章段落，开始解析
                    start = True
                    if org in ["国家卫生健康委员会","海关总署","国家金融监督管理总局"]:
                        tr_list = title.xpath("./following::table[1]//tr")
                        position_order = 0
                        tr_start_idx = 1 if org not in ["国家金融监督管理总局"] else 0
                        for tr in tr_list[tr_start_idx:]:
                            td_list = tr.xpath("./td")
                            if not(td_list and len(td_list)>1):
                                continue
                            for td_idx, td in enumerate(td_list[::2]):
                                position = "".join(tr_list[0].xpath("./th[1]//text()")).strip() if td_idx<2 else "".join(tr_list[0].xpath("./th[2]//text()")).strip()
                                position = "{}{}".format(position, "".join(td.xpath(".//text()")).strip())
                                name_text = "".join(td_list[td_idx+1].xpath(".//text()")).strip()
                                name_list = name_text.split("、")
                                url_list = url_encode(td_list[td_idx+1].xpath(".//a/@href"))
                                for name in name_list:
                                    position = re.sub("^职务", "", position)
                                    name = re.sub('^空缺', '', name)
                                    if name and position:
                                        position_order += 1
                                        url = [s for s in url_list if quote_plus(name) in s]
                                        url = url[0] if url else "" 
                                        info_str = {"parent": "国务院机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                        senior_leader.append(info_str)
                        return senior_leader
                    
                    if org in ["国家国际发展合作署"]:
                        tr_list = title.xpath("./following::table[1]//tr")
                        position_order = 0
                        pre_position = ""
                        for tr in tr_list[1:]:
                            td_list = tr.xpath("./td")
                            url_list = tr.xpath(".//a/@href")
                            url_list = url_encode(url_list)
                            if len(td_list)>1:
                                position = "".join(td_list[0].xpath(".//text()")).strip()
                                name_list = "".join(td_list[1].xpath(".//text()"))
                                pre_position = position
                            else:
                                position = pre_position
                                name_list = "".join(td_list[0].xpath(".//text()"))
                            name_list = name_list.split("、")
                            for name in name_list:
                                name = re.sub("\[.*?\]", "", name.strip())
                                position = re.sub("\[.*?\]", "", position.strip())
                                if re.search("参考资料|参考材料", name) or re.search("参考资料|参考材料", position):
                                    continue
                                if name and position:
                                    position_order+=1
                                    url = [s for s in url_list if quote_plus(name) in s]
                                    url = url[0] if url else "" 
                                    info_str = {"parent": "国务院机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str)
                        return senior_leader

                    if re.search("农业农村部|国家发展和改革委员会", org):#org in ["农业农村部","中华人民共和国农业农村部"]:#["中华人民共和国农业农村部"]:
                        tr_list = title.xpath("./following::table[1]//tr")
                        for tr in tr_list[1:]:
                            text_list = tr.xpath("./td[2]//text()")
                            name_list = join_text(text_list).strip()
                            name_list = re.split('、', name_list)
                            url_list = url_encode(tr.xpath("./td[2]//a/@href"))      
                            if name_list:
                                        position = join_text(tr.xpath("./td[1]//text()")).strip()               
                            if name_list and position:
                                for name in name_list:
                                    if re.compile("第.*?届").match(name):
                                        continue                                  
                                    name = remove_parentheses(name).strip()
                                    position_order += 1
                                    position = re.sub("\xa0", "", position)
                                    position = re.sub("\[.*?\]|。$", "", position)
                                    url = [s for s in url_list if quote_plus(name) in s]
                                    url = url[0] if url else ""
                                    # print_temp(org, name, position, position_order, url)     
                                    info_str = {"parent": "国务院机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str)    
                        return senior_leader                                             
                
                    if re.search("水利部|住房和城乡建设部|民政部", org):#org in ["水利部", "中华人民共和国水利部","住房和城乡建设部", "中华人民共和国住房和城乡建设部"]: #["水利部", "中华人民共和国住房和城乡建设部"]:
                        tr_list = title.xpath("./following::table[1]//tr")
                        for tr in tr_list[1:]:
                            text_list = tr.xpath("./td[1]//text()")
                            # name_list = re.split("、", join_text(text_list).strip())
                            name = join_text(text_list).strip()

                            url_list = url_encode(tr.xpath("./td[1]//a/@href"))
                            
                            if name == "":
                                name = join_text(tr.xpath("./td[2]//text()")).strip()
                            else:
                                position = join_text(tr.xpath("./td[2]//text()")).strip()
                                position = re.sub("\[.*?\]", "", position)
                            name = remove_parentheses(name).strip()
                            name = re.sub("^空缺", "", name)
                            if name and position:
                                if re.compile("第.*?届").match(name):
                                    continue
                                # print(name, position, url)
                                position_order += 1
                                position = re.sub("\xa0|。$", "", position)
                                url = [s for s in url_list if quote_plus(name) in s]
                                url = url[0] if url else ""
                                info_str = {"parent": "国务院机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                senior_leader.append(info_str)   
                        return senior_leader         
                
        else:
            if org in ['国家新闻出版署']:
                if title.xpath("./h3"):
                    # print(title.xpath("./h3/text()").get()) # 获得职务名称：书记、副书记
                    h_title = title.xpath("./h3/text()")
                    # h_title = ElementTree.tostring(h_title).decode()
                    h_title = "".join(h_title)
                    if h_title in ["历任部长", "历任行长", "历任主席", "中心负责人", "历任院长", "历任领导", "人员编制"]:
                        start = False
                        e_idx = div_idx
                        break
                elif title.xpath("./h2"):
                    start = False  # 找到下一个文章段落，停止解析
                    e_idx = div_idx
                    break
                else:
                    pass
                leader_div = title_list[e_idx - 1].xpath('./text() | ./*[not(contains(@class, "layout-right"))]//text()')
                text = join_text(leader_div)
                temp = re.split('[：:]', text)  # 文本分割，默认短的文本为姓名
                if len(temp) > 1:
                    if pos_pattern.search(temp[0]):
                        position = "署长/局长"
                        name_list = re.split('、', temp[1])
                    else:
                        position = "署长/局长"
                        name_list = re.split('、', temp[0])
                    for name in name_list:
                        name = remove_parentheses(name).replace('\u3000', '').strip()
                        name = re.sub("^空缺",'', name)
                        if name and position:
                            if re.compile("第.*?届").match(name):
                                continue
                            url_list = url_encode(title.xpath(".//a/@href"))
                            url = [url for url in url_list if quote_plus(name) in url]
                            url = url[0] if url else ""
                            position_order += 1
                            position = position.strip()
                            position = re.sub("\xa0|\[.*?\]|。$", "", position)
                            # print_temp(org, name, position, position_order, url)
                            info_str = {"parent": "国务院机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                            senior_leader.append(info_str)                          
                return senior_leader

            if title.xpath("./h3"):
                h_title = title.xpath("./h3/text()")
                h_title = "".join(h_title)
                if h_title in ["历任部长", "历任行长", "历任主席", "中心负责人", "历任院长", "历任领导", "人员编制", "历任主任"]:
                    start = False
                    break
            elif title.xpath("./h2"):
                start = False  # 找到下一个文章段落，停止解析
                break
            else:
                text_list = title.xpath("./text() | ./*[not(contains(@class, 'layout-right'))]//text()")  # 获得每一条文本             

                if org in ["国家语言文字工作委员会", "国家知识产权局", "国务院国有资产监督管理委员会", "公安部", "中华人民共和国公安部"]:
                    text = re.sub("（.*?）|\[.*?\]", "", join_text(text_list))
                else:
                    text = join_text(text_list)
                text = text.strip()
                # print(f"{org}:{text}")

                if re.search("资料|更新", text):
                    continue
                if org in ["公安部","中华人民共和国公安部"]: #["中华人民共和国公安部", "国家档案局"]:
                    temp = re.split(' ', text)
                elif org in ["中国气象局","国务院参事室"]:
                    temp = re.split('：', text)
                elif org in ["国家档案局"]:
                    text = re.sub("\xa0", "", text)
                    temp = re.split('\u3000| ', text)
                else:
                    temp = re.split('[：:]', text)  # 文本分割，默认短的文本为姓名
                if len(temp) > 1:
                    if pos_pattern.search(temp[0]):
                        position = temp[0]
                        name_list = re.split('、', temp[-1])
                    else:
                        position = temp[-1]
                        name_list = re.split('、', temp[0])
                else:
                    if org in ["国家统计局", "中华人民共和国生态环境部"]:
                        continue
                    if org in ['国家体育总局']:
                        if pos_pattern.search(text):
                            position = text
                        else:
                            name_list = [text]
                            continue
                    else:
                        name_list = re.split('[、, ]', text)
                        position = h_title
                for name in name_list:
                    name = remove_parentheses(name).replace('\u3000', '').strip()
                    name = re.sub("\ufeff|暂缺|空缺", "", name)
                    if name and position:
                        if re.compile("第.*?届").match(name):
                            continue
                        url_list = url_encode(title.xpath(".//a/@href"))
                        if len(name)==2:
                            text_with_space = name[0] + "　" + name[1]
                            url = [url for url in url_list if quote_plus(text_with_space) in url or text_with_space in url or quote_plus(name) in url or name in url]
                        else:
                            url = [url for url in url_list if quote_plus(name) in url or name in url]
                        url = url[0] if url else ""
                        position_order += 1
                        position = re.sub(r"\xa0|\u3000|\[.*?\]|。$", "", position)
                        # position = re.sub(f"^{org}", "", position)
                        info_str = {"parent": "国务院机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                        senior_leader.append(info_str)               
    return senior_leader

# 国家级协会
def parse_xh(org="", page_content=""):
    senior_leader=[]
    # print("*********{}**********".format(org))
    page_content = etree.HTML(page_content)
    create_new = page_content.xpath("//div[@class='errorBox']")
    if create_new:
        print("[ERROR] 无法找到{}".format(org))
        return senior_leader
    
    start = False
    h_title = ""
    position = ""
    position_order=0
    # 领导成员
    title_list = page_content.xpath("//div[contains(@class, 'para')]")
    for title in title_list:
        if start == False:
            if org in ["黄埔军校同学会", "欧美同学会", "中华全国新闻工作者协会", "宋庆龄基金会", "中华全国总工会", "中国红十字总会", "全国侨联", "中国作协", "中国文联", "中国记协", "贸促会", '对外友协']:
                leader_text = title.xpath("./h3/text()")
                leader_text = "".join(leader_text)
                if leader_text and leader_text.strip() in ["现任领导"]: # 找到文章段落，开始解析
                    start = True
                    if org in ["中国红十字总会", "全国侨联", "中国记协", "贸促会"]:
                        tr_list = title.xpath("./following::table[1]//tr")
                        for tr in tr_list[1:]:
                            position = join_text(tr.xpath("./td[1]//text()")).strip()
                            if re.search("参考|资料", position):
                                continue

                            text_list = tr.xpath("./td[2]//text()")
                            name_list = re.split("、", join_text(text_list).strip())
                            # print(name_list)
                            
                            for name in name_list:
                                name = remove_parentheses(name)
                                if org in ['中国科协']:
                                    name = re.sub('\(.*', "", name)
                                    if name.startswith('苗族'):
                                        continue
                                if name.strip() and not name.startswith("苗族"):
                                    # url = get_href(tr.get(), name)
                                    position_order += 1
                                    position = (re.sub("[\xa0|\[.*?\]]", "", position)).strip()
                                    url_list = tr.xpath(".//a/@href")
                                    url_list = url_encode(url_list)
                                    if len(name)>2:
                                        tmp_url = [s for s in url_list if quote_plus(name) in s]
                                    elif len(name)==2:
                                        tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                                    url = tmp_url[0] if tmp_url else ""
                                    info_str = {"parent": "国家级协会", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str)   
                        return senior_leader

                    if org in ["中国作协"]:
                        tr_list = title.xpath("./following::table[1]//tr")
                        for tr in tr_list[1:]:
                            if len(tr.xpath("./td")) == 3:
                                position = join_text(tr.xpath("./td[2]//text()")).strip()
                                if re.search("参考|资料", position):
                                    continue

                                text_list = tr.xpath("./td[3]//text()")
                                name_list = re.split('[、\xa0]', join_text(text_list).strip())
                            else:
                                position = join_text(tr.xpath("./td[1]//text()")).strip()
                                if re.search("参考|资料", position):
                                    continue

                                text_list = tr.xpath("./td[2]//text()")
                                name_list = re.split('[、]', join_text(text_list).strip())
                            
                            for name in name_list:
                                name = remove_parentheses(name)
                                if name.strip():
                                    url_list = tr.xpath(".//a/@href")
                                    url_list = url_encode(url_list)
                                    if len(name)>2:
                                        tmp_url = [s for s in url_list if quote_plus(name) in s]
                                    elif len(name)==2:
                                        tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                                    url = tmp_url[0] if tmp_url else ""
                                    position_order += 1
                                    position = re.sub("\xa0|\[.*?\]", "", position).strip()
                                    info_str = {"parent": "国家级协会", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str)             
                        return senior_leader
            elif org in ["中国科协"]:
                leader_text = "".join(title.xpath("./h2/text()"))
                if leader_text in ['现任领导', '科协领导']:
                    start=True
            else:
                if org in ["全国妇联"]:
                    if start == False:
                        leader_text = "".join(title.xpath("./h3/text()"))
                        if leader_text in ['现任领导']:
                            start = True
                            next_div = title.xpath("./following::table[1]")
                            tr_list = next_div[0].xpath(".//tr")
                            for tr in tr_list[1:]:
                                position = join_text(tr.xpath("./td[1]//text()")).strip()
                                if re.search("参考|资料", position):
                                    start = False
                                    continue
                                text_list = tr.xpath("./td[2]//text()")
                                # print(text_list)
                                name_list = re.split("、", "、".join(text_list))
                                url_list = tr.xpath("./td[2]//a/@href")
                                url_list = url_encode(url_list)
                                for name in name_list:
                                    name = remove_parentheses(name)
                                    name = re.sub("\u3000|（兼", "", name).strip()
                                    if not name:
                                        continue
                                    if len(name)>2:
                                        tmp_url = [s for s in url_list if quote_plus(name) in s]
                                    elif len(name)==2:
                                        tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                                    url = tmp_url[0] if tmp_url else ""
                                    position_order += 1
                                    position = re.sub("\xa0|\[.*?\]", "", position).strip()
                                    info_str = {"parent": "国家级协会", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str) 
                            return senior_leader
                    else:
                        h_title= "".join(title.xpath("./h3/text()"))
                        if h_title in ["历任领导","历任主席"]:
                            start = False
                            break

                else:
                    leader_text = "".join(title.xpath("./h2/text()"))
                    if leader_text in ["现任领导", "领导集体"]: # 找到文章段落，开始解析
                        start = True
                        if org in ["中国法学会", "中国残联"]:  # ["中国法学会"]:#
                            table_num = 1 if org in ["中国法学会"] else 2
                            for table_idx in list(range(1, table_num+1, 1)):
                                tr_list = title.xpath(f"./following::table[{table_idx}]//tr")                               
                                for tr in tr_list[1:]:
                                    position = join_text(tr.xpath("./td[1]//text()")).strip() if org in ["中国法学会"] else join_text(tr.xpath("./th//text()")).strip()
                                    if re.search("参考|资料", position):
                                        continue
                                    text_list = tr.xpath("./td[2]//text()") if org in ["中国法学会"] else tr.xpath("./td//text()")
                                    name_list = re.split("、|#", "#".join(text_list).strip())
                                    url_list = url_encode(tr.xpath(".//a/@href"))
                                    for name in name_list:
                                        name = remove_parentheses(name)
                                        name = re.sub("。", "", name)
                                        if name.strip():
                                            if len(name)>2:
                                                tmp_url = [s for s in url_list if quote_plus(name) in s]
                                            elif len(name)==2:
                                                tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                                            url = tmp_url[0] if tmp_url else ""
                                            position_order += 1
                                            position = re.sub("\xa0|\[.*?\]|（按姓氏笔画为序）", "", position).strip()
                                            info_str = {"parent": "国家级协会", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                            senior_leader.append(info_str) 
                            return senior_leader
        else:
            if title.xpath("./h3"):
                h_title = "".join(title.xpath("./h3/text()"))
                if h_title in ["历任领导", "历任主席","历届主席", "历任主席"]:
                    start = False
                    break
                if org in ['对外友协']:
                    if h_title in ["名誉会长"]:
                        start=False
                        break
            elif title.xpath("./h2"):
                start = False # 找到下一个文章段落，停止解析
                break   
            else:                   
                # text_list = title.xpath(".//text()").getall() # 获得每一条文本
                text_list = title.xpath("./text() | ./*[not(contains(@class, 'layout-right'))]//text()") # 获得每一条文本
                # 拼接文本
                text = join_text(text_list)
                if org in ["欧美同学会"] and "理事" in text:
                    break
                
                if org in ["全国台联", "欧美同学会","中国职工思想政治工作研究会"]: #"中国残联",
                    text = re.sub("\(.*?\)|（.*?）|\[.*?\]|（.*?）", "", text)
                if org in ["中国文联"]:
                    text = re.sub("\(.*?\)|（.*?）|\[.*?\]|（.*?）", " ", text)
                if re.search("资料|更新", text) or not text:
                    continue
                temp = re.split('[：:]', text) # 文本分割，默认短的文本为姓名
                # print("text : ", text)
                # print("temp : ", temp)
                
                if org in ["中国文联"]:
                    if len(temp)<2:
                        return senior_leader
                    elif temp[-1]=="":
                        continue
                    else:
                        pass
                if org in ["全国台联"]:
                    if temp[0] in ["全国台联第十一届理事会会长、副会长名单"]:
                        continue
                    elif temp[0] in ["全国台联第十一届理事会常务理事名单"]:
                        position = "常务理事"                
                    elif text.find("：")!=-1:
                        position = temp[0].replace(" ", "")
                    if position in ["副会长"]:
                        if text.find("：")!=-1:
                            continue
                        else:
                            name_list = re.split("[、]", text)
                    elif position in ["常务理事"]:
                        if text.find("、")!=-1:
                            name_list = re.split("[、]", text)
                        else:
                            continue
                    else:
                        if len(temp)>1:
                            position = temp[0].replace(" ", "")
                            name_list = re.split("[ ]", temp[1])
                        else:
                            continue
            
                if len(temp) > 1:
                    if pos_pattern.search(temp[0].replace(" ", "")):
                        position = temp[0].replace(" ", "")
                        if org in ["中国文联"]:
                            name_list = re.split('[ 、]', temp[1])
                            position = remove_parentheses(position)
                        else:
                            name_list = re.split('[、；，]', temp[1].replace(" ", ""))
                    else:
                        position = temp[1].replace(" ", "")
                        name_list = re.split('[、；，]', temp[0].replace(" ", ""))
                    if not position.strip():
                        position = h_title
                else:
                    if org in ['中华全国总工会']:
                        if pos_pattern.search(text):
                            position = text
                            continue
                        else:
                            name_list = re.split('[、 ]', text)   #[text]
                    elif org in ["中国职工思想政治工作研究会"]:
                        continue
                    else:
                        if org not in ["全国台联"]:
                            name_list = re.split('[、 ]', text)
                            position = h_title
                for name in name_list:
                    if (re.compile("第.*?届")).findall(name):
                        continue
                    if org in ['中国科协']:
                        if name.startswith("苗族"):
                            continue
                        name = re.sub(r'\(.*', '', name)
                        if not name:
                            continue
                        
                    name = remove_parentheses(name)
                    name = re.sub("。", "", name)
                    name = name.strip()
                    if name.find("理事会常务理事")!=-1 or name=="" or name.find("姓名")!=-1 or name.find("职务")!=-1 or name.find("统计截至2023年10月")!=-1:
                        continue
                    url_list = title.xpath(".//a/@href")
                    url_list = url_encode(url_list)
                    if len(name)>2:
                        tmp_url = [s for s in url_list if quote_plus(name) in s]
                    elif len(name)==2:
                        tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                    url = tmp_url[0] if tmp_url else ""
                    url = re.sub(r"\?from.*", "", url)
                    position_order += 1
                    position = re.sub("\xa0|\[.*?\]", "", position).strip()
                    position = re.sub("\（.*?\）", "", position) if org in ["欧美同学会"] else position
                    if name and position:
                        info_str = {"parent": "国家级协会", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                        senior_leader.append(info_str)
                    else:
                        continue 
    return senior_leader
                    

# 央企
def parse_yq(org="", page_content=""):
    senior_leader = []
    # print("*********{}**********".format(org))
    page_content = etree.HTML(page_content)
    create_new = page_content.xpath("//div[@class='errorBox']")
    if create_new:
        print("[ERROR] 无法找到{}".format(org))
        return senior_leader
    
    start = False
    h_title = ""
    position = ""
    position_order = 0
    url = ""
    name_position = []
    # 领导成员
    title_list = page_content.xpath("//div[contains(@class, 'para')]")
    for title_idx in range(len(title_list)):
        title = title_list[title_idx]
        if start == False:
            if org in ["中国海洋石油集团有限公司", "中国航空工业集团有限公司", "资产管理集团有限公司", "中国储备粮管理集团有限公司","华润集团有限公司","中国南方电网有限责任公司", '中国华能集团有限公司', '中国一重集团有限公司','中国东方航空集团有限公司', '中国兵器工业集团有限公司', '中国航天科工集团有限公司', '中国船舶集团有限公司', '中国兵器装备集团有限公司', '中国大唐集团有限公司', '中国中车集团有限公司','中国卫星网络集团有限公司','东风汽车集团有限公司','中国南方航空集团有限公司', '中国核工业集团有限公司']:

                if org in ['资产管理集团有限公司']:
                    leader_text = title.xpath("./h3[@class='title-text']/text()")
                else:
                    leader_text = title.xpath("./h3/text()")
                leader_text = join_text(leader_text)

                if org in ["中国南方电网有限责任公司"]:
                    h3_ls = ["领导信息", '现任领导']
                elif org in ['中国华能集团有限公司']:
                    h3_ls = ["主要领导", "现任领导", "公司领导", "集团领导", "组织体系", "领导团队", '领导成员']
                elif org in ['中国一重集团有限公司']:
                    h3_ls = ['高层管理', '现任领导']
                elif org in ["中国兵器工业集团有限公司",'中国卫星网络集团有限公司']:
                    h3_ls = ["管理层", '现任领导']
                elif org in ['中国兵器装备集团有限公司','中国大唐集团有限公司']:
                    h3_ls = ["管理团队",'现任领导']
                elif org in ['中国中车集团有限公司']:
                    h3_ls = ['集团领导']
                elif org in ['中国核工业集团有限公司']:
                    h3_ls = ['现任领导', '企业领导']
                else:
                    h3_ls = ["主要领导", "现任领导", "公司领导", "集团领导", "组织体系", "领导团队"]
                
                if leader_text and leader_text.strip() in h3_ls: # 找到文章段落，开始解析
                    start = True
                    if org in ["中国海洋石油集团有限公司", "中国储备粮管理集团有限公司", "华润集团有限公司", '中国华能集团有限公司', '中国一重集团有限公司', '中国东方航空集团有限公司', '中国兵器工业集团有限公司','中国航天科工集团有限公司', '中国船舶集团有限公司', '中国兵器装备集团有限公司','中国大唐集团有限公司', '中国中车集团有限公司','中国卫星网络集团有限公司','东风汽车集团有限公司','中国南方航空集团有限公司', '中国核工业集团有限公司']:
                        tr_list = title.xpath("./following::table[1]//tr")

                        # 将公司进行归类：第一列是职务，第二列是姓名的所有公司
                        companies = ["中国海洋石油集团有限公司", "华润集团有限公司", '中国一重集团有限公司', '中国航天科工集团有限公司','中国船舶集团有限公司', '中国兵器装备集团有限公司', '中国中车集团有限公司','中国卫星网络集团有限公司']

                        for tr in tr_list[1:]:
                            all_td_text = "".join(tr.xpath(".//td//text()"))
                            # print('td-text:', all_td_text)
                            if "参考资料" in all_td_text or '更正时间' in all_td_text:
                                continue
                            text_list = tr.xpath("./td[2]//text()") if org in companies else tr.xpath("./td[1]//text()")
                            text = join_text(text_list).strip()
                            if text == "空缺" or text=='暂缺':
                                continue
                            url_lst = tr.xpath("./td[2]//a/@href") if org in companies else tr.xpath("./td[1]//a/@href")
                            url_lst = url_encode(url_lst)                
                            position = join_text(tr.xpath("./td[1]//text()")).strip() if org in companies else join_text(tr.xpath("./td[2]//text()")).strip()   
                            position = position.strip().strip('。')
                            position = re.sub("\xa0|\[.*?]", "", position)
                            name_list = re.split("、", text)
                            if name_list and position:
                                for name in name_list:                                 
                                    position_order += 1
                                    name = re.sub("^(.*公司|集团)", "", name)
                                    name = remove_parentheses(name).strip()
                                    if not name:
                                        continue
                                    # position = re.sub("^(.*公司|集团)|\[.*?\]|。$", "", position)                  
                                    url = [s for s in url_lst if quote_plus(name) in s or name in s]
                                    url = url[0] if url else ""
                                    info_str = {"parent": "央企", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str)
                        return senior_leader
            else:
                if org in ["中国中煤能源集团有限公司", '中国石油化工集团有限公司','中国建材集团有限公司','国家电网有限公司','中国诚通控股集团有限公司','中国稀土集团有限公司','矿冶科技集团有限公司','中国华电集团有限公司','中国长江三峡集团有限公司','国家能源投资集团有限责任公司','中国铁路工程集团有限公司','中国移动通信集团有限公司', '中国信息通信科技集团有限公司','中国建设科技有限公司','中国机械工业集团有限公司','中国冶金地质总局','哈尔滨电气集团有限公司','中国煤炭地质总局','中国东方电气集团有限公司','新兴际华集团有限公司','鞍钢集团有限公司','中国铝业集团有限公司','中国航空器材集团有限公司','中国远洋海运集团有限公司','中国电力建设集团有限公司','中国能源建设集团有限公司','中国安能建设集团有限公司','华侨城集团有限公司','中国建筑集团有限公司','中国电气装备集团有限公司','中国国新控股有限责任公司','中国商用飞机有限责任公司','中国机械科学研究总院集团有限公司','中国航空发动机集团有限公司','中国融通资产管理集团有限公司','中国中钢集团有限公司','中国钢研科技集团有限公司','中国化学工程集团有限公司','中国旅游集团有限公司','中国大唐集团有限公司','中国第一汽车集团有限公司','中国医药集团有限公司','中粮集团有限公司','中国物流集团有限公司','招商局集团有限公司','中国检验认证集团有限公司','中国有色矿业集团有限公司','有研科技集团有限公司','中国国际技术智力合作集团有限公司','中国诚通控股集团有限公司','中国煤炭科工集团有限公司','中国电子科技集团有限公司','中国石油天然气集团有限公司','国家电力投资集团有限公司','中国铁路通信信号集团有限公司','中国电信集团有限公司','中国航天科技集团有限公司','中国节能环保集团有限公司','中国国际工程咨询有限公司','中国建筑科学研究院有限公司','国家石油天然气管网集团有限公司','中国铁道建筑集团有限公司','中国联合网络通信集团有限公司','中国交通建设集团有限公司','中国农业发展集团有限公司','中国电子信息产业集团有限公司','中国保利集团有限公司','中国民航信息集团有限公司','中国宝武钢铁集团有限公司','中国航空油料集团有限公司','中国广核集团有限公司','中国黄金集团有限公司','中国盐业集团有限公司','中国华录集团有限公司','中国五矿集团有限公司','中国南光集团有限公司','国家开发投资集团有限公司','中国航空集团有限公司']:
                    leader_text = title.xpath("./h2/text()")
                else:
                    leader_text = title.xpath("./h2[@name]/text()")
                leader_text = join_text(leader_text)
                h2_text  = ["领导成员", "现任领导", "管理人员", "管理团队", "领导信息", "企业领导","集团领导","公司领导","领导班子","机构领导","主要领导","领导团队","公司管理", "管理体系", "企业管理"]
                if org in ["中国旅游集团有限公司"]:
                    h2_text.append("企业管理")
                if org in ["中国建筑科学研究院有限公司"]:
                    h2_text.append("机构领导")
                if org in ["中国电子科技集团有限公司"]:
                    h2_text.append("企业架构")
                if org in ["中国石油天然气集团有限公司"]:
                    h2_text.append("公司治理")
                if org in ["国家石油天然气管网集团有限公司"]:
                    h2_text.append("集团高层")
                if org in ["中国诚通控股集团有限公司",'中国五矿集团有限公司']:
                    h2_text.append("企业治理")

                
                if leader_text in h2_text: # 找到文章段落，开始解析
                    start = True
                    if org in ["中国华电集团有限公司", "中国电信集团有限公司", "国家电力投资集团有限公司","中国建材集团有限公司", "中国农业发展集团有限公司", "中国航空发动机集团有限公司","中国安能建设集团有限公司","中国航空集团有限公司", "中国石油化工集团有限公司", "国家能源投资集团有限责任公司","中国铁路工程集团有限公司", "中国东方电气集团有限公司","鞍钢集团有限公司", "中国电力建设集团有限公司","中国能源建设集团有限公司", "中国商用飞机有限责任公司","中国第一汽车集团有限公司", "中国中车集团有限公司", "国家石油天然气管网集团有限公司", "中国铁道建筑集团有限公司", "中国联合网络通信集团有限公司", "中国电子信息产业集团有限公司", "中国保利集团有限公司", "中国中化控股有限责任公司", "中国广核集团有限公司", "国家开发投资集团有限公司","中国煤炭科工集团有限公司","中国建筑集团有限公司", "中国电子科技集团有限公司", "中国石油天然气集团有限公司", '中国通用技术（集团）控股有限责任公司']:
                        tr_list = title.xpath("./following::table[1]//tr")
                        for tr in tr_list[1:]:
                            td_list = tr.xpath("./td")
                            if org in ["中国煤炭科工集团有限公司"]:
                                if len(td_list)<2:
                                    continue
                                else:
                                    pass
                            if org in ["中国航空发动机集团有限公司","中国安能建设集团有限公司", "中国航空集团有限公司", "中国铁路工程集团有限公司","中国东方电气集团有限公司","鞍钢集团有限公司", "中国电力建设集团有限公司", "中国能源建设集团有限公司","中国第一汽车集团有限公司", "中国中车集团有限公司","中国铁道建筑集团有限公司", "中国联合网络通信集团有限公司", "中国电子信息产业集团有限公司","中国中化控股有限责任公司", "中国广核集团有限公司", "中国电子科技集团有限公司","中国石油天然气集团有限公司",'中国通用技术（集团）控股有限责任公司','中国东方航空集团有限公司']:
                                text_list = tr.xpath("./td[1]//text()")
                                text_temp = []
                                for text in text_list:
                                    text = re.sub("\[.*?\]", "",text)
                                    if text not in text_temp and text:
                                        text_temp.append(text)
                                text_list = text_temp
                                url_list = tr.xpath("./td[1]//a/@href")
                                position = join_text(tr.xpath("./td[2]//text()")).strip()
                                # print("name_list={}\tposition={}".format(text_list, position))
                            elif org in ["中国电信集团有限公司"]:
                                text_list = tr.xpath("./td[1]//text()")
                                url_list = tr.xpath("./td[1]//a/@href")
                                position = join_text(tr.xpath("./th[1]//text()")).strip()
                            elif org in ["国家开发投资集团有限公司"]:
                                text_list = tr.xpath("./td[1]//span[contains(@class, 'text')]//text()")
                                url_list = tr.xpath("./td[1]//a/@href")
                                position = join_text(tr.xpath("./td[2]//text()")).strip()
                            else:
                                text_list = tr.xpath("./td[2]//text()")
                                url_list = tr.xpath("./td[2]//a/@href")
                                position = join_text(tr.xpath("./td[1]//text()")).strip()
                            url_list = url_encode(url_list)
                            text = join_text(text_list).strip()
                            if org in ["中国石油化工集团有限公司"]:
                                text = re.sub("\[.*?\]", "、", text)

                            if re.search("参考|资料", text) or not text:
                                continue
                            # print("text=",text)
                            name_list = re.split("、|，", text)                           
                            for name in name_list:
                                name = remove_parentheses(name).strip()
                                position = re.sub("\xa0|\[.*?\]|。$", "", position)
                                position = position.strip().strip('。;')
                                # position = re.sub(org, "", position)
                                if name and position:                                    
                                    url = [s for s in url_list if quote_plus(name) in s]
                                    url = url[0] if url else ""
                                    position = re.sub(re.compile("^现任|^集团公司"), "", position).strip()
                                    position = re.sub(f"^{name}[\u3000-\u303F\uFF00-\uFFEF]", "", position)
                                    if position in ["到龄退休"]:
                                        continue
                                    position_order += 1
                                    info_str = {"parent": "央企", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str)    
                        return senior_leader  
                    
                    if org in ["中国诚通控股集团有限公司", "中粮集团有限公司", "国家电网有限公司"]:
                        position_order = 0
                        if org in ["中国诚通控股集团有限公司"]:
                            max_idx=4
                        else:
                            max_idx =3
                            
                        for ii in range(1, max_idx):
                            tr_list = title.xpath("./following::table[{}]//tr".format(ii))    
                            for tr in tr_list[1:]:
                                if re.search("参考资料", join_text(tr.xpath(".//text()"))):
                                    continue
                                name_text = join_text(tr.xpath("./td[1]/div//text()"))
                                name_list = re.split("、", name_text)
                                position = join_text(tr.xpath("./td[2]/div//text()")) if join_text(tr.xpath("./td[2]/div//text()")) else "外部董事"       
                                position = remove_parentheses(position)   
                                for name in name_list:
                                    name = remove_parentheses(name).strip() 
                                    if name and position:
                                        url = tr.xpath(".//a/@href")
                                        url = url_encode(url)
                                        url = [s for s in url if quote_plus(name) in s]
                                        url = url[0] if url else ""                                    
                                        position_order += 1
                                        info_str = {"parent": "央企", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                        senior_leader.append(info_str)
                        return senior_leader
                   
                    if org in ["中国中钢集团有限公司", "招商局集团有限公司", "中国移动通信集团有限公司",'中国五矿集团有限公司']:
                        table_num = 2 if org in ["中国中钢集团有限公司", "招商局集团有限公司"] else 3
                        position_order = 0
                        for table_id in range(1,table_num):
                            xpath_str = r'./following::table[{}]//tr'.format(table_id)
                            tr_list = title.xpath(xpath_str)
                            print("table_id=",table_id)
                            for tr in tr_list[1:]:
                                if org in ["中国中钢集团有限公司", "中国移动通信集团有限公司",'中国五矿集团有限公司']:
                                    text_list = tr.xpath("./td[1]//text()")
                                    text = join_text(text_list).strip()
                                    if re.search("参考|资料", text):
                                        continue
                                    if re.search(pos_pattern, text):
                                        position = re.sub("\xa0|\[.*?\]", "", text)
                                        name_list = join_text(tr.xpath("./td[2]//text()")).strip()
                                        name_list = re.split("、", name_list)
                                    else:
                                        name_list = re.split("、", text)
                                        position = join_text(tr.xpath("./td[2]//text()")).strip()
                                        position = re.sub("\xa0|\[.*?\]", "", position)
                                elif org in ["招商局集团有限公司"]:
                                    td_ls = tr.xpath("./td[@rowspan='1']")
                                    lines = []
                                    
                                    for td in td_ls:
                                        td_text = "".join(td.xpath(".//text()")) 
                                        if td_text:
                                            lines.append(td_text)
                                    
                                    if lines and len(lines)>1:
                                        position = lines[0]
                                        name_text = join_text(lines[1:])
                                        name_list = re.split("、", name_text)
                                    else:
                                        continue
                                url_list = tr.xpath('.//a/@href')
                                url_list = url_encode(url_list)
                                for name in name_list:
                                    name = remove_parentheses(name).strip()
                                    position = position.strip().strip('。')
                                    if name and position:                                     
                                        url = [s for s in url_list if quote_plus(name) in s]
                                        url = url[0] if url else ""  
                                        position = re.sub("\[.*?\]|。$", "", position)
                                        if name=="张经华" and org=='中国中钢集团有限公司':
                                            url = "/item/{}/866769".format(quote_plus(name))      
                                        if {"name":name, "position":position} not in name_position:
                                            name_position.append({"name":name, "position":position})                               
                                        position_order += 1                                
                                        info_str = {"parent": "央企", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                        if info_str not in senior_leader:
                                            senior_leader.append(info_str)
                        return senior_leader
                    
                    if org in ["中国宝武钢铁集团有限公司"]:
                        # table_ls = page_content.xpath("//table[@data-id='tJ6QjLbWSduI' or @data-id='tJ6QtyznjdVy']")
                        table_ls = page_content.xpath(".//table[contains(@class, 'tableBox')]")
                        position_order = 0
                        name_position = []
                        max_idx = min(len(table_ls), 2)
                        for table in table_ls[:max_idx]:
                            caption = "".join(table.xpath("./caption/text()"))         
                            if caption not in ["董事会", "管理团队"]:
                                continue
                            tr_list = table.xpath("./tbody/tr")
                            # print(len(tr_list))
                            for tr in tr_list[1:]:
                                name_text = "".join(tr.xpath("./td[1]//text()")).strip()
                                name_text = re.sub("\[.*?\]", "", name_text)
                                position = "".join(tr.xpath("./td[2]//text()")).strip()
                                position = re.sub("\（.*?\）|\[.*?\]", "", position)
                                url_list = tr.xpath("./td[1]//a/@href")
                                url_list = url_encode(url_list)
                                name_list = name_text.split("、")
                                for name in name_list:
                                    name = remove_parentheses(name).strip()
                                    position = position.strip().strip('。')
                                    position = re.sub("\[(.*?)\]|。$","", position).strip()
                                    if name and position:
                                        if {"name": name, "position":position} not in name_position:
                                            name_position.append({"name": name, "position":position})
                                        else:
                                            continue
                                        url = [s for s in url_list if quote_plus(name) in s]
                                        url = url[0] if url else ""
                                        # url = "".join(url)                                       
                                        position_order += 1
                                        info_str = {"parent": "央企", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                        senior_leader.append(info_str)
                        return senior_leader
                     
        else:
            if title.xpath("./h3"):
                h_title = title.xpath("./h3/text()")
                h_title = join_text(h_title)
                if org in ["中国航空工业集团有限公司"] and h_title in ["研发机构"]:
                    start = False
                    break
                if org in ["中国盐业集团有限公司"] and h_title in ["工作分工"]:
                    start = False
                    break
                if h_title in ["历任领导", "内控制度", "科技精英"]:
                    start = False
                    break
            elif title.xpath("./h2"):
                start = False # 找到下一个文章段落，停止解析
                break
            elif org in ["中国铝业集团有限公司"] and title.xpath(".//span[contains(@class, 'bold')]/text()"):
                title_text = title.xpath(".//span[contains(@class, 'bold')]/text()")
                if "历任领导" in title_text:
                    start=False
                    break
            else:
                if org in ["中国航空工业集团有限公司"]:
                    pat = re.compile("\（(.*?)\）")
                    span_text = "".join(title.xpath(".//span//text()"))   #[contains(@class, 'text__NfVs')]
                    if not span_text:
                        continue
                    dot_index = span_text.find("，")
                    if dot_index==-1:
                        continue
                    name_pos = span_text[:dot_index]
                    name_list = "".join(re.compile("(.*?)（").findall(name_pos))
                    position = "".join(re.compile("（(.*?)）").findall(name_pos))
                    url_list =  title.xpath(".//a/@href")
                    url_list = url_encode(url_list)
                    if position and name_list:
                        text = "{}:{}".format(position, name_list)
                    else:
                        continue
                else:
                    text_list = title.xpath("./span[not(contains(@class, 'titleSpan'))]//text()") if org in ["中国融通资产管理集团有限公司","中国煤炭科工集团有限公司"] else title.xpath("./text() | ./*[not(contains(@class, 'layout-right'))]//text()")
                    # 获得每一条文本
                    print("text_list=",text_list)
                    if org in ["中国建筑科学研究院有限公司"]:
                        text_list = title.xpath("./span//text()")
                        if "现任领导" in "".join(text_list):
                            continue
                    # 拼接文本
                    text = join_text(text_list)
                    if org in ["矿冶科技集团有限公司"]:
                        text = re.sub("\[.*?\]", "、", text)
                    text = text.strip()
                    url_list =  title.xpath(".//a/@href")
                    url_list = url_encode(url_list)
                    
                if org in ['中国融通资产管理集团有限公司']:
                    text = re.sub('\[.*?\]', "", text)
                    text = re.sub("\（.*?\）", "", text)
                    temp_ind = text.find('：')
                    if temp_ind==-1:
                        matches = list(re.finditer(pos_pattern, text))
                        if matches:
                            end_index = matches[-1].end()
                            new_text = text[:end_index]+":"+text[end_index:]
                            text = new_text

                if re.search("资料|更新", text):
                    continue
                if org in ["中国建材集团有限公司","中国煤炭科工集团有限公司"]:#,"中国航空发动机集团有限公司"]:
                    temp = re.split(' ', text)
                elif org in ['中国华录集团有限公司', "中国钢研科技集团有限公司"]:
                    temp = re.split('[ ：]', text)
                elif org in ["中国安能建设集团有限公司", "中国东方航空集团有限公司", "哈尔滨电气集团有限公司", "中国铁路通信信号集团有限公司"]:
                    temp = re.split(r'[：]', text)
                elif org in ['中国医药集团有限公司']:
                    temp = re.split(r'：', text)
                elif org in ["中国化学工程集团有限公司"]:
                    temp = re.split('：', text)
                elif org in ["华侨城集团有限公司", "中国融通资产管理集团有限公司"]:
                    temp = re.split('[：:]', text)
                else:
                    temp = re.split('[：:，]', text) # 文本分割，默认短的文本为姓名
                # print(len(temp))
                if len(temp) > 1:
                    if pos_pattern.search(temp[0].replace(" ", "")):
                        position = temp[0].replace(" ", "")
                        if org in ["中国煤炭科工集团有限公司"]:
                            name_list = temp[1:]
                        else:
                            name_list = re.split('[、；，]', "".join(temp[1:]).replace(" ", ""))
                    else:
                        position = temp[1].replace(" ", "")
                        name_list = re.split('[、；，]', temp[0].replace(" ", ""))
                    if not position.strip():
                        position = h_title

                else:
                    if org in ['中国兵器工业集团有限公司']:
                        if pos_pattern.search(text):
                            position = text
                        else:
                            name_list = [text]
                            continue
                    else:
                        name_list = re.split('[、 ]', text)
                        position = h_title
           
                for name in name_list:
                    name = remove_parentheses(name).strip()
                    position = position.strip().strip('。')
                    position = re.sub("\xa0|\[.*?\]|。$", "", position)
                    if org in ["中国融通资产管理集团有限公司"]:
                        name = re.sub(".*总经理|董事长", "", name)
                        name = re.sub(org+position, "", name)
                    if name and position:
                        position_order += 1
                        name = name.strip()
                        # print("---title.get---", title.get())
                        name = name.strip("。")
                        pattern = re.compile(r'\d{2,4}(年\d{1,2}月.*)')
                        if org in ["哈尔滨电气集团有限公司","中国铁路通信信号集团有限公司"]:
                            position=re.sub("\[.*?\]|。$", "", position)
                        name = re.sub(pattern, "" , name)
                        name = re.sub("^(.*公司|集团)", "", name)
                        position = re.sub(r"\[.*?\]|。$", "", position)
                        tmp_url=[]
                        if len(name)>2:
                            tmp_url = [s for s in url_list if quote_plus(name) in s]
                        elif len(name)==2:
                            tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                        try:
                            url = tmp_url[0] if tmp_url else ""
                        except:
                            print({"parent": "央企", "province":"", "city":"", "org":org, "position":position, "name":name})
                        info_str = {"parent": "央企", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                        senior_leader.append(info_str)                                                  
    return senior_leader

# 中直机构
def parse_zz(org="", page_content=""):
    senior_leader = []
    # d("*********{}**********".format(org))
    page_content = etree.HTML(page_content)
    create_new = page_content.xpath("//div[@class='errorBox']")
    if create_new:
        print("[ERROR] 无法找到{}".format(org))
        return senior_leader

    start = False
    h_title = ""
    position = ""
    position_order=0
    # 领导成员
    title_list = page_content.xpath("//div[contains(@class, 'para')]")
    for title in title_list:
        if start == False:
            if org in ["中国浦东干部学院", "中央军民融合发展委员会", "中共中央组织部", "人民日报"]:
                leader_text = title.xpath("./h3/text()")
                leader_text = join_text(leader_text)
                if leader_text and leader_text.strip() in ["主要领导", "现任领导", "办公室"]: # 找到文章段落，开始解析
                    start = True
                    if org in ["人民日报"]:
                        tr_list = title.xpath("./following::table[1]//tr")
                        position_order=0
                        for tr in tr_list[1:]:
                            position = "".join(tr.xpath("./td[1]//text()"))
                            position = position.strip()
                            name_text = "".join(tr.xpath("./td[2]//text()"))
                            name_list = name_text.split("、")
                            url_list = url_encode(tr.xpath("./td[2]//a/@href"))
                            for name in name_list:
                                name = remove_parentheses(name).strip()
                                if name:
                                    if re.compile("第.*?届").match(name):
                                        continue
                                    tmp_url = [s for s in url_list if quote_plus(name) in s]
                                    url = tmp_url[0] if tmp_url else ""
                                    url = re.sub(r"\?from.*", "", url) 
                                    position = re.sub("\xa0|\[.*?\]", "", position)
                                    position_order+=1
                                    info_str = {"parent": "中直机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str)
                        return senior_leader
            else:
                leader_text = title.xpath('./h2/text()')
                # if org in ["中共中央宣传部", '中共中央统一战线工作部','中共中央对外联络部','中共中央政策研究室','中央网络安全和信息化委员会办公室', '中央台湾工作办公室', '中央外事工作委员会办公室','中央机构编制委员会办公室','中央党校','中央党史和文献研究院','求是杂志社','光明日报社','中国井冈山干部学院','中国延安干部学院','中央社会主义学院','中共中央办公厅','中共中央政法委员会','中央国家安全委员会办公室','中央军民融合发展委员会','中央财经委员会办公室','中国共产党中央委员会中央和国家机关工作委员会']:
                #     leader_text = title.xpath("./h2/text()")
                # else:
                #     leader_text = title.xpath("./h2[@name]/text()")

                leader_text = join_text(leader_text)
                if leader_text in ["领导成员", "现任领导", "领导体制", "主要领导", "领导信息", "领导简介", "领导班子", "领导分工"]: # 找到文章段落，开始解析
                    start = True
                    if org in ["中国延安干部学院"]:
                        tr_list = title.xpath("./following::table[1]//tr")
                        for tr in tr_list[1:]:
                            text_list = tr.xpath("./td[2]//text()")
                            name_text = join_text(text_list).strip()
                            name_list = name_text.split("、")
                            for name in name_list:
                                if name == "空缺":
                                    continue
                                url = url_encode(tr.xpath("./td[2]//a/@href"))
                                if name == "":
                                    name = join_text(tr.xpath("./td[1]//text()")).strip()
                                else:
                                    position = join_text(tr.xpath("./td[1]//text()")).strip()
                                    position = re.sub('[:、，]', "", position)
                                name = remove_parentheses(name).strip()
                                if name:
                                    if re.compile("第.*?届").match(name):
                                        continue
                                    position_order += 1
                                    url = "".join([s for s in url if quote_plus(name) in s])
                                    url = re.sub(r"\?from.*", "", url)
                                    position = re.sub("\xa0|\[.*?\]", "", position)
                                    info_str = {"parent": "中直机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str)
                        return senior_leader
        else:
            if title.xpath("./h3"):
                # print(title.xpath("./h3/text()").get()) # 获得职务名称：书记、副书记
                h_title = title.xpath("./h3/text()")
                h_title = "".join(h_title)
                if h_title in ["历任领导", "内控制度"]:
                    start = False
                    break
            elif title.xpath("./h2"):
                start = False # 找到下一个文章段落，停止解析
                break
            else:
                # text_list = title.xpath(".//text()").getall() # 获得每一条文本
                text_list = title.xpath("./text() | ./*[not(contains(@class, 'layout-right'))]//text()")# 获得每一条文本
                text = join_text(text_list)
                text = re.sub(r'（[^（）]*）', '', text) if org in ["中国浦东干部学院"] else text
                # print("text=", text)

                if re.search("现任领导|资料|更新", text):
                    continue
                if org in ["中央机构编制委员会办公室"]:
                    temp = re.split('[：]', text)
                elif org in ["中共中央统一战线工作部"]:
                    if len(text)>1 and text[1] in [' ']:
                        text = re.sub("\s+", "", text, count=1)
                    temp = re.split('[ ]', text)
                    if len(temp)>=2 and temp[1]=='':
                        temp.pop(1)
                    if len(temp)>=2:
                        temp[1] = "".join(temp[1:])
                else:
                    temp = re.split('[：:，]', text) # 文本分割，默认短的文本为姓名
                if len(temp) > 1:
                    if pos_pattern.search(temp[0].replace("　", "")):
                        position = temp[0].replace("　", "")
                        if org in ["中央机构编制委员会办公室"]:
                            position = h_title + position
                            name_list = re.split('[ 、]', temp[1])
                        elif org in ['中国浦东干部学院']:
                            name_list = re.split('[。、]', remove_parentheses(temp[1].replace(" ", "")))
                        elif org in ["中共中央政法委员会"]:
                            name_list = re.split("[ 、]", temp[1])
                        elif org in ["中共中央组织部"]:
                            tt = remove_parentheses(temp[1])
                            name_list = re.split("、", tt)
                        else:
                            name_list = re.split('[。、；，]', temp[1].replace(" ", ""))
                    else:
                        position = temp[1].replace(" ", "")
                        name_list = re.split('[。、；，]', temp[0].replace(" ", ""))
                    if not position.strip():
                        position = h_title
                else:
                    if org in ['中央机构编制委员会办公室']:
                        continue
                    name_list = re.split('[。、 ]', text)
                    position = h_title
                url_list = url_encode(title.xpath(".//a/@href"))
                for name in name_list:
                    name = re.sub(r'（[^（）]*）', '', name)
                    name = remove_parentheses(name).strip()
                    if re.compile("第.*?届").match(name):
                        continue
                    if name and position:          
                        if len(name)>2:            
                            tmp_url = [s for s in url_list if quote_plus(name) in s]
                        elif len(name)==2:
                            tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                        url = tmp_url[0] if tmp_url else ""
                        position_order += 1
                        position = re.sub("\xa0|\[.*?\]", "", position)
                        info_str = {"parent": "中直机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                        senior_leader.append(info_str)
    return senior_leader
                        


# 军事机构
def parse_pla(org="", page_content=""):
    senior_leader = []
    # print("*********{}**********".format(org))
    page_content = etree.HTML(page_content)
    create_new = page_content.xpath("//div[@class='errorBox']")
    if create_new:
        print("[ERROR] 无法找到{}".format(org))
        return senior_leader

    start = False
    h_title = ""
    position = ""
    position_order=0
    # 领导成员
    title_list = page_content.xpath("//div[contains(@class, 'para')]")
    # title_list = page_content.xpath("//div[@data-pid='72']")
    for title in title_list:
        if start == False:
            if org in ["中国浦东干部学院", "中央军民融合发展委员会", "中央军委办公厅", "国防大学", \
                "中国人民解放军军事法院", "中国人民解放军军事检察院", "军事科学院", "国防科技大学"]:
                leader_text = title.xpath("./h3/text()")
                leader_text = "".join(leader_text)
                if leader_text and leader_text.strip() in ["主要领导", "现任领导", "办公室"]: # 找到文章段落，开始解析
                    start = True
                    if org in ["国防大学", "军事科学院","国防科技大学"]:
                        tr_list = title.xpath("./following::table[1]//tr")
                        position_order=0
                        for tr in tr_list[1:]:
                            position = "".join(tr.xpath("./td[1]//text()"))
                            name_text = "".join(tr.xpath("./td[2]//text()"))
                            name_list = name_text.split("、")
                            url_list = url_encode(tr.xpath("./td[2]//a/@href"))
                            for name in name_list:
                                name = remove_parentheses(name).strip()
                                if name:
                                    if re.compile("第.*?届").match(name):
                                        continue
                                    tmp_url = [s for s in url_list if quote_plus(name) in s]
                                    url = tmp_url[0] if tmp_url else ""
                                    url = re.sub(r"\?from.*", "", url) 
                                    position = re.sub("\xa0|\[.*?\]", "", position).strip() if org not in ["军事科学院"] else re.sub("\xa0|\[.*?\]|\（.*?\）", "", position).strip()
                                    
                                    position_order+=1
                                    info_str = {"parent": "军事机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str)
                        return senior_leader
            else:
                leader_text = title.xpath("./h2[@name]/text()") if org not in ['中央军委联合参谋部','中央军委政治工作部','中央军委后勤保障部','中央军委装备发展部','中央军委训练管理部','中央军委国防动员部','中央军委政法委员会','中央军委科学技术委员会','中央军委战略规划办公室','南部战区','西部战区','北部战区','中部战区','中国人民解放军火箭军','中国人民解放军联勤保障部队','中央军委纪律检查委员会','中央军委国际军事合作办公室','中央军委审计署','中央军委机关事务管理总局','东部战区','中国人民解放军陆军','中国人民解放军海军','中国人民解放军空军','中国人民武装警察部队'] else title.xpath('./h2/text()')
                leader_text = "".join(leader_text)

                
                if leader_text in ["领导成员", "现任领导", "领导体制", "主要领导", "领导信息", "领导简介", "领导班子", "部门领导", "战区领导", "部队领导", "主要将领", "学校领导", "海军领导","军委领导"]: # 找到文章段落，开始解析
                    start = True
                    if org in ["中国人民解放军海军"]:
                        tr_list = []
                        ## 司令员
                        positions = ["中国人民解放军海军司令员", "中国人民解放军海军政治委员"]
                        position_order = 0
                        for ii in range(1,3):
                            tr_list = title.xpath("./following::table[{}]//tr".format(ii))
                            position = positions[ii-1]
                            tr = tr_list[-1]
                            name_text = join_text(tr.xpath("./td[1]//text()"))
                            name_list = name_text.split("、")
                            url_list = tr.xpath("./td[1]//a/@href")
                            url_list = url_encode(url_list)
                            for name in name_list:
                                name = remove_parentheses(name).strip()
                                if name:
                                    if re.compile("第.*?届").match(name):
                                        continue
                                    position=re.sub("\xa0|\[.*?\]", "", position)
                                    position_order += 1
                                    if len(name)>2:
                                        tmp_url = [s for s in url_list if quote_plus(name) in s]
                                    elif len(name)==2:
                                        tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                                    url = tmp_url[0] if tmp_url else ""
                                    info_str = {"parent": "军事机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str)
                        return senior_leader

                    if org in ["中国延安干部学院", "中国人民武装警察部队"]:
                        tr_list = title.xpath("./following::table[1]//tr")
                        for tr in tr_list[1:]:
                            if org in ["中国人民武装警察部队"]:
                                text_list = tr.xpath("./td[1]//text()")
                                name_text = join_text(text_list).strip()
                                name_list = name_text.split("、")
                                url_list = tr.xpath("./td[1]//a/@href")     
                            else:
                                text_list = tr.xpath("./td[2]//text()")
                                name_text = join_text(text_list).strip()
                                name_list = name_text.split("、")
                                url_list = tr.xpath("./td[2]//a/@href")
                            url_list = url_encode(url_list)
                            for name in name_list:
                                if name == "空缺":
                                    continue
                                if name == "":
                                    name = join_text(tr.xpath("./td[1]//text()")).strip()
                                else:
                                    if org in ["中国人民武装警察部队"]:
                                        position = join_text(tr.xpath("./td[2]//text()")).strip()
                                        position = re.sub('[:、，]', "", position)
                                    else:
                                        position = join_text(tr.xpath("./td[1]//text()")).strip()
                                        position = re.sub('[:、，]', "", position)
                                name = remove_parentheses(name).strip()
                                if name:
                                    if re.compile("第.*?届").match(name):
                                        continue
                                
                                    position=re.sub("\xa0|\[.*?\]", "", position)
                                    position = position.strip()
                                    position_order += 1
                                    if len(name)>2:
                                        tmp_url = [s for s in url_list if quote_plus(name) in s]
                                    elif len(name)==2:
                                        tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s)]
                                    url = tmp_url[0] if tmp_url else ""
                                    url = re.sub(r"\?from.*", "", url)
                                    info_str = {"parent": "军事机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                                    senior_leader.append(info_str)
                        return senior_leader    
        else:
            if title.xpath("./h3"):
                # print(title.xpath("./h3/text()").get()) # 获得职务名称：书记、副书记
                h_title = "".join(title.xpath("./h3/text()"))
                if h_title in ["历任领导", "内控制度"]:
                    start = False
                    break
            elif title.xpath("./h2"):
                start = False # 找到下一个文章段落，停止解析
                break
            else:
                text_list = title.xpath("./text() | ./*[not(contains(@class, 'layout-right'))]//text()") # 获得每一条文本
                text = join_text(text_list)
                if not text:
                    continue
                
                text = text if org not in ["中央军委政治工作部"] else re.sub("\[.*?\]", "、", text)
                if org in ["中国浦东干部学院"]:
                    text = re.sub("\[.*?\]|\（.*?\）|）", "", text)
                # print("---",text)
                if re.search("现任领导|资料|更新", text):
                    continue
                if org in ["中共中央统一战线工作部"]:
                    temp = re.split('[ ]', text)
                    temp[1] = "".join(temp[1:])
                elif org in ["中国人民解放军军事法院"]:
                    temp = re.split("：", text)
                elif org in ["中央军委政治工作部"]:
                    temp = re.split(r"[：]", text)
                else:
                    temp = re.split('[：:，]', text) # 文本分割，默认短的文本为姓名
                if len(temp) > 1:
                    if pos_pattern.search(temp[0].replace("　", "")):
                        position = temp[0].replace("　", "")
                        if org in ['中国浦东干部学院']:
                            name_list = re.split('[。、]', remove_parentheses(temp[1].replace(" ", "")))
                        elif org in ["中央军委政治工作部"]:
                            name_list = re.split(r"[、 ]", temp[1])
                        else:
                            name_list = re.split('[。、；，]', temp[1].replace(" ", ""))
                    else:
                        position = temp[1].replace(" ", "")
                        name_list = re.split('[。、；，]', temp[0].replace(" ", ""))
                    if not position.strip():
                        position = h_title
                else:
                    name_list = re.split('[。、 ]', text)
                    position = h_title
                url_list = url_encode(title.xpath(".//a/@href"))

                for name in name_list:
                    name = remove_parentheses(name).strip()
                    if re.compile("第.*?届").match(name):
                        continue
                    if name and position:
                        position = re.sub("\xa0", "", position)
                        position_order += 1
                        if len(name)>2:
                            tmp_url = [s for s in url_list if quote_plus(name) in s]
                        elif len(name)==2:
                            tmp_url = [s for s in url_list if quote_plus(name) in s or (quote_plus(name[0]) in s and quote_plus(name[1]) in s) ]
                        url = tmp_url[0] if tmp_url else ""
                        name = re.sub(pos_pattern, "", name)
                        position = re.sub("\[.*?\]", "", position)
                            # position = "".join(my_pos_pattern.findall(position))
                        info_str = {"parent": "军事机构", "province":"", "city":"", "org":org, "position":position, "position_order":position_order, "name":name, "url":url}
                        senior_leader.append(info_str)

    return senior_leader
                        


def test():
    from elasticsearch import Elasticsearch, helpers
    __index_name__ = 'leader-info-zfztemp'
    es = Elasticsearch(["http://120.48.11.3:9203"], basic_auth=('elastic', 'gz123@people!@#'), request_timeout=120, max_retries=10, retry_on_timeout=True)
    query = {
        "bool": {
            "must": []
        }
    }

    res = es.search(index=__index_name__, query=query, timeout='10m', size=10000)
