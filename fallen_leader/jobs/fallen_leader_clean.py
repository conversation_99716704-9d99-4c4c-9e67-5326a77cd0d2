# coding=utf-8
"""fallen_leader dataset clean"""
# Author: <PERSON><PERSON><PERSON>
# Time: 2022-5-24
#
from textwrap import indent
from elasticsearch import helpers, Elasticsearch
from tqdm import tqdm
import Levenshtein
import datetime, json
from elasticsearch.helpers import bulk
# python3 es-tool.py delete -name organizations
# python3 es-tool.py index -name organizations -mapping ./maps/organizations-mapping.json
# python3 es-tool.py import -name organizations -data ./maps/organizations.json -type json
import os
os.environ["TOKENIZERS_PARALLELISM"]= "false"
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

class fallen_leader_clean:
    def __init__(self,date_flag=False,update_time='2022-05-22',name_thres = 0.9, position_thres = 0.5):
        """self.es = Elasticsearch(['127.0.0.1'],
                           http_auth=('elastic', 'gz123@people!@#'),
                           port=9203,
                           timeout=120,
                           max_retries=10,
                           retry_on_timeout=True)"""
        file_path = os.path.dirname(__file__)
        with open(os.path.join(os.path.abspath(os.path.join(file_path, "../..")), "configs/es_config.json"),"r", encoding="utf-8") as fp:
                CONFIG_ES = json.load(fp)
        self.es = Elasticsearch(CONFIG_ES['host'], basic_auth=(CONFIG_ES['user'], CONFIG_ES['passwd']), request_timeout=120, max_retries=10, retry_on_timeout=True)
        # 新增的人物数据列表，从fallen-leader中遍历出来，并去重，再与fallen-leader-clean的数据比对之后插入到fallen-leader-clean库中
        self.people_list = []

        #fallen-leader库中已经清洗的人物数据id值列表，比如清洗过某个王刚，包含王刚1，王刚2，那这个列表里包含了这两个王刚的id值
        self.matched_people_id_list = []
        # 需要合并的人物数据在self.people_list中的索引，用于对数据的删除
        self.clean_index_list = []
        #人名比对的阈值
        self.name_Levenshtein_threshold = name_thres
        #职位比对的阈值
        self.position_Levenshtein_threshold = position_thres

        # 数据更新日期标志，默认false，如果初始化的时候设置为true，则根据初始化输入的update_time日期进行数据更新
        if date_flag:
            self.date = update_time
        #如果初始化设置为False，则通过读update_date.txt文中的上次更新日期来进行当前更新
        else:
            with open(BASE_DIR+'/update_date.txt', 'r') as f:  # 打开文件
                lines = f.readlines()  # 读取所有行，每次更新的日期都保存在文档中，每次都读最后一行
            self.date = lines[-1].replace("\n", "")
        #当前这次更新完数据之后，标定的日期，根据fallen-leader数据中日期字段中最大的值决定
        self.new_date = datetime.date.today()

    #遍历fallen-leader库中指定日期之后的新增数据，将数据写入self.people_list列表
    def search_updated_origin_fallen_leader(self):
        people_query = {
            "query": {
                "bool": {
                    "should": [
                        {
                            "match_all": {}

                        }],
                    "filter": {
                        "range": {
                            "create_time": {
                                "gt": self.date
                            }
                        }
                    }
                }
            }
        }
        scan_res = helpers.scan(client=self.es, query=people_query, scroll="10m", index="fallen-leader", timeout="10m")
        for res in tqdm(scan_res, desc='fallen_people'):
            self.people_list.append(res)

        print("新增的落马人员：", len(self.people_list))

    #将self.people_list中，重名的人物数据进行合并，先遍历self.people_list列表，然后根据人名搜索fallen-leader库中同名人
    def origin_fallen_leader_same_people_merge(self):
        for i,people in enumerate(self.people_list):#遍历self.people_list列表
            if people['_id'] not in self.matched_people_id_list:#当前遍历的人物不在需要删除的数据列表中
                same_name_query = {
                    "bool": {
                        "must": [
                            {
                                "match_phrase": {
                                    "name": people['_source']['name']
                                }

                            }],
                        "filter": {
                            "range": {
                                "create_time": {
                                    "gt": self.date
                                }
                            }
                        }
                    }
                }
                same_name_list = self.es.search(index='fallen-leader', query=same_name_query) #
                # print(same_name_list)
                #每次只会有一条数据与当前人同名匹配
                best_match_score = 0#记录当前people匹配的同名人的职位相似度最高的值
                best_match_people_id=0
                best_match_name = []
                for same_name in same_name_list['hits']['hits']:
                    if same_name['_id'] not in self.matched_people_id_list:#检索出来的同命人物不在已经清洗过的人物列表里
                        fallen_type = same_name['_source']['fallen_type'] + people['_source']['fallen_type']
                        type_diff = abs(same_name['_source']['fallen_type'] - people['_source']['fallen_type'])
                        if (type_diff) == 1 and (fallen_type in [3,7,11]):#落马类型匹配，1和2，3和4，5和6
                            name_dist = Levenshtein.jaro(people['_source']['name'],same_name['_source']['name'])
                            position_dist = Levenshtein.jaro_winkler(people['_source']['position'],same_name['_source']['position'])
                            # flag = False and position_dist>best_match_score
                            if name_dist>self.name_Levenshtein_threshold:#如果人名重合度大于0.9
                                #先将职位信息有完全包含关系的找出来，如果有完全包含关系，就直接判断为同一人，不需要看职位距离了
                                if ((people['_source']['position'] in same_name['_source']['position'])
                                        or (same_name['_source']['position'] in people['_source']['position'])):
                                    best_match_score = 0.99#具备包含关系，则假设职位匹配度为0.99
                                    best_match_people_id = same_name['_id']
                                else:
                                    # 且职位重合度大于0.5,同时职位重合度分数比最佳匹配的分数高
                                    if position_dist>self.position_Levenshtein_threshold \
                                            and position_dist>best_match_score:
                                        best_match_people_id = same_name['_id']
                                        best_match_score = position_dist

                            # print(' {:.02f} : {:.02f}'.format(name_dist,position_dist))
                if best_match_score>0:
                    # 将当前people在self.people_list列表的索引值追加到清洗索引列表中，后期用于删除数据
                    self.clean_index_list.append(i)
                    # 将匹配成功的两条数据的id值加到已匹配过的数据列表
                    self.matched_people_id_list.append(best_match_people_id)
                    self.matched_people_id_list.append(people['_id'])
        # 将self.people_lis中需要去重的数据的索引进行反转，然后从后往前通过pop删除，得到去重后的列表self.people_list
        self.clean_index_list.reverse()
        for i in self.clean_index_list:
            self.people_list.pop(i)
    
    # 插入到fallen_leader_clean中的落马官员有重复的，把同一人的信息删除掉
    def remove_duplicates_fallen_leader(self, data):
        seen = {}
        result = []
        for item in data:
            name = item.get("_source").get("name")
            position = item.get("_source").get("position")
            if (name, position) not in seen:
                seen[(name, position)] =True
                result.append(item)
        return result


    #根据self.people_list列表，查询清洗后的fallen_leader_clean库中是否存在同名人
    # 如果存在同名人，则不插入fallen_leader_clean库中，直接pass，如果不存在，则将该条人物数据插入到fallen_leader_clean中
    def fallen_leader_clean_insert(self):
        fallen_leader_clean_actions = []
        for i,people in enumerate(self.people_list):#遍历self.people_list列表
            same_name_query = {
                    "match_phrase":{
                        "name": people['_source']['name']
                    }
                }
            #搜索名称相近的人物数据列表
            same_name_list = self.es.search(index='fallen-leader-clean', query=same_name_query) #查找face数据库中小于10张人脸的人物
            # print(same_name_list)
            flag = False
            for same_name in same_name_list['hits']['hits']:
                #计算人名重合度以及职务重合度
                name_dist = Levenshtein.jaro(people['_source']['name'],same_name['_source']['name'])
                position_dist = Levenshtein.jaro_winkler(people['_source']['position'],same_name['_source']['position'])
                #是否需要新增到fallen_leader_clean库的标签

                if name_dist>self.name_Levenshtein_threshold:
                    if position_dist>self.position_Levenshtein_threshold:
                        flag = True
                    else:
                        if ((people['_source']['position'] in same_name['_source']['position'])
                            or (same_name['_source']['position'] in people['_source']['position'])):
                            flag = True
            if not flag:
                # 插入数据
                action = {
                    '_index': 'fallen-leader-clean',
                    '_source': people['_source']
                }
                fallen_leader_clean_actions.append(action)
        # self.es.index(index="fallen-leader-clean", body=people)
        # 当天新增的落马人员里有重复的数据，过滤一下重复的数据
        remove_result = self.remove_duplicates_fallen_leader(fallen_leader_clean_actions)
        success, _ = bulk(self.es, remove_result, raise_on_error=True)

        #完成清洗数据更新后，修改update_date.txt中的更新时间，用追加的方式
        try:
            with open(os.path.join(BASE_DIR,"logs/fallen_leader_clean_{}.json".format(datetime.date.today().strftime("%Y%m%d"))), "a", encoding="utf-8") as f:
                f.write(json.dumps(fallen_leader_clean_actions, ensure_ascii=False, indent=2))
                f.write("过滤当天同名之后的数据")
                f.write(json.dumps(remove_result, ensure_ascii=False, indent=2))
        except Exception as err:
            print(err)
    def update(self):
        self.search_updated_origin_fallen_leader()
        self.origin_fallen_leader_same_people_merge()
        self.fallen_leader_clean_insert()

if __name__ == '__main__':
    data_clean = fallen_leader_clean()
    data_clean.update()
    print(len(data_clean.people_list))
