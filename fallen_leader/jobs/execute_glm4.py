import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

import os
os.environ["CUDA_VISIBLE_DEVICES"] = "0" # 表示使用第3个

device = "cpu"#"cuda" if torch.cuda.is_available() else "cpu"
print(device)

# model_path = "/hpfs/yud/share/glm-4-9b-chat"
model_path = "/home/<USER>/glm-4-9b-chat"

tokenizer = AutoTokenizer.from_pretrained(model_path,trust_remote_code=True)



model = AutoModelForCausalLM.from_pretrained(
	model_path,
	torch_dtype=torch.bfloat16,
	low_cpu_mem_usage=True,
	trust_remote_code=True
).to(device).eval()

# query = '''从TEXT后面的文本中抽取{}及其对应的地域、机构、职位等信息，多个职位分开列出，以json格式返回结果，如
# 句子为: 云南省迪庆州委副书记许洋接受审查调查， 抽取结果为
# {{
#   "name": "许洋",
#   "region": "云南省迪庆州",
#   "org": "迪庆州委",
#   "position": "副书记"
# }},
# 不要输出其它内容. TEXT: {}'''

# query = '''从后面的TEXT文本中抽取所有人名，及其对应的地域、机构、职位等信息，多个职位分开列出，以json格式返回结果，如
# 句子为: 云南省迪庆州委副书记许洋接受审查调查， 抽取结果为
# {{
#   "name": "许洋",
#   "region": "云南省迪庆州",
#   "org": "迪庆州委",
#   "position": "副书记"
# }},
# 不要输出其它内容，当出现多个人名时，以list的格式返回. TEXT: {}'''
query="判断后面TEXT中的两个人是不是同一机构的同一人，返回是或者否.TEXT:{}"


def get_output(query):
    inputs = tokenizer.apply_chat_template([{"role": "user", "content": query}],
                                       add_generation_prompt=True,
                                       tokenize=True,
                                       return_tensors="pt",
                                       return_dict=True
                                       )

    inputs = inputs.to(device)

    gen_kwargs = {"max_length": 2500, "do_sample": True, "top_k": 1}
    with torch.no_grad():
        outputs = model.generate(**inputs, **gen_kwargs)
        outputs = outputs[:, inputs['input_ids'].shape[1]:]
        # print(tokenizer.decode(outputs[0], skip_special_tokens=True))
        return tokenizer.decode(outputs[0], skip_special_tokens=True)



def load_ltp_result():
    import json
    from tqdm import tqdm
    filepath = "ltp_result.txt"
    fw = open("ltp_result_glm4.txt", "w")
    used_set = set()
    with open(filepath) as f:
        for idx, line in tqdm(enumerate(f)):
            data = json.loads(line.strip())

            content = data["content"]
            if content in used_set:
                continue
            if any(
                s in content
                for s in [
                "万千星辉",
                "阿余黄子华高能经商",
                "体验免费看海量港剧港",
                "包季首订低至力独播",
                 ]
            ):
                continue
            if len(content) < 1000:
                continue
            used_set.add(content)
            s = query.format(content)
            result = get_output(s)
            data["glm4_result"] = result
            if idx < 5:
                print(s)
                print(result)
            fw.write(json.dumps(data, ensure_ascii=False)+"\n")
    fw.close()


# load_ltp_result()
def load_fallen_leader():
    import json
    from tqdm import tqdm
    # fw = open("paper_data_glm4.txt", "w")
    text="中国共产党中央委员会宣传部副部长张建春和中央宣传部副部长张建春"
    s = query.format(text)
    result = get_output(s)
    print(result)

load_fallen_leader()
 
