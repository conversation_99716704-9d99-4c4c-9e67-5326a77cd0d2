# from asyncio.windows_events import NULL
import os
from socket import dup
from xmlrpc.client import DateTime
from elasticsearch import Elasticsearch
from datetime import datetime
from time import sleep
from requests_html import HTMLSession
import re
import random
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from ltp import LTP
from fallen_leader_clean import fallen_leader_clean
import json, logging
import schedule, time, requests
from lxml import etree
import traceback
import difflib, traceback
import torch
torch.cuda._initialized=True


try:
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    print(BASE_DIR)


    # 写入log文件
    running_log_file = os.path.join(BASE_DIR, "fall_info.log")
    logging.basicConfig(filename=running_log_file, level=logging.WARNING, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger()

    with open(os.path.join(os.path.abspath(os.path.join(BASE_DIR, "../..")), "configs/es_config.json"), "r", encoding="utf-8") as fp:
        es_config = json.load(fp=fp)
    es = Elasticsearch(es_config.get("host"), 
                    basic_auth=(es_config["user"], es_config["passwd"]))

    INDEX_NAME = 'fallen-leader'
    user_agent_list = ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/22.0.1207.1 Safari/537.1"]
    """
    user_agent_list = [
            "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/22.0.1207.1 Safari/537.1",
            "Mozilla/5.0 (X11; CrOS i686 2268.111.0) AppleWebKit/536.11 (KHTML, like Gecko) Chrome/20.0.1132.57 Safari/536.11",
            "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.6 (KHTML, like Gecko) Chrome/20.0.1092.0 Safari/536.6",
            "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.6 (KHTML, like Gecko) Chrome/20.0.1090.0 Safari/536.6",
            "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/19.77.34.5 Safari/537.1",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.9 Safari/536.5",
            "Mozilla/5.0 (Windows NT 6.0) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.36 Safari/536.5",
            "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3",
            "Mozilla/5.0 (Windows NT 5.1) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_0) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3",
            "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1062.0 Safari/536.3",
            "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1062.0 Safari/536.3",
            "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3",
            "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3",
            "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3",
            "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.0 Safari/536.3",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.24 (KHTML, like Gecko) Chrome/19.0.1055.1 Safari/535.24",
            "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/535.24 (KHTML, like Gecko) Chrome/19.0.1055.1 Safari/535.24"
        ]
        """
    start_urls = ['https://www.ccdi.gov.cn/scdcn/zggb/zjsc/',
              'https://www.ccdi.gov.cn/scdcn/zggb/djcf/',
              'https://www.ccdi.gov.cn/scdcn/zyyj/zjsc/',
              'https://www.ccdi.gov.cn/scdcn/zyyj/djcf/',
              'https://www.ccdi.gov.cn/scdcn/sggb/zjsc/',
              'https://www.ccdi.gov.cn/scdcn/sggb/djcf/',
            ]
except:
    print(traceback.format_exc())
def get_time():
    return datetime.now().strftime('%Y-%m-%d %H:%M:%S')



def create_index():
    mappings = {
            "properties": {
                "position": {"type": "text"},
                "name": {"type": "text"},
                "matchtext": {"type": "text"},
                "url": {"type": "keyword"},
                "fallen_type": {"type": "keyword"},
                "fallen_date": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"},
                "create_time": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"},
                "update_time": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"}
            }
        }
    print("es.indices.exists=",es.indices.exists(index=INDEX_NAME))
    if not es.indices.exists(index=INDEX_NAME):
        es.indices.create(index=INDEX_NAME, mappings=mappings)
    else:
        try:
            if es.indices.exists(index="fallen-leader-clean"):
                es.indices.delete(index=INDEX_NAME)
                docs = es.search(index="fallen-leader-clean", body={"query":{"match_all":{}},"size":100000},)["hits"]["hits"]
                print(len(docs))
                for idx,doc in enumerate(docs):
                    _id = doc["_id"]
                    original_date_str_tmp = doc["_source"]["fallen_date"]
                    if len(original_date_str_tmp)<18:
                        original_date_str = original_date_str_tmp + " 00:00:00"# print(len(original_date_str))
                        original_date = datetime.strptime(original_date_str, "%Y-%m-%d %H:%M:%S")
                        # 将日期重新格式化为 Elasticsearch 预期的格式
                        formatted_date_str = original_date.strftime("%Y-%m-%d %H:%M:%S")
                            # 更新文档中的日期字段值
                        doc["_source"]["fallen_date"] = formatted_date_str  
                        doc["_source"]["update_time"] = get_time()
                        update_body = {
                            "doc": doc["_source"]
                        }
                        es.update(index="fallen-leader-clean", id=_id, body=update_body)
                        # 将文档索引到目标索引
                        # es.index(index=INDEX_NAME, body=doc["_source"])
                    else:
                        continue
                    
                bb = {
                    "source": {"index":"fallen-leader-clean"},
                    "dest": {"index": INDEX_NAME}
                }
                es.reindex(body=bb, wait_for_completion=True)
                print(f"Copied data from index 'fallen-leader-clean' to '{INDEX_NAME}'")
                logger.warning("Copied data from index 'fallen-leader-clean' to 'fallen-leader'")
        except Exception as errmsg:
            print(errmsg)
            logger.warning(f"erron in copy index:{errmsg}")


def insert_data(name:str, position, matchtext, url, fallen_type, fallen_date):
    if name[-1] == "被":
        name = name[:-1]
    if name.endswith("被双开"):
        name = name[:-3]
    doc = {
            "name": name,
            "position": position,
            "matchtext": matchtext,
            "url": url,
            "fallen_type": fallen_type,
            "fallen_date": fallen_date,
            "create_time": get_time(),
            "update_time": get_time()
        }
    logger.warning(r"insert to {}  name={} posiiton={}".format(INDEX_NAME, name, position))
    # print(doc)
    # 在数据插入之前进行是否是确认的落马官员
    pattern = re.compile(r"接受.{0,20}调查|双开|开除党籍|纪律审查|审查调查|被调查|开除公职|违纪违法|接受.{0,20}审查|涉嫌.{0,20}违纪|被[处分,问责]|.{0,20}[审查,调查]|决定逮捕|提起公诉|受到.{0,20}处分")
    match_result = pattern.findall(matchtext)
    if match_result:
        es.index(index=INDEX_NAME, document=doc)
        time.sleep(1)

def find_data(matchtext):
    query = {
            "match_phrase": {
                "matchtext": matchtext,
            }
        }
    res = es.search(index=INDEX_NAME, query=query)
    total = res['hits']['total']['value']
    if total == 0:
        return False
    elif res['hits']['hits'][0]['_source']['matchtext'] == matchtext:
        return True
    return False

#  中纪委-审查调查版块
def update_fallen_leader():
    scdc_fallen_leaders = []
    with HTMLSession() as session:
    # session = HTMLSession()
        ner_service = NewsNERService()
        headers = {}
        headers["Accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"
        headers["Accept-Encoding"] = "gzip, deflate, br"
        headers["Accept-Language"] = "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"
        headers["Cache-Control"] = "max-age=0"
        headers["Connection"] = "keep-alive"
        headers["Host"] = "www.ccdi.gov.cn"
        print("审查调查")
        logger.warning("审查调查")
        '''
        # for idx, url in enumerate(start_urls):
        #     next_url = url
        #     has_data = False
        #     max_request = 10
        #     request_iter = 0
        #     while request_iter<max_request:
        #         request_iter+=1
        #         headers['User-Agent'] = random.choice(user_agent_list)
        #         try:
        #             response = session.get(next_url, headers=headers, timeout=20)
        #             response.html.render()
     
        #         except Exception as e:
        #             print(e)
        #             sleep(1)
        #             continue
        #         soup = BeautifulSoup(response.content, 'lxml')
       
        #         try:
        #             li_list = soup.find('ul', "list_news_dl fixed")
        #             if li_list is None:
        #                 li_list = soup.find('ul', "list_news_dl2 fixed")
        #                 if li_list is None:
        #                     session = HTMLSession()
        #                     sleep(1)
        #                     continue

        #             li_list = li_list.find_all('li')
        #         except Exception as e:
        #             print(e)
        #             sleep(1)
        #             continue
        #         for li in li_list:
        #             print("="*50)
        #             article_title = li.find("a").text.strip()
        #             article_url = urljoin(response.url, li.find("a").get("href").strip())
        #             article_date = li.find("div", "more")
        #             if article_date == None:
        #                 article_date = li.find("span")
        #             article_date = article_date.text.strip()
        #             print(article_title)
        #             if find_data(article_title):
        #                 has_data = True
        #                 break
                    
        #             info_list = ner_service.predict(article_title)
        #             print("NER results=",info_list)
        #             for info in info_list:
        #                 temp = {"name":info["name"], "position":info["position"], "matchtext":article_title, "fallen_date":article_date, "url":article_url, "fallen_type":idx+1}
        #                 if temp not in scdc_fallen_leaders:
        #                     scdc_fallen_leaders.append(temp)
        #                 # insert_data(info['name'], info['position'], article_title, article_url, idx+1, article_date)
        #         if has_data:
        #             break

        #         next_param = re.findall("createPageHTML\((.*?)\)", str(soup.find('div', 'page')))
        #         if len(next_param) > 0:
        #             temp = next_param[0].replace('\"', '').split(',')
        #             total = int(temp[0].strip())
        #             current = int(temp[1].strip())
        #             if total > current + 1:
        #                 next_url = urljoin(response.url, '{}_{}.{}'.format(temp[2].strip(), current+1, temp[3].strip()))
        #                 continue
        #         break
        '''
    return scdc_fallen_leaders

# def my_Request(url="https://www.ccdi.gov.cn/"):
#     if url:
#         headers = {}
#         headers["Accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"
#         headers["Accept-Encoding"] = "gzip, deflate, br"
#         headers["Accept-Language"] = "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"
#         headers["Cache-Control"] = "max-age=0"
#         headers["Connection"] = "keep-alive"
#         headers["Host"] = "www.ccdi.gov.cn"
#         headers["user-agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
#         resp = requests.request("GET", url=url, headers=headers, timeout=20)
#         if resp.status_code==200:
#             resp.encoding = "utf-8"
#             return resp
#         else:
#             return []

# # 要闻
# def get_shouye_fallen(url="https://www.ccdi.gov.cn/"):
#     sy_fallen_leaders = []
#     logger.warning("yaowen info")
#     print("yaowen info")
#     ner_service = NewsNERService()
#     try:
#         resp = my_Request(url=url)
#         if resp:
#             root = etree.HTML(resp.text)
#             topnewscon_ul_list = root.xpath(".//div[@class='topnewscon']/ul")
#             if topnewscon_ul_list:
#                 for ul in topnewscon_ul_list:
#                     li_lst = ul.xpath("./li")
#                     for li in li_lst:
#                         text = "".join(li.xpath(".//text()")).strip()
#                         href = "".join(li.xpath("./a/@href")).strip()
#                         # print(text, " : ", href)
#                         info = ner_service.predict(text)
#                         if info and href:
#                             print("yaowentitle:", text)
#                             next_resp = my_Request(url=href)
#                             if next_resp:
#                                 next_root = etree.HTML(next_resp.text)
#                                 title = "".join(next_root.xpath(".//h2[@class='tit']/text()")).strip()
#                                 fallen_date = "".join(next_root.xpath(".//div[@class='daty_con']/em[@class='e e2']/text()")).strip()
#                                 fallen_date = "".join(re.findall(r".*?(\d{4}-\d{2}-\d{2})", fallen_date))
#                                 new_info = ner_service.predict(title)
#                                 print(title, "\t", new_info)
#                                 if new_info and title and href and fallen_date and not find_data(title):
#                                     temp = {"name":new_info[0].get("name"), "position":new_info[0].get("position"), "matchtext":title, "fallen_date":fallen_date, "url":href, "fallen_type":0}
#                                     if temp not in sy_fallen_leaders:
#                                         sy_fallen_leaders.append(temp)
#                                     insert_data(name=new_info[0].get("name"), position=new_info[0].get("position"), matchtext=title, url=href, fallen_type=0, fallen_date=fallen_date)
#             print(get_time())
#         else:
#             return sy_fallen_leaders
#     except:
#         print(traceback.format_exc())
#         return sy_fallen_leaders


# def text_similarity(item1, item2):
#     url_similarity1 = difflib.SequenceMatcher(None, item1["url"], item2["url"]).ratio()
#     url_similarity2 = difflib.SequenceMatcher(None, item1["url"].rsplit("/",1)[-1], item2["url"].rsplit("/",1)[-1]).ratio()
#     url_similarity = min(url_similarity1, url_similarity2)
#     if url_similarity==1.0:
#         return 1.0
#     if len(item1["matchtext"])>10 and len(item2["matchtext"])>10:
#         if item1["matchtext"][:10]==item2["matchtext"][:10]:
#             return 1.0
#     matchtext_radio = difflib.SequenceMatcher(None, item1["matchtext"], item2["matchtext"]).ratio()
#     return matchtext_radio

# def remove_duplicates(scdc_fallen, sy_fallen, threshold=0.92):
#     whole_fallen_leaders = []
#     whole_fallen_leaders.extend(scdc_fallen)
#     whole_fallen_leaders.extend(sy_fallen)
#     unique_list = []
#     seen = set()
#     for i in range(len(whole_fallen_leaders)):
#         current = whole_fallen_leaders[i]
#         if current["name"] in seen:
#             continue
#         seen.add(current["name"])
#         duplicates = [current]
#         for j in range(i+1, len(whole_fallen_leaders)):
#             if whole_fallen_leaders[j]["name"]==current["name"]:
#                 if text_similarity(current, whole_fallen_leaders[j]) > threshold:
#                     duplicates.append(whole_fallen_leaders[j])
#         unique_list.append(duplicates[0])
#     return unique_list


# def daily_task():
#     # create_index()
#     # update_fallen_leader() 
#     # get_shouye_fallen()   # 获取首页上落马信息
#     # data_clean = fallen_leader_clean()
#     # data_clean.update()
#     return

# def job():
#     hour = random.randint(5,6)
#     minute = random.randint(0,30)
#     second = random.randint(0,59)
#     print("任务启动时间\t{:02d}:{:02d}:{:02d}".format(hour, minute, second))
#     schedule.every().day.at("{:02d}:{:02d}:{:02d}".format(hour, minute, second)).do(daily_task)
#     # weekly_task() 
#     print("任务计划已设置")

if __name__=="__main__":
    try:
        update_fallen_leader()
    except:
        print(traceback.format_exc())
    # job()
    # while True:
    #     schedule.run_pending()
    #     time.sleep(60)