from textwrap import indent
from elasticsearch import helpers, Elasticsearch
from tqdm import tqdm
import Lev<PERSON>htein
import datetime, json
from elasticsearch.helpers import bulk
# python3 es-tool.py delete -name organizations
# python3 es-tool.py index -name organizations -mapping ./maps/organizations-mapping.json
# python3 es-tool.py import -name organizations -data ./maps/organizations.json -type json
import os
os.environ["TOKENIZERS_PARALLELISM"]= "false"
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

def temp():
    file_path = os.path.dirname(__file__)
    with open(os.path.join(os.path.abspath(os.path.join(file_path, "../..")), "configs/es_config.json"),"r", encoding="utf-8") as fp:
            CONFIG_ES = json.load(fp)
    es = Elasticsearch(CONFIG_ES['host'], basic_auth=(CONFIG_ES['user'], CONFIG_ES['passwd']), request_timeout=120, max_retries=10, retry_on_timeout=True)
    people_query = {
        "query": {
            "bool": {
                "should": [
                    {
                        "match_all": {}

                    }],
                "filter": {
                    "range": {
                        "create_time": {
                            "gt": "2024-06-01 00:00:00"
                        }
                    }
                }
            }
        }
    }

    same_name_list = es.search(index="fallen-leader-clean", body=people_query, size=10000)#(index='fallen-leader-clean', body=people_query, )
    hits = same_name_list["hits"]["hits"]
    print(len(hits))
    with open("temp.json", "w", encoding="utf-8") as file:
        file.write(json.dumps(hits, ensure_ascii=False, indent=2))




import Levenshtein

def calculate_similarity(str1, str2):
    # 计算Levenshtein距离
    distance = Levenshtein.distance(str1, str2)
    
    # 计算最大可能的距离
    max_distance = max(len(str1), len(str2))
    
    if max_distance == 0:
        return 1.0  # 如果两个字符串都是空的，则相似度为100%
    
    # 计算相似度
    similarity = (max_distance - distance) / max_distance
    
    return similarity

# 示例字符串
str1 = "黑龙江省哈尔滨市政协原常委、文化文史和学习委员会原主任",
str2 = "黑龙江省哈尔滨市政协原常委"

similarity_score = calculate_similarity(str1, str2)
print(f"Similarity between '{str1}' and '{str2}': {similarity_score:.2f}")