from ltp import LTP
import hanlp
class NewsNERService:
    def __init__(self):
        #ltp = LTP(path='/model_file/ltp/base1.tgz')
        self.ltp = LTP()
        

    def deep_find(self, dep, start):
        res = []
        for ent in dep:
            if ent[2] == "ATT" and ent[1] == start:
                res += self.deep_find(dep, ent[0])
                res.append(ent[0])
        return res

    def predict(self, sent):
        seg, hidden = self.ltp.seg([sent])
        seg = seg[0]
        ner = self.ltp.ner(hidden)[0]
        # dep = self.ltp.dep(hidden)[0]

        res = []
        pre_name_end = 0
        for i in range(len(ner)):
            if ner[i][0] == "Nh":
                tag, start, end = ner[i]
                name = seg[start]
                # tt = self.deep_find(dep, start)
                # position = ''.join([seg[i] for i in tt])
                position = ''.join(seg[pre_name_end:start])
                if len(seg) > end+1 and seg[end+1] in ['、', '和']:
                    pre_name_end = end + 2
                else:
                    pre_name_end = end + 1
                res.append({"name": name, "position": position})
        return res


class HanLPNERService:
    def __init__(self):
        self.model = hanlp.load(hanlp.pretrained.mtl.CLOSE_TOK_POS_NER_SRL_DEP_SDP_CON_ELECTRA_SMALL_ZH, conll=0, verbose=True)
    
    def predict(self, sent):
        hanlp_result = self.model(sent, task=["tok/fine", "ner/msra"])
        name_ls = []
        # print(hanlp_result)
        if isinstance(sent, list):
            sent = sent[0]
        if hanlp_result:
            #for item in hanlp_result.get("ner/msra"):
            item = hanlp_result.get("ner/msra")
            if not item:
                return name_ls
            # print(item)
            first_element = "".join([element[0] for element in item if 'PERSON' in element])
            if first_element:
                name = first_element
                st_idx = sent.find(name)
                if st_idx:
                    info = {"name":name, "position": sent[:st_idx]}
                    if info not in name_ls:
                        name_ls.append(info)
        return name_ls
if __name__=="__main__":
    # ner = NewsNERService()
    title = "十四届全国政协人口资源环境委员会副主任李微微接受中央纪委国家监委纪律审查和监察调查"
    title = "江西省政协原党组成员、副主席胡强严重违纪违法被开除党籍"
    # title = "中国农业发展银行湖南省分行原副行长张松柏接受审查调查"
    ner = HanLPNERService()
    res = ner.predict(title)
    print(res)
    position = res[0]["position"]
    print(position)
