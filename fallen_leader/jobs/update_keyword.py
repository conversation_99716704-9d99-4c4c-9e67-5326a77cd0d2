import os
import pymongo
from pymongo import MongoClient
from elasticsearch import Elasticsearch
from datetime import datetime
from bson import ObjectId

BASE_DIR = os.path.dirname(os.path.abspath(__file__))

es = Elasticsearch(["127.0.0.1"], http_auth=('elastic', 'gz123@people!@#'), port=int(9203))
# mongo = MongoClient(host="127.0.0.1").pdetect
import urllib
mongo = MongoClient('mongodb://{}:{}@{}:{}/'.format("admin", urllib.parse.quote("gz123@people!@#"), "127.0.0.1", 27018)).pdetect

def get_time():
    return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def has_keyword(keyword):
    __index_name__ = 'pd-kw'
    body = {
        "query": {
            "match_phrase": { 
                "keyword": keyword
            }
        }
    }
    res = es.search(index=__index_name__, body=body)
    total = res['hits']['total']['value']
    if total == 0:
        return False
    elif res['hits']['hits'][0]['_source']['keyword'] == keyword:
        return True
    return False

class PDKeywordResult:
    collection = mongo.PDKeywordResult

    def __init__(self, keyword, count, detect_count, dataId=None, is_included=False, create_time=None, update_time=None):
        self.keyword = keyword
        self.count = count
        self.detect_count = detect_count
        self.dataId = dataId
        self.is_included = is_included
        self.create_time = create_time if create_time else get_time()
        self.update_time = update_time if update_time else get_time()
        
    @classmethod
    def get(cls, dataId:str):
        res = cls.collection.find_one({"_id": ObjectId(dataId)})
        if res:
            return PDKeywordResult(keyword=res['keyword'], 
                                  count=res['count'], 
                                  detect_count=res['detect_count'], 
                                  dataId=dataId, 
                                  is_included=res['is_included'],
                                  create_time=res['create_time'], 
                                  update_time=res['update_time'])
        return None
    
    @classmethod
    def get_all(cls, page_num=1, page_size=10):
        pr_list = []
        res = cls.collection.find({}, {'keyword':1, 'count':1, 'detect_count':1, 'is_included': 1, 'create_time':1, 'update_time':1}).sort("detect_count", pymongo.DESCENDING).limit(page_size).skip(page_size*(page_num-1))
        for pr in res:
            pr_list.append(PDKeywordResult(keyword=pr['keyword'], 
                                  count=pr['count'], 
                                  detect_count=pr['detect_count'], 
                                  dataId=pr['_id'], 
                                  is_included=pr['is_included'],
                                  create_time=pr['create_time'], 
                                  update_time=pr['update_time']))
        return cls.collection.count_documents({}), pr_list

    @classmethod
    def get_by_keyword(cls, keyword):
        res = cls.collection.find_one({"keyword":keyword})
        if res:
            return PDKeywordResult(keyword=res['keyword'], 
                                  count=res['count'], 
                                  detect_count=res['detect_count'], 
                                  dataId=res["_id"], 
                                  is_included=res['is_included'],
                                  create_time=res['create_time'], 
                                  update_time=res['update_time'])
        return None
    

    @classmethod
    def insert(cls, pd_keyword_rsult):
        doc = {
            "keyword": pd_keyword_rsult.keyword, 
            "count": pd_keyword_rsult.count,
            "detect_count": pd_keyword_rsult.detect_count,
            "is_included": has_keyword(pd_keyword_rsult.keyword),
            "create_time":pd_keyword_rsult.create_time, 
            "update_time": pd_keyword_rsult.update_time
        }
        res = cls.collection.insert_one(doc)
        pd_keyword_rsult.dataId = res.inserted_id
        return res.inserted_id

    @classmethod
    def update(cls, dataId, detect_count, count, is_included=False):
        doc = {
            "detect_count": detect_count, 
            "count": count, 
            "is_included": is_included,
            "update_time": get_time()
        }
        res = cls.collection.update_one({"_id": ObjectId(dataId)}, {"$set": doc})
        if res.matched_count:
            return True
        return False

    @classmethod
    def update_by_keyword(cls, keyword, detect_count, count):
        res = cls.get_by_keyword(keyword)
        if res:
            cls.update(res.dataId, res.detect_count + detect_count, count, res.is_included)
        else:
            cls.insert(PDKeywordResult(keyword, count, detect_count))

def update_keyword():
    # 分页读取包含检测类型2的结果
    collection = mongo.result
    page_size = 10
    page_num = 1
    keyword_result = {}
    count_result = {}
    last_id = None
    while True:
        if os.path.exists(BASE_DIR + '/update_time'):
            with open(BASE_DIR + '/update_time', 'r') as f:
                dummy_id = ObjectId(f.read())
        else:
            dummy_id = ObjectId.from_datetime(datetime(2000, 1, 1))
            with open(BASE_DIR + '/update_time', 'w') as f:
                f.write(str(dummy_id))
        res = collection.find({"_id": {"$gt": dummy_id}}, {'_id':1, 'detect_type':1, 'data':1}).limit(page_size).skip(page_size*(page_num-1))
        if res.count(True) == 0:
            break;
        for r in res:
            last_id = str(r['_id'])
            if r['detect_type'] & 0x02 == 0x02:
                if not r['data']:
                    continue
                for sec in r['data']:
                    for data in sec['result']:
                        if data['type'] == 2:
                            for info in data['hitinfos']:
                                keyword = info['hitseg']
                                count = info['hitnum']
                                if keyword not in keyword_result:
                                    keyword_result[keyword] = 1
                                else:
                                    keyword_result[keyword] += 1
                                count_result[keyword] = count
        if res.count(True) < page_size:
            break
        page_num += 1

    # 统计结果中的hitseg和hitnum
    if last_id:
        for keyword in keyword_result.keys():
            PDKeywordResult.update_by_keyword(keyword, keyword_result[keyword], count_result[keyword])
        with open(BASE_DIR + '/update_time', 'w') as f:
            f.write(str(last_id))
    
    print("{}, 更新{}条分析结果".format(datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S"), len(keyword_result)))

if __name__ == "__main__":
    update_keyword()
