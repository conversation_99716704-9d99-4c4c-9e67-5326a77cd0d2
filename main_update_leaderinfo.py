import os
import traceback
os.environ['OPENBLAS_NUM_THREADS']='1'
import schedule, random, os, re
import time
from datetime import datetime, timedelta
from ops.fill_information import FillLeaderInformation
# from ops.esops import get_date_from_ES
from utils import write_to_txt, write_to_json
from server.check_per import Check_by_Sever
from ops.further_filter import Leader_Info_Filter

os.chdir(os.path.abspath(os.path.dirname(__file__)))

# from apscheduler.schedulers.blocking import BlockingScheduler

def generate_random_time():
    # 生成一个随机的小时、分钟和秒数
    hour = random.randint(11, 12)
    minute = random.randint(45, 51)
    second = random.randint(0, 59)

    # 创建一个基准时间对象，表示今天的日期
    now = datetime.now()

    # 创建一个新的时间对象，并将随机生成的小时、分钟和秒数设置进去
    random_time = now.replace(hour=hour, minute=minute, second=second)

    # 格式化时间为 "hh:mm:ss" 格式
    formatted_time = random_time.strftime("%H:%M:%S")

    return formatted_time

# 补充职务信息到现有es数据库, 每周补充一次
def weekly_task():
    from ops.fill_information import execute_fill_info
    execute_fill_info()
    return


# 从百科获取时政人物信息
def daily_task():
    from runner import UpdateLeaderInfo

    try:
        runner = UpdateLeaderInfo('/home/<USER>/leader-info/UpdateLeaderInfo/configs/config.yaml', use_cache=True)
        runner.update_main()
    except Exception as errmsg:
        print(errmsg)
    '''
    # # 补充当前领导人的url信息
    # current_day = datetime.today().strftime("%Y-%m-%d")
    # # runningfile = os.path.join(os.path.join(os.path.abspath(os.path.dirname(__file__)), "ops/fill_info_log"), "leader-info-{}.txt".format(re.sub("-", "", current_day)))
    # runningfile = os.path.join(os.path.join(os.path.abspath(os.path.dirname(__file__)), "runtime/{}".format(datetime.today().strftime("%Y%m%d"))), "leader_details.txt".format(re.sub("-", "", current_day)))
    # fillinformation=FillLeaderInformation(runningfile=runningfile)
    # # fillinformation.fill_position_url_info(start_time="{} 00:00:00".format(current_day), end_time = "{} 23:59:59".format(current_day))
    # fillinformation.fill_position_url_info(start_time="2020-01-01 00:00:00", end_time="{} 23:59:59".format(current_day))

    ## 增加人员过滤，将落马官员从时政人物中过滤掉,用glm服务
    # try:
    #     time.sleep(20)
    #     server = Check_by_Sever()
    #     server.running()
    # except:
    #     print(traceback.format_exc())
    '''
    
    ## 用的传统字符串判断
    time.sleep(5)
    try:
        leader_filter = Leader_Info_Filter()
        leader_filter.del_fallenleader_from_leaderinfo()
    except Exception as errmsg:
        print(errmsg)
        
    '''
    # 将时政人物信息写入txt文件
    print("url、json信息写入txt和json文件")
    write_to_txt(fillinformation.es, index_name=fillinformation.leader_info_indexname)
    write_to_json(fillinformation.es, fallen_index_name=fillinformation.fallen_leader_cv_indexname)
    '''

    """结果保存在name_validate_res文件夹下，按日期保存"""
    time.sleep(20)
    from utils import validate_and_filter_data
    validate_and_filter_data() 

    print("程序已经全部执行完")
    print("="*60)
    


def schedule_jobs():
    hour = random.randint(9,10)
    minute = random.randint(30,40)
    second = random.randint(0,59)
    print("{:02d}:{:02d}:{:02d}".format(hour, minute, second))
    schedule.every().day.at("{:02d}:{:02d}:{:02d}".format(hour, minute, second)).do(daily_task)
    print("任务计划已设置")



if __name__ == '__main__':
    # weekly_task()
    daily_task()
    # schedule_jobs()
    # while True:
    #     schedule.run_pending()
    #     time.sleep(1)

