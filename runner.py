
#from gc import callbacks
import os
import json
import re
import traceback
from urllib.parse import quote_plus
import requests

#import parser
import parsers
from logger import get_logger
from spiders import <PERSON><PERSON><PERSON><PERSON>  #, MPSpiderGlobalAgent, BaiduRequestor
from utils import get_workdir, get_search_text_from_url #, get_ips
# from ops import OrgList, NewLeaderInfo, OldLeaderInfo, DfLeaderInfo, new_leader_info, old_leader_info, update_ES_leader_info, MergeLeaderInfo
from ops.new_leader_info import NewLeaderInfo
from ops import OrgList, OldLeaderInfo, DfLeaderInfo,  MergeLeaderInfo  #, old_leader_info
import ops.new_leader_info
import ops.update_ES_leader_info
from ops.compare_leader_info import compare_new_old_leader_info    #  , write_to_sql
import logging
from ip_proxy.ip_pool_client import main_ip_monitor


class UpdateLeaderInfo(object):
    def __init__(self, config_file, use_cache=True) -> None:
        self.use_cache = use_cache
        self.workdir = get_workdir()   # 这里已经根据日期创建了runtime文件夹
        # self.workdir = "/home/<USER>/leader-info/UpdateLeaderInfo/runtime/20250417"
        self.logger = get_logger(log_dir=os.path.join(self.workdir,'logs'))
        self.logger.setLevel(logging.DEBUG)
        snapshot_dir = os.path.join(self.workdir, 'snapshot')
        self.spider = MPSpider(config_file, logger=self.logger, snapshot_dir=snapshot_dir)

        self.org_list_op = OrgList(self.spider)
        self.old_leader_info_op = OldLeaderInfo()
        self.df_leader_info_op = DfLeaderInfo()    #self.spider
        self.merge_leader_info_op = MergeLeaderInfo()
        self.new_leader_info_op = NewLeaderInfo()  #self.spider

        '''设置runner的log'''
        self.runnerlogger = logging.getLogger("runner_log")
        self.runnerlogger.setLevel(logging.DEBUG)
        # 设置file处理器
        print(os.path.join(self.workdir, "logs"))
        if not os.path.exists(os.path.join(self.workdir, "logs")):
            os.mkdir(os.path.join(self.workdir, "logs"))
        filehandler = logging.FileHandler(filename=os.path.join(self.workdir, "logs/runner.log"), mode="a", encoding="utf-8")
        filehandler.setLevel(logging.DEBUG)
        formater = logging.Formatter("%(levelname)s|%(asctime)s|%(threadName)s|%(filename)s:%(lineno)d|%(message)s")
        filehandler.setFormatter(formater)
        self.runnerlogger.addHandler(filehandler)
        # 设置console处理器
        streamhandler = logging.StreamHandler()
        streamhandler.setLevel(logging.DEBUG)
        streamhandler.setFormatter(formater)
        self.runnerlogger.addHandler(streamhandler)

        config_filename = os.path.join(os.path.dirname(__file__),'configs/configall.json')
        with open(config_filename, "r", encoding="utf-8") as fp:
            CONFIG = json.load(fp=fp)
            self.requests_host = CONFIG.get("requests_host").get("value")   # 请求百科网页的url，request的url通过body={'url':url}的方式输入给host
            self.http_organizations = CONFIG.get("http_organizations").get("value")      # 获取es的机构链接

    def update_main(self):
        # 获取机构列表 org_list
        self.logger.info('step1: 获取机构列表')
        self.runnerlogger.info('step1: 获取机构列表：')
        try:
            org_list = self.get_org_list()
            self.runnerlogger.info(f'获取省部级机构:{len(org_list)}')
        except:
            self.logger.error("获取省部级机构失败！")
            self.runnerlogger.error("获取省部级机构失败！")
            self.runnerlogger.error(traceback.format_exc())
            return 
        

        self.logger.info('step2: 获取地方机构列表')
        self.runnerlogger.info('step2: 获取地方机构列表：')
        try:
            df_org_list = self.get_df_org_list()
            count_df_org = 0
            for item in df_org_list['data']['list']:
                if 'childIds' in item:
                    count_df_org += len(item['childIds'])
            self.logger.info(r"获取市级机构:{}".format(count_df_org))
            self.runnerlogger.info("获取市级机构：{}".format(count_df_org))
        except:
            self.logger.error(traceback.format_exc())
            self.logger.error("获取地方机构列表失败")
            self.runnerlogger.error("获取市级机构失败！")
            self.runnerlogger.error(traceback.format_exc())
            return
        
        # 获取现有领导人信息,从es读取数据old_leader_info
        self.logger.info('step3: 获取ES现有领导人信息')
        self.runnerlogger.info('step3: 获取ES现有领导人信息：')
        try:
            old_leader_info = self.get_old_leader_info()
            self.logger.info("当前ES存储领导人信息条目:{}".format(len(old_leader_info)))
            # cmdinfo_file.write(f'step3: 获取现有领导人信息：共 {len(old_leader_info)} 个\n')
        except:
            self.logger.error(traceback.format_exc())
            self.runnerlogger.error("获取ES存储领导人失败！")
            self.runnerlogger.exception(traceback.format_exc())
            
        
        # 获取新领导人信息
        self.logger.info('step4: 获取省部级新领导人信息')
        self.runnerlogger.info('step4: 获取省部级新领导人信息：')
        try:
            new_leader_info = self.get_new_leader_info(org_list)  # NewLeaderInfo.log显示url成功和失败的数目，parser_shengbu_result.json包含每个机构的解析结果
            self.logger.info(f'获取省部级新领导人信息:{len(new_leader_info)}')
            self.runnerlogger.info(f'获取省部级新领导人信息:{len(new_leader_info)}')
        except:
            self.logger.error(traceback.format_exc())
            self.runnerlogger.error("解析省部级领导人信息失败！")
            self.runnerlogger.error(traceback.format_exc())
            return
        
        # 获取新地方领导人信息
        self.logger.info('step5: 获取新地方领导人信息')
        self.runnerlogger.info("step5: 获取新地方领导人信息")
        try:
            df_leader_info = self.get_df_leader_info(df_org_list)
            self.logger.info(f'获取新地方领导人信息:{len(df_leader_info)}')
            self.runnerlogger.info(f'获取新地方领导人信息:{len(df_leader_info)}')
        except:
            self.logger.error(traceback.format_exc())
            self.runnerlogger.error("解析地方领导人信息失败！")
            self.runnerlogger.error(traceback.format_exc())
            return

              
        # 比较新旧领导人信息，并得到需要更新的结果 （差集）
        self.logger.info('step6: 比较新旧领导人信息，并得到需要更新的结果')
        self.runnerlogger.info('step6: 比较新旧领导人信息，并得到需要更新的结果：')
        try:
            leader_info_to_be_updated = self.compare_leader_info(new_leader_info, old_leader_info, org_list)
            self.runnerlogger.info(f'step6: 比较新旧领导人信息，并得到需要更新的结果：{len(leader_info_to_be_updated)}')
        except:
            self.runnerlogger.error("比较新旧领导人信息失败！")
            self.runnerlogger.error(traceback.format_exc())
            self.logger.error(traceback.format_exc())
            return

        # 合并新旧领导人信息 （并集）
        self.logger.info('step7: 合并新旧领导人信息')
        self.runnerlogger.info('step7: 合并新旧领导人信息')
        try:
            merged_leader_file = self.merge_leader_info(new_leader_info, df_leader_info, leader_info_to_be_updated)
            with open(merged_leader_file, "r", encoding="utf-8") as fp:
                merged_leader_data = []
                lines = fp.readlines()
                for line in lines:
                    merged_leader_data.append(json.dumps(line,ensure_ascii=False))
                self.runnerlogger.info("合成后的领导人信息：{}".format(len(merged_leader_data)))
        except:
            self.runnerlogger.error("合成失败！")
            self.runnerlogger.error(traceback.format_exc())
            self.logger.error(traceback.format_exc())
            return

        # 更新本地库
        self.logger.info('step8: 更新本地库')
        self.runnerlogger.info(f'step8: 更新本地库\n')
        try:
            self.update_leader_info(merged_leader_file, leader_info_to_be_updated)
        except:
            self.runnerlogger.error("更新失败！")
            self.runnerlogger.error(traceback.format_exc())
            self.logger.error(traceback.format_exc())
            return
        


    def update_leader_info(self, leader_info_file, leader_info_to_be_updated):
        """更新领导人信息"""
        resp = requests.get(self.http_organizations)
        org_dict = resp.json()
        self.logger.info(leader_info_file)
        # update_ES_leader_info.update_leader_info(leader_info_file, leader_info_to_be_updated, org_dict=org_dict)
        ops.update_ES_leader_info.main2(work_dir=self.workdir, org_dict=org_dict)

    def get_org_list(self):
        '''从configs下获取机构列表'''
        org_file_path = os.path.join(os.path.abspath(os.path.dirname(__file__)), "configs")
        cache_file = os.path.join(org_file_path, 'org_list.json')
        self.logger.info(f'cache file: {cache_file}')
        if self.use_cache and os.path.exists(cache_file):
            with open(cache_file) as f:
                data = json.load(f)
        else:
            data = self.org_list_op.get_org_list()
            with open(cache_file, 'w') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        self.logger.info(f"获取省部级机构org_list：{len(data)}")
        return data
    
    def get_df_org_list(self):
        '''从congfigs文件夹获取市级机构'''
        df_org_file_path = os.path.join(os.path.abspath(os.path.dirname(__file__)), "configs")
        cache_file = os.path.join(df_org_file_path, 'df_org_list.json')
        self.logger.info(f'cache file: {cache_file}')
        if self.use_cache and os.path.exists(cache_file):
            with open(cache_file) as f:
                data = json.load(f)
        else:
            data = self.org_list_op.get_df_org_list()
            with open(cache_file, 'w') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        self.logger.info(f"获取市名信息df_org_list：{len(data)}")
        return data
    
    def get_old_leader_info(self):
        '''从es读取数据，获取目前index中的全部信息'''
        cache_file = os.path.join(self.workdir, 'old_leader_info.json')
        self.logger.info(f'cache file: {cache_file}')
        if self.use_cache and os.path.exists(cache_file):
            with open(cache_file) as f:
                data = json.load(f)
        else:
            data = self.old_leader_info_op.get_old_leader_info()
            with open(cache_file, 'w') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        self.logger.info(f"获取old_leader_info：{len(data)}")
        return data
    
    def get_new_leader_info(self, org_list):
        '''获取中央、部级、央国企、省级的领导人信息'''
        cache_file = os.path.join(self.workdir, 'new_shengbu_leader_info.json')
        self.logger.info(f'cache file: {cache_file}')
        if self.use_cache and os.path.exists(cache_file):
            data = self.new_leader_info_op.get_new_leader_info(org_list) 
            with open(cache_file) as f:
                data = json.load(f)
        else:
            try:
                # data = self.new_leader_info_op.get_new_leader_info(org_list)
                data = self.new_leader_info_op.get_new_leader_info2(org_list) #这里只请求机构主页，不采集个人信息   
                #self.new_leader_info_op.get_new_leader_info2是为了测试日志写入设置的
                with open(cache_file, 'w') as f: #将新解析的数据写入new_shengbu_leader_info.json
                    json.dump(data, f, ensure_ascii=False, indent=2)
            except Exception as errmsg:
                print(errmsg)
        self.logger.info(f"获取new_leader_info：{len(data)}")
        return data
    
    def get_df_leader_info(self, df_org_list):
        '''获取省、市级领导人信息'''
        cache_file = os.path.join(self.workdir, 'df_leader_info.json')
        self.logger.info(f'cache file: {cache_file}')
        if self.use_cache and os.path.exists(cache_file):
            with open(cache_file) as f:
                data = json.load(f)
                add_url_data = data
        else:
            data = self.df_leader_info_op.get_df_leader_data(df_org_list)
            add_url_data = data
            if os.path.exists(os.path.join(self.workdir, "old_leader_info.json")):
                with open(os.path.join(self.workdir, "old_leader_info.json"), "r", encoding="utf-8") as fp:
                    old_leader_info = json.load(fp=fp)
                for idx, item in enumerate(data): #从原数据中捞地方领导人的url
                    old_items = [old_leader for old_leader in old_leader_info if item["name"]==old_leader["name"] and old_leader["parent"]=="地方" and old_leader["city"]==item["city"]]
                    if old_items:
                        url = old_items[0].get("url", "")
                        add_url_data[idx]["url"]=url

            with open(cache_file, 'w') as f:
                json.dump(add_url_data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"获取df_leader_info：{len(add_url_data)}")
        return add_url_data

    def collect_leader_info_to_be_updated(self, leader_desc):
        def _collect_info(leader_info):
            '''根据html的内容，返回info = {"name":name, "url":url, "title":title, "pre_title": pre_title, "shencha":shencha}'''
            name = leader["name"]
            name = re.sub("_百度百科","", name)
            name = re.sub(r"\（.*?\）", "", name)
            url = leader["url"]
            url = re.sub("https://baike.baidu.com", "", url)
            url = re.sub("\?.*", "", url)

            title, pre_title, shencha = leader.get('title', ""), leader.get("pre_title", ""), leader.get("shencha", "")

            info = {"name":name, "url":quote_plus(url), "title":title, "pre_title": pre_title, "shencha":shencha}
            return info

        leaders_to_be_updated = []
        for leader in leader_desc:
            try:
                '''根据html的内容，返回info = {"name":name, "url":url, "title":title, "pre_title": pre_title, "shencha":shencha}'''
                info = _collect_info(leader)
            except:
                self.logger.error(f"入库失败\n{traceback.format_exc()}")
                continue
            leaders_to_be_updated.append(info)

        save_name = os.path.join(self.workdir, "leaders_to_be_updated.json")
        with open(save_name, "w", encoding="utf8") as fp:
            json.dump(leaders_to_be_updated, fp, ensure_ascii=False, indent=2)
            self.logger.info(f"saved {save_name}")

        return leaders_to_be_updated

    def get_leader_desc(self, url_list):
        cache_file = os.path.join(self.workdir, 'leader_desc.json')
        self.logger.info(f'cache file: {cache_file}')
        if self.use_cache and os.path.exists(cache_file):
            res_data = []
            with open(cache_file) as f:
                data = json.load(f)
                return data
        else:
            print("work-dir:", self.workdir)
            data = main_ip_monitor(urls=url_list, runing_path=self.workdir, ip_nums=min(20, len(url_list)), callbacks=[parsers.leader_desc])
            res_data = list()
            for item in data:
                if item['code'] == 0 or item["code"]==200:
                    self.logger.debug(f"url: {item['url']}")
                    self.logger.debug(f"code: {item['code']}")
                    self.logger.debug(f"title: {item['resp']['XianRen']}")
                    self.logger.debug(f"summary: {item['resp']['Summary']}")
                    self.logger.debug(f"description: {item['resp']['Description']}")
                    item['resp']['Url'] = item['url']
                    item['resp']['SearchText'] = get_search_text_from_url(item['url'])
                    res_data.append(item['resp'])
            data = res_data
            with open(cache_file, 'w') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        self.logger.info(f'获取leader_desc: {len(data)}')
        return data

    def merge_leader_info(self, shengbu_leader_info, difang_leader_info, leaders_info_to_be_updated=[]):
        merged_leader_file = os.path.join(self.workdir, 'whole_leader_info.json')
        self.logger.info(f'cache file: {merged_leader_file}')
        if self.use_cache and os.path.exists(merged_leader_file):
            return merged_leader_file
        else:
            merged_leader_info = self.merge_leader_info_op.merge(shengbu_leader_info, difang_leader_info, leaders_info_to_be_updated)
            self.merge_leader_info_op.dump(merged_leader_info, merged_leader_file)
            return merged_leader_file

    def compare_leader_info(self, new_leader_info, old_leader_info, org_list):
        import traceback
        try:
            to_be_update_leaders = compare_new_old_leader_info(new_leader_info, old_leader_info, org_list, self.logger, self.workdir)
            leader_desc = self.get_leader_desc(to_be_update_leaders)  # 这里会获取到人物的详细职务信息
            leader_info_to_be_updated = leader_desc
            count = 0
            for ii in leader_info_to_be_updated:
                if ii["XianRen"]:
                    count+=1
            print("\t有现任职务信息的领导：", count)
        except:
            print(traceback.format_exc())
            return []
        return leader_info_to_be_updated


if __name__=="__main__":
    update_leader_info = UpdateLeaderInfo('/home/<USER>/leader-info/UpdateLeaderInfo/configs/config.yaml', use_cache=True)
    update_leader_info.update_main()
