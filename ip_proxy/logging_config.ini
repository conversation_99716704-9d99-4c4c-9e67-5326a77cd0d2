[loggers]
keys=root,IPPoolMonitor

[handlers]
keys=console<PERSON><PERSON><PERSON>,fileHandler

[formatters]
keys=consoleFormatter,fileFormatter

[logger_root]
level=DEBUG
handlers=consoleHandler,fileHandler

[logger_IPPoolMonitor]
level=DEBUG
handlers=consoleHandler,fileHandler
qualname=IPPoolMonitor
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=DEBUG
formatter=consoleFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=FileHandler
level=DEBUG
formatter=fileFormatter
args=('ip_pool_monitor.log', 'a')

[formatter_consoleFormatter]
class=logging.Formatter
format=%(asctime)s - %(levelname)s - %(message)s
datefmt=%Y-%m-%d %H:%M:%S

[formatter_fileFormatter]
class=logging.Formatter
format=%(asctime)s - %(levelname)s - %(message)s
datefmt=%Y-%m-%d %H:%M:%S