
import asyncio
import aiohttp
import time
from datetime import datetime, timedelta
import logging.config
import sys
import json
import os
import random
import logging



class IPProxyPool:
    # def __init__(self, runing_path, snapshot_path="", proxy_url="http://api.tianqiip.com/getip?secret=sd5kt44eb54q0cp3&num={}&type=json&port=1&time=3&mr=1&sign=902b7418b60dd09b741b0d45475768d5", ip_num=10, log_path="logs"):
    def __init__(self, runing_path, snapshot_path="", proxy_url="http://api.tianqiip.com/getip?secret=sd5kt44eb54q0cp3&num={}&type=json&port=1&time=3&mr=1&sign=902b7418b60dd09b741b0d45475768d5", ip_num=10, log_path="logs"):
    # def __init__(self, runing_path, snapshot_path="", proxy_url="http://api.tianqiip.com/getip?secret=vn0eoekx&num={}&type=json&port=1&mr=1&sign=902b7418b60dd09b741b0d45475768d5", ip_num=3, log_path="logs"):
        # proxy_url = "http://api.tianqiip.com/getip?secret=sd5kt44eb54q0cp3&num={}&type=json&port=1&time=3&mr=1&sign=902b7418b60dd09b741b0d45475768d5"
        self.proxy_url = proxy_url
        self.ip_num = ip_num
        self.ip_port = []
        self.available = {}  # 记录代理的可用状态
        if not os.path.exists(os.path.join(runing_path, log_path)):
            os.system("mkdir -p {}".format(os.path.join(runing_path, log_path)))
        # print("ip_proxy_pool.log:", os.path.join(runing_path, log_path))
        self.logger = self.update_log_file(log_path=os.path.join(runing_path, log_path), log_file="ip_proxy_pool.log")
        self.refresh_proxies(needed_proxies=ip_num)
        self.runing_path = runing_path
        
    def update_log_file(self, log_path, log_file):
        # 创建和更新 ippoolmonitor 日志
        logger_name = f"IPPoolMonitor_{id(self)}"  # 使用实例ID确保唯一性
        newlogger = logging.getLogger(logger_name)

        # 检查是否已经配置过handler，避免重复添加
        if newlogger.handlers:
            return newlogger

        newlogger.setLevel(logging.DEBUG)

        # 添加文件处理器
        filehandler = logging.FileHandler(filename=os.path.join(log_path, log_file), mode="a", encoding="utf-8")
        print("ip_proxy_pool.log:", os.path.join(log_path, log_file))
        filehandler.setLevel(logging.DEBUG)
        formater = logging.Formatter("%(levelname)s | %(asctime)s | %(filename)s:%(lineno)d | %(message)s")
        filehandler.setFormatter(formater)
        newlogger.addHandler(filehandler)

        # 设置console处理器（可选，如果不需要控制台输出可以注释掉）
        # streamhandler = logging.StreamHandler()
        # streamhandler.setLevel(logging.DEBUG)
        # streamhandler.setFormatter(formater)
        # newlogger.addHandler(streamhandler)

        # 防止日志向上传播到root logger
        newlogger.propagate = False

        return newlogger


    async def fetch_proxies(self, session, ip_num):
        # 获取ip代理池，并分配每个ip的获取和失效时间，存入self.ip_port
        # 构建带有 ip_num 参数的 URL
        url_with_params = self.proxy_url.format(ip_num)
        async with session.get(url_with_params) as response:
            if response.status == 200:
                proxies = await response.text()
                proxies_data = proxies.split('\r\n')
                # proxies_data = json.loads(proxies)#proxies.split(',')  # 假设代理以逗号分隔
                if isinstance(proxies_data, list):
                    proxies_list = proxies_data[0]
                if isinstance(proxies_data, dict):
                    proxies_list = json.loads(proxies)#proxies_data["data"]
                if isinstance(proxies_list, str):
                    proxies_list = json.loads(proxies_list)
                    proxies_list = proxies_list["data"]
                for proxy in proxies_list:
                    if isinstance(proxy, str):
                        proxy = json.loads(proxy)
                    ip_port = "{}:{}".format(proxy.get("ip", ""),proxy.get("port", ""))
                    self.ip_port.append({
                        'ip': ip_port,
                        'available': 'available',  # 'available' 或 'using'
                        'expires_at': datetime.now() + timedelta(minutes=2)  # 假设默认有效期为3分钟
                    })
                    self.available[ip_port] = 'available'
                self.logger.info(f"Refreshed {len(proxies_list)} proxies.")
            else:
                self.logger.warning("Failed to fetch proxies.")

    def check_and_update_proxies(self):
        # 检查代理是否失效，并更新 ip_port 列表
        current_time = datetime.now()
        proxies_to_remove = [proxy['ip'] for proxy in self.ip_port if proxy['expires_at'] < current_time or proxy['available'] == 'invalid']
        for proxy_ip in proxies_to_remove:
            for item in self.ip_port:
                if item["ip"]==proxy_ip:
                    self.ip_port.remove(item)
                    
            # self.ip_port.remove({'ip': proxy_ip, 'available': self.available[proxy_ip]})
            del self.available[proxy_ip]
            self.logger.info(f"Removed expired proxy: {proxy_ip}")

        # 如果 ip_port 中的元素少于 5 个，则重新获取足够的 IP 补充到 10 个
        if len(self.ip_port) < 5:
            needed_proxies = max(0, 10 - len(self.ip_port))  # 计算需要补充的代理数量
            self.refresh_proxies(needed_proxies)

    def refresh_proxies(self, needed_proxies=None):
        if needed_proxies is None:
            needed_proxies = self.ip_num
        async def refresh():
            async with aiohttp.ClientSession() as session:
                await self.fetch_proxies(session, needed_proxies)
        asyncio.run(refresh())

    def get_available_proxy(self):
        # 获取一个可用的代理 IP
        self.check_and_update_proxies()  # 确保代理列表是最新的
        available_proxies = [proxy for proxy in self.ip_port if proxy['available'] == 'available']
        if not available_proxies:
            return None
        proxy_to_use = random.choice(available_proxies)#available_proxies[0]
        proxy_to_use['available'] = 'using'
        self.available[proxy_to_use['ip']] = 'using'
        return proxy_to_use['ip']

    def release_proxy(self, proxy_ip):
        # 释放一个代理 IP，使其再次可用
        if proxy_ip in self.available:
            self.available[proxy_ip] = 'available'
            for proxy in self.ip_port:
                if proxy['ip'] == proxy_ip:
                    proxy['available'] = 'available'
                    break


if __name__=="__main__":
    ipproxypool = IPProxyPool(runing_path="/home/<USER>/leader-info/UpdateLeaderInfo/runtime/20250819", snapshot_path="snapshot", ip_num=5)
    ips = ipproxypool.get_available_proxy()
    print(ips)