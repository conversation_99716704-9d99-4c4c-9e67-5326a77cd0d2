# -*- utf-8
import asyncio
import logging
import logging.config
from .IPProxyPool import IPProxyPool
from .myurl_parser import save_snapshots
import json, os, random, time
import requests
from .myurl_parser import save_snapshots, load_snopshots, url_to_filepath
from urllib.parse import quote_plus
import os, sys, re
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from parsers import leader_desc

user_agent_list = [
    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/22.0.1207.1 Safari/537.1",
    "Mozilla/5.0 (X11; CrOS i686 2268.111.0) AppleWebKit/536.11 (KHTML, like Gecko) Chrome/20.0.1132.57 Safari/536.11",
    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.6 (<PERSON><PERSON><PERSON>, like Gecko) Chrome/20.0.1092.0 Safari/536.6",
    "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.6 (KHTML, like Gecko) Chrome/20.0.1090.0 Safari/536.6",
    "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/********** Safari/537.1",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.9 Safari/536.5",
    "Mozilla/5.0 (Windows NT 6.0) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.36 Safari/536.5",
    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3",
    "Mozilla/5.0 (Windows NT 5.1) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_0) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3",
    "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1062.0 Safari/536.3",
    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1062.0 Safari/536.3",
    "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3",
    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3",
    "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3",
    "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.0 Safari/536.3",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.24 (KHTML, like Gecko) Chrome/19.0.1055.1 Safari/535.24",
    "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/535.24 (KHTML, like Gecko) Chrome/19.0.1055.1 Safari/535.24",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
]

def contains_verification(html_content):
    # 使用正则表达式提取<title>和</title>标签之间的内容
    match = re.search(r'<title>(.*?)</title>', html_content)   # <title>百度百科-验证</title>
    if match:
        title = match.group(1)
        if '验证' in title:
            return True
    return False

def main_ip_monitor(urls, runing_path, snapshot_path="snapshot", callbacks=[], ip_nums=10):
    responses = []
    ipproxypool = IPProxyPool(runing_path=runing_path, snapshot_path=snapshot_path, ip_num=ip_nums)
    if urls==[]:
        with open(os.path.join(runing_path, "tobe_updated_url_list.txt"), "r", encoding="utf-8") as fp:
            lines = fp.readlines()
            for line in lines:
                line = line.strip()
                if line not in urls:
                    urls.append(line)

    all_urls = len(urls)
    succeed_count = 0
    failed_count = 0
    load_count = 0
    for idx, url in enumerate(urls):
        path, filename = url_to_filepath(url=url)
        ipproxypool.logger.info("--"*20)
        ipproxypool.logger.info("idx={}, url={}".format(idx, url))
        if not (path and filename):
            ipproxypool.logger.error("url_to_filepath Failed! url={}".format(url))
        else:
            whole_filename = os.path.join(os.path.join(runing_path, snapshot_path), f"{path}/{filename}")
            # print(os.path.join(os.path.join(runing_path, snapshot_path), f"{path}"))
            if os.path.exists(os.path.join(os.path.join(runing_path, snapshot_path), f"{path}")) and os.path.isfile(whole_filename):
                load_response = load_snopshots(url=url, file_name=filename, file_path=path, snapshot_path=snapshot_path, running_path=runing_path)
                info = {
                    "code": 0,
                    "url": url,
                    "proxy": "",
                    "resp": load_response
                }
                responses.append(info)
                ipproxypool.logger.info("Load succeed! {}".format(url))
                load_count+=1
                succeed_count+=1
            else:
                time.sleep(5)
                bool_get_ip = True
                while bool_get_ip:
                    ip = ipproxypool.get_available_proxy()
                    # print({"ip:", ip})
                    if ip:
                        bool_get_ip = False
                    else:
                        time.sleep(5)

                headers = {"user-agent":random.choice(user_agent_list)}
                proxyMeta = "http://{}".format(ip)
                proxydata = {
                    "https": proxyMeta,
                    "http": proxyMeta
                }
                try:
                    import re
                    response = requests.request("GET", url=url, headers=headers, proxies=proxydata, timeout=(5, 10))             
                    if response.status_code==200 and len(response.text)>0:
                        verification = contains_verification(response.text)
                        if verification:
                            ipproxypool.logger.info("验证! {}:{}".format(ip, url))
                            failed_count += 1
                        else:
                            info = {
                                "code": 0,
                                "url": url,
                                "proxy": ip,
                                "resp": response
                            }
                            if info not in responses:
                                responses.append(info)
                            ipproxypool.logger.info("Request Succeed! {}:{}".format(ip, url))
                            save_snapshots(html_content=response.text, file_name=filename, file_path=path, snapshot_path=snapshot_path, running_path=runing_path)
                            succeed_count+=1
                    else:
                        ipproxypool.logger.warning("Request Failed! {}:{}".format(ip, url))
                        failed_count+=1
                    ipproxypool.release_proxy(ip)
                    ipproxypool.logger.warning("Release {}!".format(ip))
                except:
                    ipproxypool.logger.error("Request Failed! {}:{}".format(ip, url))
                    ipproxypool.release_proxy(ip)
                    failed_count+=1
    ipproxypool.logger.info("urls={}, load_count={}, succeed_count={}, failed_count={}, percent={:.2f}".format(all_urls, load_count, succeed_count, failed_count, succeed_count/(all_urls+0.0000001)))
    ipproxypool.logger.info("responses={}".format(len(responses)))
    if not callbacks:
        return responses
    else:
        func_responses = []
        for func in callbacks:
            for item in responses:
                if item["code"]==0 or item["code"]==200:
                    try:
                        item["resp"] = func(item["resp"])
                        item["code"] = 0
                    except Exception as errmsg:
                        ipproxypool.logger.warning(errmsg)
                        item["code"] = -1
                func_responses.append(item)
        return func_responses
                
    
# if __name__=="__main__":
#     org_list_file = "/home/<USER>/leader-info/UpdateLeaderInfo/configs/org_list.json"
#     with open(org_list_file) as f:
#         org_list = json.load(f)
#     search_data = {f"https://baike.baidu.com/search/word?fromModule=lemma_search-box&lemmaId=&word={quote_plus(org['name'].encode('utf-8'))}": org for org in org_list if org["name"] is not None}
#     search_urls = list(search_data.keys())
#     responses = main_ip_monitor(urls=[], runing_path="/home/<USER>/leader-info/UpdateLeaderInfo/runtime/20241202", snapshot_path="snapshot", callbacks=[leader_desc])