from ast import parse
import os
from typing_extensions import runtime
from urllib.parse import unquote
from urllib.parse import urlparse
from scrapy.http import HtmlResponse

# 用于加载html文件
def load_snopshots(url, file_name, file_path, snapshot_path, running_path):
    abs_html_path = os.path.join(running_path,"{}/{}".format(snapshot_path, file_path))
    # print(os.path.exists(os.path.join(abs_html_path, file_name)))
    if os.path.exists(os.path.join(abs_html_path, file_name)):
        with open(os.path.join(abs_html_path, file_name), "r", encoding="utf-8") as fp:
            html = fp.read()
            if len(html)==0:
                raise("HTML content is empty!")
            response = HtmlResponse(url=url, body=html, encoding="utf-8")
            response.status_code=0
            return response
    else:
        return None
    
# 保存html文件
def save_snapshots(html_content, file_name, file_path, running_path, snapshot_path):
    abs_html_path = os.path.join(running_path, "{}/{}".format(snapshot_path,file_path))
    try:
        if not os.path.exists(abs_html_path):
            os.system(f"mkdir -p {abs_html_path}")
        with open(os.path.join(abs_html_path, file_name), "w", encoding="utf-8") as fp:
            fp.write(html_content)
        return True
    except:
        return False


def url_to_filepath(url):
    '''解析url，将url拆分成路径和文件名,返回path和filename'''
    import urllib.parse
    path, filename = "",""
    if not url:
        return path, filename
    else:
        if url.endswith(".com/") or "g12.guodata.com" in url:
            return path, filename
        if "liuyan.people.cn" in url:
            path = "liuyan.peope.cn"
            filename = "fid"
            return path, filename
        else:
            parsed_url = urllib.parse.urlparse(url)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            # 处理url中是否有word，面向的是机构查询
            if "word" in query_params:
                filename=query_params["word"][0]
                path = "baike.baidu.com/search/word"

            else:
                path_parts = parsed_url.path.split("/")
                if "item" in path_parts:
                    search_item_idx = path_parts.index("item")+1
                    decode_search_item = urllib.parse.unquote(path_parts[search_item_idx])
                    if search_item_idx<len(path_parts):
                        path = "baike.baidu.com/item/{}/{}".format(decode_search_item, path_parts[-1])
                        filename = decode_search_item
                    else:
                        path = "baike.baidu.com/item/{}".format(decode_search_item)
                        filename = decode_search_item
            return path, filename

                